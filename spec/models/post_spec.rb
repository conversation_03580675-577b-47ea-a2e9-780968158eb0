# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Post, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'validations' do
    it { should validate_presence_of(:title) }
    it 'validates status enum values' do
      should define_enum_for(:status)
        .with_values(draft: 'draft', published: 'published')
        .backed_by_column_of_type(:string)
    end
  end

  describe 'instance methods' do
    let(:user) { create(:user) }
    let(:post) { create(:post, user: user, status: 'draft') }

    describe '#publish' do
      it 'changes status from draft to published' do
        expect { post.publish }.to change { post.status }.from('draft').to('published')
      end

      it 'sets published_at timestamp' do
        expect { post.publish }.to change { post.published_at }.from(nil)
      end
    end

    describe '#unpublish' do
      let(:published_post) { create(:post, user: user, status: 'published', published_at: Time.current) }

      it 'changes status from published to draft' do
        expect { published_post.unpublish }.to change { published_post.status }.from('published').to('draft')
      end

      it 'clears published_at timestamp' do
        expect { published_post.unpublish }.to change { published_post.published_at }.to(nil)
      end
    end
  end

  describe 'scopes' do
    let(:user) { create(:user) }
    let!(:draft_post) { create(:post, user: user, status: 'draft') }
    let!(:published_post) { create(:post, user: user, status: 'published') }

    describe '.drafts' do
      it 'returns only draft posts' do
        expect(Post.drafts).to include(draft_post)
        expect(Post.drafts).not_to include(published_post)
      end
    end

    describe '.published' do
      it 'returns only published posts' do
        expect(Post.published).to include(published_post)
        expect(Post.published).not_to include(draft_post)
      end
    end
  end
end
