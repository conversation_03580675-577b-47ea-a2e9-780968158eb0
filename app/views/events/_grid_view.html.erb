<%#
  Events Grid View Partial

  用法：
  render "events/grid_view", events: @events, current_tab: @current_tab

  参数：
  - events: 事件集合
  - current_tab: 当前标签页 ('upcoming', 'past', 'all')
%>

<!-- 网格视图容器 -->
<div data-view-switcher-target="gridContainer" id="events-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 <%= (@current_view == 'grid' || @current_view.blank?) ? '' : 'hidden' %>">
  <% if events.any? %>
    <%= render partial: 'events/event_card', collection: events, as: :event %>
  <% else %>
    <!-- 空状态 -->
    <div class="col-span-full text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
        <% if params[:search].present? %>
          No events found
        <% else %>
          <% case current_tab %>
          <% when 'past' %>
            <%= t('events.index.no_past_events') %>
          <% when 'upcoming' %>
            <%= t('events.index.no_upcoming_events') %>
          <% else %>
            <%= t('events.index.no_events_found') %>
          <% end %>
        <% end %>
      </h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        <% if params[:search].present? %>
          <%= t('events.index.no_events_found_description') %>
        <% else %>
          <% case current_tab %>
          <% when 'past' %>
            <%= t('events.index.no_past_events_description') %>
          <% when 'upcoming' %>
            <%= t('events.index.no_upcoming_events_description') %>
          <% else %>
            <%= t('events.index.no_events_found_description') %>
          <% end %>
        <% end %>
      </p>
      <% if current_tab == 'upcoming' && allowed_to?(:create?, Event) %>
        <div class="mt-6">
          <%= link_to new_event_path,
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            <%= t('events.new.create_event') %>
          <% end %>
        </div>
      <% end %>
    </div>
  <% end %>
</div>
