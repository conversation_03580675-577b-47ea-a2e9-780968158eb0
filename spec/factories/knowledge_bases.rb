FactoryBot.define do
  factory :knowledge_base do
    sequence(:name) { |n| "Knowledge Base #{n}" }
    description { "Test knowledge base description" }
    visibility { :private_visibility }
    association :owner, factory: :user
    metadata { { category: "general" } }

    trait :public_visibility do
      visibility { :public_visibility }
    end

    trait :team_visibility do
      visibility { :team_visibility }

      after(:build) do |kb|
        team = create(:team)
        team_member = create(:team_member, team: team)
        kb.team_resource_accesses.build(
          team_member: team_member,
          resource_kind: 'knowledge_base',
          role: :viewer
        )
      end
    end

    trait :with_posts do
      after(:create) do |kb|
        create_list(:knowledge_base_post, 2, knowledge_base: kb)
      end
    end

    trait :archived do
      archived { true }
    end

    trait :deleted do
      deleted_at { Time.current }
    end
  end
end
