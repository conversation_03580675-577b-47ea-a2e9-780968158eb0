<!-- 面包屑导航组件 -->
<nav class="flex mb-6" aria-label="Breadcrumb">
  <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
    <li class="inline-flex items-center">
      <%= link_to root_path, class: "inline-flex items-center text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-primary-500 transition-colors duration-200" do %>
        <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
        </svg>
        <%= t('shared.breadcrumb.home') %>
      <% end %>
    </li>
    
    <% if defined?(items) && items.any? %>
      <% items.each_with_index do |(text, path), index| %>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <% if index == items.size - 1 || path.nil? %>
              <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500 transition-colors duration-200" aria-current="page"><%= text %></span>
            <% else %>
              <%= link_to text, path, class: "ml-1 text-gray-700 hover:text-primary-600 md:ml-2 dark:text-gray-300 dark:hover:text-primary-500 transition-colors duration-200" %>
            <% end %>
          </div>
        </li>
      <% end %>
    <% end %>
  </ol>
</nav>
