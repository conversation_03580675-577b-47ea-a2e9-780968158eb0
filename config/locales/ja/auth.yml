# 認証およびユーザー管理の日本語翻訳
ja:
  auth:
    sign_in: "ログイン"
    sign_up: "新規登録"
    sign_out: "ログアウト"
    sessions:
      title: "ログイン"
      email: "メールアドレス"
      password: "パスワード"
      remember_me: "ログイン状態を保持"
      forgot_password: "パスワードをお忘れですか？"
      new_account: "アカウントをお持ちでないですか？"
    registrations:
      title: "アカウント作成"
      username: "ユーザー名"
      email: "メールアドレス"
      password: "パスワード"
      password_confirmation: "パスワード確認"
      account_exists: "すでにアカウントをお持ちですか？"
      terms_agreement: "登録することにより、利用規約とプライバシーポリシーに同意したことになります"
      steps:
        verification:
          title: "認証コード送信"
          phone_label: "電話番号"
          phone_placeholder: "電話番号を入力してください"
          code_label: "認証コード"
          code_placeholder: "コードを入力してください"
          send_code: "コード取得"
          next: "次へ"
          have_account: "すでにアカウントをお持ちですか？"
        progress:
          send: "送信"
          code: "コード"
          create: "作成"
          account: "アカウント"
          send_code: "コード送信"
          create_account: "アカウント作成"
          subscribe: "購読"
          step: "ステップ %{number}"
      pricing:
        get_started: "始める"
      placeholders:
        username: "ユーザー名を入力 (英数字とアンダースコアのみ)"
        email: "<EMAIL>"
        password: "••••••••"
    passwords:
      forgot:
        title: "パスワードをお忘れですか？"
        description: "ご心配なく！メールアドレスを入力していただければ、リセットリンクをお送りします！"
        email_label: "メールアドレス"
        email_placeholder: "<EMAIL>"
        submit: "パスワードリセット"
      reset:
        title: "パスワードリセット"
        new_password_label: "新しいパスワード"
        confirm_password_label: "新しいパスワード確認"
        password_placeholder: "••••••••"
        submit: "パスワードリセット"
      terms:
        accept: "同意します"
        link: "利用規約"
      mailer:
        reset:
          html:
            message: "次の15分以内に"
            link_text: "このパスワードリセットページ"
          text:
            message: "次の15分以内にこのパスワードリセットページでパスワードをリセットできます:"
    verification:
      code_sent: "コードを送信しました"
      enter_code: "コードを入力"
      resend_code: "コード再送信"
      verify_button: "認証"
