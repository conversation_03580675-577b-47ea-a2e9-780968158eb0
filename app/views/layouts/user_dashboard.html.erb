<!DOCTYPE html>
<html class="h-full">
  <head>
    <title><%= content_for(:title) || "Ai Pro Cgc" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <%# Removed overflow-hidden from body AGAIN %>
  <%# Ensure body prevents outer scroll, h-screen might be redundant with flex-col %>
  <%# Added data-controller="sidebar preferences" %>
  <body class="bg-gray-50 dark:bg-gray-900 h-screen flex flex-col overflow-hidden" data-controller="sidebar">
    <%= render 'layouts/navbar_dashboard' %>

    <%# This container fills space below navbar and allows internal scrolling if needed %>
    <div class="flex flex-1 pt-16 overflow-hidden bg-gray-50 dark:bg-gray-900">
      <%= render 'shared/sidebar' %>
      <%# Main content column container %>
      <div class="relative w-full flex flex-col lg:ml-64">
        <%# Main area grows and scrolls its content (yield) %>
        <main class="flex-1 overflow-y-auto">
          <%= render 'shared/flash' %>
          <%# Yield content, show.html.erb should NOT use h-full now %>
          <%= yield %>
        </main>
        <%# Footer is outside the scrollable main area %>
        <div class="h-14 shrink-0 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
           <%= render 'layouts/footer_dashboard' %>
        </div>
      </div>
    </div>
  </body>
</html>
