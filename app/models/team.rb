class Team < ApplicationRecord
  # 常量定义
  STATUSES = %w[active inactive].freeze

  # 关联关系
  has_many :team_members, dependent: :destroy
  has_many :members, through: :team_members, source: :user
  has_many :team_resource_accesses, through: :team_members

  # 资源关联
  has_many :knowledge_bases, through: :team_resource_accesses, source: :resource, source_type: "KnowledgeBase"

  # 验证规则
  validates :name, presence: true, uniqueness: { case_sensitive: false }
  validates :status, presence: true, inclusion: { in: STATUSES }

  # 作用域
  scope :active, -> { where(status: "active") }
  scope :inactive, -> { where(status: "inactive") }
  scope :accessible_to, ->(user) {
    return none unless user.present?
    joins(:team_members).where(team_members: { user_id: user.id })
  }

  # 成员相关方法
  def admins
    members.merge(TeamMember.team_admin_role)
  end

  def regular_members
    members.joins(:team_members).where(team_members: { role: "member" })
  end

  def admin?(user)
    team_members.team_admin_role.exists?(user: user)
  end

  def member?(user)
    team_members.exists?(user: user)
  end

  def add_member(user, role = "member")
    team_members.create!(user: user, role: role)
  end

  def remove_member(user)
    team_members.find_by(user: user).destroy
  end

  # 资源访问相关方法
  def knowledge_base_viewers
    knowledge_bases.merge(TeamResourceAccess.where(role: :viewer))
  end

  def knowledge_base_editors
    knowledge_bases.merge(TeamResourceAccess.where(role: :editor))
  end

  def knowledge_base_maintainers
    knowledge_bases.merge(TeamResourceAccess.where(role: :maintainer))
  end

  # 状态相关方法
  def active?
    status == "active"
  end

  def inactive?
    status == "inactive"
  end
end
