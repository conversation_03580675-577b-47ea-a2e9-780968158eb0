# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source <PERSON>s uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_30_022241) do
  create_table "action_text_rich_texts", force: :cascade do |t|
    t.string "name", null: false
    t.text "body"
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["record_type", "record_id", "name"], name: "index_action_text_rich_texts_uniqueness", unique: true
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "assistants", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "name", null: false
    t.text "instructions"
    t.string "tool_choice"
    t.json "tools"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_assistants_on_user_id"
  end

  create_table "base_units", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "name", null: false
    t.string "content_type"
    t.integer "file_size", limit: 8, null: false
    t.string "checksum"
    t.string "status", default: "processing"
    t.datetime "processed_at"
    t.json "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["content_type"], name: "index_base_units_on_content_type"
    t.index ["status"], name: "index_base_units_on_status"
    t.index ["user_id", "name"], name: "index_base_units_on_user_id_and_name", unique: true
    t.index ["user_id"], name: "index_base_units_on_user_id"
  end

  create_table "conversations", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "assistant_id", null: false
    t.string "title", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assistant_id", "created_at"], name: "index_conversations_on_assistant_id_and_created_at"
    t.index ["assistant_id"], name: "index_conversations_on_assistant_id"
    t.index ["user_id", "created_at"], name: "index_conversations_on_user_id_and_created_at"
    t.index ["user_id"], name: "index_conversations_on_user_id"
  end

  create_table "event_categories", force: :cascade do |t|
    t.string "name", null: false
    t.string "slug", null: false
    t.text "description"
    t.string "color"
    t.string "icon"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_event_categories_on_name"
    t.index ["slug"], name: "index_event_categories_on_slug", unique: true
  end

  create_table "event_instructors", force: :cascade do |t|
    t.integer "event_id", null: false
    t.integer "user_id", null: false
    t.string "role", default: "instructor"
    t.text "bio"
    t.string "title"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["event_id", "user_id"], name: "index_event_instructors_on_event_id_and_user_id", unique: true
    t.index ["event_id"], name: "index_event_instructors_on_event_id"
    t.index ["role"], name: "index_event_instructors_on_role"
    t.index ["user_id"], name: "index_event_instructors_on_user_id"
  end

  create_table "event_locations", force: :cascade do |t|
    t.string "name", null: false
    t.string "address"
    t.string "city"
    t.string "province"
    t.string "country"
    t.decimal "latitude", precision: 10, scale: 6
    t.decimal "longitude", precision: 10, scale: 6
    t.text "directions"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["city"], name: "index_event_locations_on_city"
    t.index ["latitude", "longitude"], name: "index_event_locations_on_latitude_and_longitude"
  end

  create_table "event_registrations", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "event_id", null: false
    t.string "status", default: "confirmed"
    t.decimal "amount_paid", precision: 8, scale: 2, default: "0.0"
    t.text "notes"
    t.datetime "registered_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["event_id"], name: "index_event_registrations_on_event_id"
    t.index ["registered_at"], name: "index_event_registrations_on_registered_at"
    t.index ["status"], name: "index_event_registrations_on_status"
    t.index ["user_id", "event_id"], name: "index_event_registrations_on_user_id_and_event_id", unique: true
    t.index ["user_id"], name: "index_event_registrations_on_user_id"
  end

  create_table "events", force: :cascade do |t|
    t.string "title", null: false
    t.text "description"
    t.text "prerequisites"
    t.datetime "start_time", null: false
    t.datetime "end_time", null: false
    t.decimal "price", precision: 8, scale: 2, default: "0.0"
    t.decimal "member_price", precision: 8, scale: 2
    t.integer "max_participants"
    t.integer "min_participants"
    t.string "status", default: "draft"
    t.integer "visibility", default: 2
    t.string "difficulty_level"
    t.text "what_to_bring"
    t.text "what_included"
    t.boolean "is_online", default: false
    t.string "meeting_link"
    t.boolean "archived", default: false
    t.datetime "published_at"
    t.integer "user_id", null: false
    t.integer "event_category_id"
    t.integer "event_location_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["archived"], name: "index_events_on_archived"
    t.index ["event_category_id"], name: "index_events_on_event_category_id"
    t.index ["event_location_id"], name: "index_events_on_event_location_id"
    t.index ["start_time", "end_time"], name: "index_events_on_start_time_and_end_time"
    t.index ["start_time"], name: "index_events_on_start_time"
    t.index ["status", "visibility", "archived"], name: "index_events_on_status_and_visibility_and_archived"
    t.index ["status"], name: "index_events_on_status"
    t.index ["user_id"], name: "index_events_on_user_id"
    t.index ["visibility"], name: "index_events_on_visibility"
  end

  create_table "knowledge_base_posts", force: :cascade do |t|
    t.integer "knowledge_base_id", null: false
    t.integer "post_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["knowledge_base_id", "post_id"], name: "index_knowledge_base_posts_on_knowledge_base_id_and_post_id", unique: true
    t.index ["knowledge_base_id"], name: "index_knowledge_base_posts_on_knowledge_base_id"
    t.index ["post_id"], name: "index_knowledge_base_posts_on_post_id"
  end

  create_table "knowledge_bases", force: :cascade do |t|
    t.string "name", null: false
    t.integer "visibility", default: 0, null: false
    t.integer "owner_id", null: false
    t.text "description"
    t.text "metadata", default: "{}"
    t.datetime "deleted_at"
    t.boolean "archived", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["archived"], name: "index_knowledge_bases_on_archived"
    t.index ["deleted_at"], name: "index_knowledge_bases_on_deleted_at"
    t.index ["name", "deleted_at"], name: "index_knowledge_bases_on_name_and_deleted_at", unique: true
    t.index ["owner_id"], name: "index_knowledge_bases_on_owner_id"
    t.index ["visibility", "owner_id"], name: "index_knowledge_bases_on_visibility_and_owner_id"
    t.index ["visibility"], name: "index_knowledge_bases_on_visibility"
  end

  create_table "messages", force: :cascade do |t|
    t.string "messageable_type", null: false
    t.integer "messageable_id", null: false
    t.string "role", null: false
    t.text "content"
    t.json "tool_calls"
    t.string "tool_call_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_messages_on_created_at"
    t.index ["messageable_type", "messageable_id", "role", "created_at"], name: "idx_on_messageable_type_messageable_id_role_created_b089ca31a7"
    t.index ["messageable_type", "messageable_id"], name: "index_messages_on_messageable"
    t.index ["tool_call_id"], name: "index_messages_on_tool_call_id"
  end

  create_table "noticed_events", force: :cascade do |t|
    t.string "type"
    t.string "record_type"
    t.bigint "record_id"
    t.json "params"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "notifications_count"
    t.index ["record_type", "record_id"], name: "index_noticed_events_on_record"
  end

  create_table "noticed_notifications", force: :cascade do |t|
    t.string "type"
    t.bigint "event_id", null: false
    t.string "recipient_type", null: false
    t.bigint "recipient_id", null: false
    t.datetime "read_at", precision: nil
    t.datetime "seen_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["event_id"], name: "index_noticed_notifications_on_event_id"
    t.index ["recipient_type", "recipient_id"], name: "index_noticed_notifications_on_recipient"
  end

  create_table "omni_auth_identities", force: :cascade do |t|
    t.string "uid"
    t.string "provider"
    t.integer "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "unionid"
    t.index ["provider", "uid"], name: "index_omni_auth_identities_on_provider_and_uid", unique: true
    t.index ["provider", "unionid"], name: "index_omni_auth_identities_on_provider_and_unionid"
    t.index ["user_id"], name: "index_omni_auth_identities_on_user_id"
  end

  create_table "posts", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "base_unit_id"
    t.string "title", null: false
    t.string "status", default: "draft"
    t.datetime "published_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["base_unit_id"], name: "index_posts_on_base_unit_id"
    t.index ["published_at"], name: "index_posts_on_published_at"
    t.index ["status"], name: "index_posts_on_status"
    t.index ["user_id"], name: "index_posts_on_user_id"
  end

  create_table "prompts", force: :cascade do |t|
    t.string "name", null: false
    t.text "template", null: false
    t.integer "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "name"], name: "index_prompts_on_user_id_and_name", unique: true
    t.index ["user_id"], name: "index_prompts_on_user_id"
  end

  create_table "sessions", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "source"
    t.index ["user_id"], name: "index_sessions_on_user_id"
  end

  create_table "subscriptions", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "plan_type"
    t.datetime "start_date"
    t.datetime "end_date"
    t.integer "status"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_subscriptions_on_user_id"
  end

  create_table "team_members", force: :cascade do |t|
    t.integer "team_id", null: false
    t.integer "user_id", null: false
    t.integer "role", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role"], name: "index_team_members_on_role"
    t.index ["team_id", "user_id"], name: "index_team_members_on_team_id_and_user_id", unique: true
    t.index ["team_id"], name: "index_team_members_on_team_id"
    t.index ["user_id"], name: "index_team_members_on_user_id"
  end

  create_table "team_resource_accesses", force: :cascade do |t|
    t.integer "team_member_id", null: false
    t.string "resource_type", null: false
    t.integer "resource_id", null: false
    t.string "resource_kind", null: false
    t.string "role", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["resource_kind", "role"], name: "index_team_resource_accesses_on_resource_kind_and_role"
    t.index ["resource_type", "resource_id"], name: "index_team_resource_accesses_on_resource"
    t.index ["team_member_id", "resource_type", "resource_id"], name: "index_team_resource_accesses_on_team_member_and_resource", unique: true
    t.index ["team_member_id"], name: "index_team_resource_accesses_on_team_member_id"
  end

  create_table "teams", force: :cascade do |t|
    t.string "name", null: false
    t.string "category"
    t.text "description"
    t.string "status", default: "active"
    t.string "avatar"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["category"], name: "index_teams_on_category"
    t.index ["name"], name: "index_teams_on_name", unique: true
  end

  create_table "tool_usages", force: :cascade do |t|
    t.integer "message_id", null: false
    t.string "function_name", null: false
    t.string "status", default: "pending", null: false
    t.text "arguments"
    t.text "result"
    t.datetime "started_at"
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["message_id", "function_name"], name: "index_tool_usages_on_message_id_and_function_name", unique: true
    t.index ["message_id"], name: "index_tool_usages_on_message_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email_address", null: false
    t.string "password_digest", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "phone_number"
    t.boolean "phone_verified", default: false
    t.string "username", null: false
    t.string "gender"
    t.string "location"
    t.json "hobbies", default: []
    t.json "preferences", default: {"language"=>"zh", "theme"=>"light", "timezone"=>"Asia/Shanghai", "currency"=>"CNY", "notification"=>{"email"=>false, "web"=>true, "service_wechat"=>true}}
    t.boolean "admin", default: false, null: false
    t.index ["admin"], name: "index_users_on_admin"
    t.index ["email_address"], name: "index_users_on_email_address", unique: true
    t.index ["phone_number"], name: "index_users_on_phone_number", unique: true
    t.index ["username"], name: "index_users_on_username", unique: true
  end

  create_table "wechat_configs", force: :cascade do |t|
    t.string "environment", default: "development", null: false
    t.string "account", null: false
    t.boolean "enabled", default: true
    t.string "appid"
    t.string "secret"
    t.string "corpid"
    t.string "corpsecret"
    t.integer "agentid"
    t.boolean "encrypt_mode"
    t.string "encoding_aes_key"
    t.string "token", null: false
    t.string "access_token", null: false
    t.string "jsapi_ticket", null: false
    t.boolean "skip_verify_ssl", default: true
    t.integer "timeout", default: 20
    t.string "trusted_domain_fullname"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["environment", "account"], name: "index_wechat_configs_on_environment_and_account", unique: true
  end

  create_table "wechat_sessions", force: :cascade do |t|
    t.string "openid", null: false
    t.string "hash_store"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["openid"], name: "index_wechat_sessions_on_openid", unique: true
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "assistants", "users"
  add_foreign_key "base_units", "users"
  add_foreign_key "conversations", "assistants"
  add_foreign_key "conversations", "users"
  add_foreign_key "event_instructors", "events"
  add_foreign_key "event_instructors", "users"
  add_foreign_key "event_registrations", "events"
  add_foreign_key "event_registrations", "users"
  add_foreign_key "events", "event_categories"
  add_foreign_key "events", "event_locations"
  add_foreign_key "events", "users"
  add_foreign_key "knowledge_base_posts", "knowledge_bases"
  add_foreign_key "knowledge_base_posts", "posts"
  add_foreign_key "knowledge_bases", "users", column: "owner_id"
  add_foreign_key "omni_auth_identities", "users"
  add_foreign_key "posts", "base_units"
  add_foreign_key "posts", "users"
  add_foreign_key "prompts", "users"
  add_foreign_key "sessions", "users"
  add_foreign_key "subscriptions", "users"
  add_foreign_key "team_members", "teams"
  add_foreign_key "team_members", "users"
  add_foreign_key "team_resource_accesses", "team_members"
  add_foreign_key "tool_usages", "messages"

  # Virtual tables defined in this database.
  # Note that virtual tables may not work with other database engines. Be careful if changing database.
  create_virtual_table "events_fts", "fts5", ["title", "description", "prerequisites", "what_to_bring", "what_included", "event_id UNINDEXED", "tokenize='unicode61'"]
end
