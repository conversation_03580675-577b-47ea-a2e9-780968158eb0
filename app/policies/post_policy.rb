class PostPolicy < ApplicationPolicy
  # See https://actionpolicy.evilmartians.io/#/writing_policies
  #
  # def index?
  #   true
  # end
  #
  # def update?
  #   # here we can access our context and record
  #   user.admin? || (user.id == record.user_id)
  # end

  # Scoping
  # See https://actionpolicy.evilmartians.io/#/scoping
  #
  # relation_scope do |relation|
  #   next relation if user.admin?
  #   relation.where(user: user)
  # end

  # Use the method defined in ApplicationPolicy to check if user is present
  pre_check :authenticated?

  relation_scope do |relation|
    next relation if user.admin?  # Admin可以看到所有文章
    relation.where(user: user)
  end

  def create?
    admin? || authenticated?
  end

  def update?
    admin? || owner? || knowledge_base_editor?
  end

  def destroy?
    admin? || (owner? && record.draft?)
  end

  private

  def owner?
    record.user == user
  end

  def knowledge_base_editor?
    return false unless record.knowledge_bases.any?

    # 获取当前用户的所有 TeamMember 记录的 ID
    user_team_member_ids = user.team_member_ids # Assumes has_many :team_members association exists

    # 检查是否有任何一个知识库，用户通过其团队成员身份拥有编辑或维护权限
    record.knowledge_bases.any? do |kb|
      kb.team_resource_accesses.exists?(
        team_member_id: user_team_member_ids, # 使用 TeamMember ID 列表进行查询
        role: %w[editor maintainer]
      )
    end
  end
end
