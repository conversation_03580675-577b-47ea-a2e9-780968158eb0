module Ai
  class MessageService
    # 类方法部分
    class << self
      # 创建并处理用户消息和AI助手响应
      # @param messageable [ActiveRecord] 可关联消息的模型实例
      # @param content [String] 用户消息内容
      # @return [Hash] 包含用户消息和助手消息的哈希
      #   - user_message [Message] 用户消息记录
      #   - assistant_message [Message] 助手消息记录
      def create_and_process(messageable:, content:)
        # 创建用户消息
        user_message = messageable.messages.create!(
          role: "user",
          content: content
        )

        # 创建助手消息
        assistant_message = messageable.messages.create!(
          role: "assistant",
          content: "I'm thinking...",
          status: :processing
        )

        # 启动异步处理，传递 user_message_id
        process_async(
          messageable: messageable,
          user_message_id: user_message.id, # 传递用户消息 ID
          assistant_message: assistant_message
        )

        # 返回创建的消息
        {
          user_message: user_message,
          assistant_message: assistant_message
        }
      end

      # 异步处理 - 接收并传递 user_message_id
      def process_async(messageable:, user_message_id:, assistant_message:)
        Ai::ResponseJob.perform_later(
          messageable,
          user_message_id, # 传递用户消息 ID 给 Job
          assistant_message
        )
      end

      # 同步处理（可用于测试或特殊场景）
      # 注意：同步处理也需要 user_message_id
      def process_sync(messageable:, user_message_id:, assistant_message:)
        new(
          messageable: messageable,
          user_message_id: user_message_id, # 传递用户消息 ID 给实例
          assistant_message: assistant_message
        ).process
      end


      # 获取指定 messageable 的历史消息
      # 已经过滤掉 system 消息和内容为 "I'm thinking..." 的助手消息
      # @param messageable [ActiveRecord] 可关联消息的模型实例
      # @return [Array<Hash>] 格式化后的历史消息数组
      def get_messageable_history(messageable)
        # 加载对话的工具使用记录
        messages = case messageable
        when Conversation
          messageable.messages.for_langchain_assistant_context
        else
          messageable.messages.for_langchain_assistant_context
        end

          # 转换为统一格式
          messages.ordered.map do |message|
          {
            id: message.id,
            role: message.role,
            content: message.content,
            tool_calls: message.tool_calls || [],
            tool_call_id: message.tool_call_id
          }
        end
      end

      # 广播消息创建或更新
      # @param message [Message] 要广播的消息
      def broadcast(message)
        stream = "#{message.messageable_type.downcase}_#{message.messageable_id}"

        if message.previously_new_record?
          # 新消息 - 追加到列表
          Turbo::StreamsChannel.broadcast_append_to(
            stream,
            target: "messages",
            partial: "ai/messages/message",
            locals: { message: message }
          )
        else
          # 更新消息 - 替换现有消息
          Turbo::StreamsChannel.broadcast_replace_to(
            stream,
            target: "message_#{message.id}",
            partial: "ai/messages/message",
            locals: { message: message }
          )
        end
      end
    end

    # 实例方法部分
    attr_reader :messageable, :user_message, :assistant_message

    # 更新初始化方法以接收 user_message_id 并查找对象
    def initialize(messageable:, user_message_id:, assistant_message:)
      @messageable = messageable
      @user_message = messageable.messages.find(user_message_id)
      @assistant_message = assistant_message
    end

    def process
      # 获取 Assistant 对象
      assistant = @messageable.assistant

      # 设置 langchain_assistant，传递 user_message_id 以便在缓存未命中时排除它
      assistant.set_langchain_assistant(@assistant_message, user_message_id: @user_message.id)
      langchain_assistant = assistant.instance_variable_get(:@langchain_assistant) # 获取实例

      # 检查 user_message 是否已存在于加载的缓存中（避免重复添加）
      # 注意：这需要 Langchain::Assistant::Messages::OpenAIMessage 实现某种比较逻辑，
      # 或者我们基于内容和角色进行检查。为简单起见，我们先假设它需要被添加。
      # 更健壮的实现可能需要检查 langchain_assistant.messages 中是否已有此 user_message。

      # 构建当前用户消息的 Langchain 对象
      # 确保 @user_message 是从 initialize 中获取的 Message 实例
      current_user_langchain_message = langchain_assistant.llm_adapter.build_message(
        role: @user_message.role,
        content: @user_message.content
        # 用户消息通常没有 tool_calls 或 tool_call_id
      )

      # 将当前用户消息添加到 Langchain Assistant 的消息列表
      # 检查是否已存在（基于内容和角色可能不够，最好有唯一标识符，但暂时先添加）
      # TODO: 添加更可靠的重复检查逻辑，如果 OpenAIMessage 对象支持比较或有唯一ID
      unless langchain_assistant.messages.any? { |m| m.role == current_user_langchain_message.role && m.content == current_user_langchain_message.content }
        langchain_assistant.messages << current_user_langchain_message
        # Rails.logger.info "Added current user message to Langchain assistant instance." # 移除调试信息

        # 将当前用户消息追加到缓存
        # 注意：这里假设 load_cached_langchain_assistant_messages 已经处理了缓存加载
        # 我们只负责追加当前这个明确要处理的用户消息
        assistant.append_new_message_to_cache(current_user_langchain_message)
        # else # 移除调试信息
        # Rails.logger.warn "Skipped adding duplicate user message to Langchain assistant instance."
        # 如果消息已存在于从缓存加载的列表中，我们不需要再次追加到缓存
      end


      # 处理响应，使用 run! 而不是 add_message_and_run!
      langchain_assistant.run!
    rescue StandardError => e
      # 错误处理
      handle_error(e)
      raise # 重新抛出以触发 Job 重试
    end

    private

    def handle_error(error)
      # 更新消息为错误状态
      @assistant_message.update(
        content: "Sorry, an error occurred: #{error.message}",
        status: :failed
      )

      # 记录错误日志
      Rails.logger.error("AI Response Error: #{error.message}\n#{error.backtrace.join("\n")}")
    end
  end # End of MessageService class
end # End of Ai module
