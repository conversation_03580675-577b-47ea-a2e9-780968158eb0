require 'rails_helper'

RSpec.describe Event, "search functionality" do
  describe "FTS5 search with SqliteSearch concern" do
    let!(:event1) { create(:event, title: "Ruby on Rails Workshop", description: "Learn Ruby on Rails basics") }
    let!(:event2) { create(:event, title: "Python Programming", description: "Python for beginners") }
    let!(:event3) { create(:event, title: "中文编程课程", description: "学习编程基础知识") }
    let!(:event4) { create(:event, title: "JavaScript 高级技巧", description: "深入理解 JavaScript") }

    before do
      # 确保搜索索引已更新
      Event.rebuild_search_index
    end

    describe ".full_search" do
      it "finds events by title" do
        results = Event.full_search("Ruby")
        expect(results).to include(event1)
        expect(results).not_to include(event2, event3, event4)
      end

      it "finds events by description" do
        results = Event.full_search("beginners")
        expect(results).to include(event2)
        expect(results).not_to include(event1, event3, event4)
      end

      it "supports Chinese search" do
        results = Event.fts_search("中文")
        expect(results).to include(event3)
        expect(results).not_to include(event1, event2, event4)
      end

      it "supports mixed language search" do
        results = Event.full_search("JavaScript")
        expect(results).to include(event4)
        expect(results).not_to include(event1, event2, event3)
      end

      it "supports partial matching for English" do
        results = Event.full_search("prog")
        expect(results).to include(event2)
      end

      it "returns empty result for no matches" do
        results = Event.full_search("NonExistentContent")
        expect(results).to be_empty
      end
    end

    describe ".phrase_search" do
      it "finds exact phrases" do
        results = Event.phrase_search("Ruby on Rails")
        expect(results).to include(event1)
        expect(results).not_to include(event2, event3, event4)
      end

      it "supports Chinese phrases" do
        results = Event.fts_search("编程基础")
        expect(results).to include(event3)
        expect(results).not_to include(event1, event2, event4)
      end
    end

    describe ".search_in_field" do
      it "searches only in specified field" do
        results = Event.search_in_field("title", "Ruby")
        expect(results).to include(event1)
        expect(results).not_to include(event2)

        results = Event.search_in_field("description", "Ruby")
        expect(results).to include(event1)
      end
    end

    describe ".advanced_search" do
      it "supports AND operator" do
        create(:event, title: "Advanced Ruby", description: "Rails framework")
        Event.rebuild_search_index

        results = Event.advanced_search("Ruby AND Rails")
        expect(results.count).to be >= 1
      end

      it "supports OR operator" do
        results = Event.advanced_search("Ruby OR Python")
        expect(results).to include(event1, event2)
      end

      it "supports NOT operator" do
        results = Event.advanced_search("programming NOT Ruby")
        expect(results).to include(event2)
        expect(results).not_to include(event1)
      end
    end

    describe ".search_suggestions" do
      it "provides autocomplete suggestions" do
        suggestions = Event.search_suggestions("Rub")
        expect(suggestions).to include("Ruby on Rails Workshop")
      end

      it "provides Chinese suggestions" do
        # 中文搜索建议可能需要不同的实现，暂时跳过
        skip "Chinese search suggestions need special implementation"
      end

      it "limits suggestions" do
        10.times { |i| create(:event, title: "Test Event #{i}") }
        Event.rebuild_search_index

        suggestions = Event.search_suggestions("Test", limit: 5)
        expect(suggestions.count).to eq(5)
      end
    end

    describe "backward compatibility" do
      it "maintains fts_search alias" do
        results = Event.fts_search("Ruby")
        expect(results).to include(event1)
        expect(results).not_to include(event2, event3, event4)
      end

      it "returns results with relevance_score" do
        results = Event.fts_search_with_relevance("Ruby")
        expect(results.first.attributes).to have_key("relevance_score")
      end
    end

    describe "index updates" do
      it "updates index when event is created" do
        new_event = create(:event, title: "New Workshop", description: "Brand new content")

        results = Event.full_search("Workshop")
        expect(results).to include(new_event)
      end

      it "updates index when event is updated" do
        event1.update!(title: "Updated Title with Unique Content", description: "Completely different content")

        results = Event.fts_search("Unique")
        expect(results).to include(event1)

        results = Event.fts_search("Ruby")
        expect(results).not_to include(event1)
      end

      it "removes from index when event is destroyed" do
        event1.destroy!

        results = Event.full_search("Ruby")
        expect(results).not_to include(event1)
      end
    end

    describe "error handling" do
      it "falls back to LIKE search on FTS5 errors" do
        # 模拟 FTS5 错误
        allow(Event.connection).to receive(:execute).and_raise(ActiveRecord::StatementInvalid)

        results = Event.full_search("Ruby")
        # 应该仍然返回结果（通过 fallback_search）
        expect { results.to_a }.not_to raise_error
      end
    end
  end
end
