import { Controller } from "@hotwired/stimulus"
// Assuming Prism is available globally via application.js import
// import Prism from 'prismjs'; // Uncomment if Prism is not global

export default class extends Controller {
  // Added "form" to targets
  static targets = ["input", "submit", "messages", "form"]
  observer = null // Initialize observer property

  connect() {
    this.setupMutationObserver() // Call setup method
    this.adjustHeight()

    // Highlight any existing code blocks on initial load
    if (this.hasMessagesTarget && typeof Prism !== 'undefined') {
      this.messagesTarget.querySelectorAll('pre code[class*="language-"]').forEach((block) => {
        Prism.highlightElement(block);
      });
    }

    // Delay the initial scroll slightly to allow the browser to render
    setTimeout(() => this.scrollToBottom(), 10); // 10ms delay, adjust if needed
  }

  disconnect() {
    // Disconnect the observer when the controller is disconnected
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  setupMutationObserver() {
    if (!this.hasMessagesTarget) return; // Exit if messages target doesn't exist

    const targetNode = this.messagesTarget;

    // Options for the observer (watch for additions to the child list)
    const config = { childList: true, subtree: true };

    // Callback function to execute when mutations are observed
    const callback = (mutationsList, observer) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            // Check if the added node is an element
            if (node.nodeType === Node.ELEMENT_NODE) {
              let highlighted = false;
              // Find code blocks within the new node or if the node itself is one
              const codeBlocks = node.querySelectorAll('pre code[class*="language-"]');
              if (codeBlocks.length > 0) {
                codeBlocks.forEach(block => {
                  if (typeof Prism !== 'undefined') {
                    Prism.highlightElement(block);
                    highlighted = true;
                  }
                });
              } else if (node.matches && node.matches('pre code[class*="language-"]')) {
                // Handle case where the added node itself is the code block
                if (typeof Prism !== 'undefined') {
                  Prism.highlightElement(node);
                  highlighted = true;
                }
              }
              // No longer check if highlighted, scroll whenever a node is added
            }
          });
          // Scroll down after processing all added nodes in this mutation record
          // Check if any nodes were actually added before scrolling
          if (mutation.addedNodes.length > 0) {
            this.scrollToBottom();
          }
        }
      }
    };

    // Create an observer instance linked to the callback function
    this.observer = new MutationObserver(callback);

    // Start observing the target node for configured mutations
    this.observer.observe(targetNode, config);
  }


  handleKeydown(event) {
    // 如果按下 Enter 且没有按住 Shift
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      this.submit()
    }
  }

  submit() {
    if (this.inputTarget.value.trim() !== "") {
      // Changed this.element to this.formTarget
      this.formTarget.requestSubmit()
    }
  }

  reset() {
    this.inputTarget.value = ""
    this.adjustHeight()
    this.inputTarget.focus()
    this.scrollToBottom()
  }

  // Updated method: Scroll to the bottom of the chat area using scrollIntoView
  scrollToBottom() {
    if (this.hasMessagesTarget) {
      const lastMessage = this.messagesTarget.lastElementChild; // Get the last message element
      if (lastMessage) {
        // Scroll the end of the last message into view immediately
        lastMessage.scrollIntoView({ block: 'end', behavior: 'auto' });
      } else {
        // Fallback or initial state: scroll the container itself (might be less reliable)
        this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight;
      }
    }
  }

  adjustHeight() {
    const textarea = this.inputTarget
    textarea.style.height = "auto"

    const maxHeight = parseInt(textarea.dataset.maxHeight || 200)
    const newHeight = Math.min(textarea.scrollHeight, maxHeight)

    textarea.style.height = `${newHeight}px`

    // 如果内容超过最大高度，启用滚动
    textarea.style.overflowY = textarea.scrollHeight > maxHeight ? "auto" : "hidden"
  }
}
