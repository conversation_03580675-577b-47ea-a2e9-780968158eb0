class KnowledgeBasePost < ApplicationRecord
  belongs_to :knowledge_base
  belongs_to :post

  validates :post_id, uniqueness: { scope: :knowledge_base_id }
  validate :validate_post_limits

  private

  def validate_post_limits
    return unless knowledge_base

    existing_posts_count = knowledge_base.knowledge_base_posts.where.not(id: id || -1).size
    if existing_posts_count >= KnowledgeBase::MAX_POSTS_LIMIT
      errors.add(:base, "Maximum number of posts reached")
    end
  end
end
