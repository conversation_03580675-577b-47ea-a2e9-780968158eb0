# AI 模块中文翻译
zh:
  ai:
    common:
      loading: "加载中..."
      no_results: "未找到结果"
      copy: "复制"
      copied: "已复制"
      unknown_type: "未知消息类型"
      error: "错误"
      retry: "重试"
    assistants:
      processing_message: "正在处理工具调用..."
      title: "我的助手"
      new: "新建助手"
      edit: "编辑助手"
      delete: "删除助手"
      back_to_assistants: "返回助手列表"
      no_assistants_found: "未找到助手，请创建一个！"
      form:
        name_label: "名称"
        name_placeholder: "输入助手名称"
        instructions_label: "指令"
        instructions_placeholder: "输入助手指令...(输入/使用命令)"
        model_label: "AI模型"
        tool_usage_mode: "工具使用模式"
        tool_choices:
          auto: "自动 - 根据上下文智能选择工具"
          none: "无 - 纯对话不使用工具"
          any: "任意 - 可使用所有可用工具"
        available_tools: "可用工具"
        select_all: "全选工具"
        save_button: "保存助手"
        create_button: "创建助手"
        update_button: "更新助手"
        cancel_button: "取消"
      show:
        title: "助手详情"
        info:
          title: "助手信息"
          name: "名称"
          instructions: "指令"
          no_instructions: "未提供指令"
          model: "模型"
          tool_choice: "工具选择"
          available_tools: "可用工具"
          no_tools: "未为此助手配置工具"
        time_info:
          title: "时间信息"
          conversations_count: "对话次数"
          messages_count: "消息数量"
        actions:
          start_chat: "开始聊天"
        delete_confirm: "确定要删除此助手吗？"
      index:
        title: "我的助手"
        new_button: "新建助手"
        table_headers:
          name: "名称"
          model: "模型"
          tools: "工具"
          conversations: "对话"
          actions: "操作"
        actions:
          chat: "聊天"
          edit: "编辑"
          delete: "删除"
      fields:
        name: "名称"
        instructions: "指令"
        models: "AI模型"
        tools: "可用工具"
      tools:
        web_search: "网页搜索"
        knowledge_search: "知识搜索"
        file_upload: "文件上传"
      actions:
        create: "创建助手"
        update: "更新助手"
        delete_confirm: "确定要删除此助手吗？"
      tips:
        title: "配置提示"
        basic:
          title: "基础配置"
          name: "为助手选择一个描述性名称"
          instructions: "定义具体的规则和偏好"
        prompts:
          title: "使用提示"
          select: "在指令中输入/访问提示模板"
          combine: "混合多个提示以增强能力"
          customize: "修改提示模板以满足需求"
        tools:
          title: "工具选择"
          quick_setup: "使用切换开关选择/取消选择所有工具"
          custom: "根据需求选择特定工具"
          management: "每个工具提供独特功能"
        modes:
          title: "工具使用模式"
          auto: "助手智能选择合适工具"
          none: "纯对话不使用工具"
          any: "助手可自由使用任何选定工具"
        practices:
          title: "最佳实践"
          items:
            - "选择适当的提示以获得专业能力"
            - "从基本工具开始，根据需要添加更多"
            - "使用自动模式获得平衡的工具使用"
            - "提供清晰的指令以获得最佳结果"
    conversations:
      title: "AI聊天"
      new: "新对话"
      clear: "清空对话"
      clear_confirm: "确定要清空此对话吗？"
      placeholder: "输入 / 使用命令\nEnter 发送, Shift + Enter 换行"
      send: "发送"
      stop_generating: "停止生成"
      regenerate: "重新生成响应"
      loading: "AI正在思考..."
      error: "处理请求时出错，请重试"
      retry: "重试"
      empty_state: "通过选择助手并发送消息开始新对话"
      chat:
        title: "聊天设置"
        new_chat: "新聊天"
        assistant_selection: "选择助手"
        assistant_prompt: "为此对话选择AI助手"
        no_assistant_selected: "未选择助手"
        create_assistant: "创建新助手"
        view_history: "查看聊天历史"
        back_to_conversations: "返回对话列表"
      messages:
        tool_calls: "工具调用"
        tool_call: "工具调用"
        tool_results: "工具结果"
        tool_result: "结果:"
        tool_status: "状态:"
        thinking: "思考中..."
        error_occurred: "发生错误"
      history:
        title: "聊天历史"
        view_all: "查看所有对话"
        empty: "暂无聊天历史"
      actions:
        start_new: "开始新聊天"
    messages:
      assistant: "助手"
      user: "用户"
      tool: "工具"
      system: "系统"
      actions:
        copy: "复制消息"
        edit: "编辑消息"
        delete: "删除消息"
      tool_info:
        name: "工具名称:"
        input: "输入:"
        output: "输出:"
        status: "状态:"
        error: "错误:"
    prompts:
      title: "保存的提示"
      new: "新建提示"
      edit: "编辑提示"
      delete: "删除提示"
      fields:
        title: "标题"
        content: "内容"
        tags: "标签"
