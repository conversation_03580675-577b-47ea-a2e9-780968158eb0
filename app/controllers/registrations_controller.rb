class RegistrationsController < ApplicationController
  before_action :set_step, only: [ :new, :create ]
  allow_unauthenticated_access only: [ :new, :create, :send_verification_code ] # Removed :preview_subscribe

  # TODO: Kept for potential future use or testing, currently not linked from registration flow.
  # def preview_subscribe
  #   render :preview_subscribe
  # end

  def new
    # Initialize user object, potentially pre-filled if coming back from failed validation
    @user = User.new(phone_number: session[:verification_phone]) if @current_step == "create_account" && session[:verification_phone]
    @user ||= User.new

    render :new, locals: { current_step: @current_step, user: @user }
  end

  def create
    @current_step = params.dig(:user, :step) || params[:step]

    case @current_step
    when "send_verification_code"
      # Frontend now sends E.164 formatted phone number directly
      phone = params[:phone]
      code = params[:verification_code]

      # logger.debug "--- Verification Step ---"
      # logger.debug "Original phone param: #{params[:phone]}"
      # logger.debug "Verification code param: #{code}"
      # logger.debug "Attempting verification with phone: #{phone}, code: #{code}"

      if SmsService.verify_sms_code(phone, code)
        # logger.debug "Verification successful!"
        # Store the verified (E.164) phone number in session
        session[:verification_phone] = phone
        @current_step = "create_account"
        # Initialize user with the verified phone number
        @user = User.new(phone_number: session[:verification_phone])
        render turbo_stream: [
          turbo_stream.update("form_container",
            partial: "registrations/registration_user"), # Partial uses @user
          turbo_stream.update("stepper",
            partial: "registrations/stepper",
            locals: { current_step: @current_step }) # Stepper still needs current_step local
        ]
      else
        # logger.debug "Verification failed!"
        flash.now[:alert] = "Invalid Code"
        render turbo_stream: [
          turbo_stream.update("form_container",
            partial: "registrations/verify_identity"),
          turbo_stream.update("flash",
            partial: "shared/flash")
        ], status: :unprocessable_entity
      end
    when "create_account"
      @user = User.new(user_params)
      # Apply phone number from SMS verification session if present
      @user.phone_number = session[:verification_phone] if session[:verification_phone]

      if @user.save(context: :registration)
        session.delete(:verification_phone) # Clean up SMS session data
        start_new_session_for @user, source: :registration
        redirect_to root_path, notice: t("auth.registrations.success")
      else
        # Re-render the registration form with errors
        # @user retains submitted values
        render turbo_stream: [
          turbo_stream.update("form_container",
            partial: "registrations/registration_user",
            locals: { user: @user }),
          turbo_stream.update("flash",
            partial: "shared/flash",
            locals: { alert: @user.errors.full_messages.join(", ") }) # Keep flash update for errors
        ], status: :unprocessable_entity
      end
      # Removed the "subscribe" step logic entirely
    end
  end

  def send_verification_code
    phone = params[:phone]
    SendVerificationCodeJob.perform_later(phone)
    render json: { message: "Verification code sent" }
  end

  private

  def set_step
    @current_step = params[:step] || "send_verification_code"
  end

  def user_params
    params.require(:user).permit(
      :username,
      :email_address,
      :password,
      :password_confirmation,
      :phone_number
    )
  end
end
