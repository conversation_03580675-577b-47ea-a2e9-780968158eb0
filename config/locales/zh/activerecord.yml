# ActiveRecord 模型和属性的中文翻译
zh:
  activerecord:
    models:
      user: "用户"
      team: "团队"
      team_member: "团队成员"
      knowledge_base: "知识库"
      post: "帖子"
      assistant: "助手"
      conversation: "对话"
      message: "消息"
      base_unit: "基础单元"
      subscription: "订阅"
    attributes:
      user:
        name: "姓名"
        email: "邮箱"
        password: "密码"
        bio: "个人简介"
        preferences: "偏好设置"
      team:
        name: "名称"
        description: "描述"
    errors:
      messages:
        blank: "不能为空"
        taken: "已被占用"
        invalid: "无效"
        too_short: "太短(最少需要%{count}个字符)"
        too_long: "太长(最多允许%{count}个字符)"
        confirmation: "与确认值不匹配"
