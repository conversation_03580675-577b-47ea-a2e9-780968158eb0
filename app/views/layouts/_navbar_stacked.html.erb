<header>
  <nav class="fixed z-30 w-full bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700 py-3 px-4">
    <div class="flex justify-between items-center max-w-(--breakpoint-2xl) mx-auto">
      <div class="flex justify-start items-center">
        <%= link_to root_path, class: "flex mr-14" do %>
          <%= image_tag "https://codingirls.club/cgc_logo_orange_white.png", class: "mr-3 h-8", alt: "CGC Logo" %>
          <span class="self-center hidden sm:flex text-2xl font-semibold whitespace-nowrap dark:text-white">Coding Girls Club</span>
        <% end %>
        <!-- Desktop Menu -->
        <div class="hidden justify-between items-center w-full lg:flex lg:w-auto lg:order-1">
          <ul class="flex flex-col mt-4 space-x-6 text-sm font-medium lg:flex-row xl:space-x-8 lg:mt-0">
            <li>
              <%= link_to "Home", root_path, class: "block rounded-xs text-primary-600 dark:text-primary-500", aria: { current: "page" } %>
            </li>
            <li>
              <%= link_to "Courses", "#", class: "block text-gray-700 hover:text-primary-600 dark:text-gray-400 dark:hover:text-white" %>
            </li>
            <li>
              <%= link_to "Community", "#", class: "block text-gray-700 hover:text-primary-600 dark:text-gray-400 dark:hover:text-white" %>
            </li>
            <li>
              <%= link_to "Resources", "#", class: "block text-gray-700 hover:text-primary-600 dark:text-gray-400 dark:hover:text-white" %>
            </li>
            <li>
              <button id="dropdownNavbarLink" data-dropdown-toggle="dropdownNavbar" class="flex justify-between items-center py-2 pr-4 pl-3 w-full font-medium text-gray-700 border-b border-gray-100 hover:bg-gray-50 md:hover:bg-transparent md:border-0 md:hover:text-primary-600 md:p-0 md:w-auto dark:text-gray-400 dark:hover:text-white dark:focus:text-white dark:border-gray-700 dark:hover:bg-gray-700 md:dark:hover:bg-transparent">
                More
                <svg class="ml-1 w-4 h-4" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
              </button>
              <!-- Dropdown menu -->
              <div id="dropdownNavbar" class="hidden z-20 w-44 font-normal bg-white rounded-xs divide-y divide-gray-100 shadow-xs dark:bg-gray-700 dark:divide-gray-600">
                <ul class="py-1 text-sm text-gray-700 dark:text-gray-400" aria-labelledby="dropdownLargeButton">
                  <li>
                    <%= link_to "Blog", "#", class: "block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" %>
                  </li>
                  <li>
                    <%= link_to "Events", "#", class: "block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" %>
                  </li>
                  <li>
                    <%= link_to "About Us", "#", class: "block py-2 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" %>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
      </div>

      <div class="flex justify-between items-center lg:order-2">
        <!-- Search button -->
        <button type="button" data-modal-target="searchModal" data-modal-toggle="searchModal" class="p-2 text-gray-500 rounded-xs hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600">
          <span class="sr-only">Search</span>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </button>

        <!-- Notifications button -->
        <button type="button" data-dropdown-toggle="notification-dropdown" class="p-2 text-gray-500 rounded-xs hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600">
          <span class="sr-only">View notifications</span>
          <div class="relative">
            <svg aria-hidden="true" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path></svg>
            <div class="absolute -top-2 -right-2 inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">3</div>
          </div>
        </button>
        <!-- Notifications dropdown -->
        <div class="hidden overflow-hidden z-50 my-4 max-w-sm text-base list-none bg-white rounded-xs divide-y divide-gray-100 shadow-lg dark:bg-gray-700 dark:divide-gray-600" id="notification-dropdown">
          <div class="block py-2 px-4 text-base font-semibold text-center text-gray-700 bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            Notifications
          </div>
          <div>
            <% # Rails loop for actual notifications %>
            <a href="#" class="flex py-3 px-4 border-b hover:bg-gray-100 dark:hover:bg-gray-600 dark:border-gray-600">
              <div class="shrink-0">
                <%= image_tag "https://flowbite.com/docs/images/people/profile-picture-1.jpg", class: "w-11 h-11 rounded-full", alt: "User avatar" %>
                <div class="flex absolute justify-center items-center ml-6 -mt-5 w-5 h-5 rounded-full border border-white bg-primary-600 dark:border-gray-700">
                  <svg aria-hidden="true" class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M8.707 7.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l2-2a1 1 0 00-1.414-1.414L11 7.586V3a1 1 0 10-2 0v4.586l-.293-.293z"></path><path d="M3 5a2 2 0 012-2h1a1 1 0 010 2H5v7h2l1 2h4l1-2h2V5h-1a1 1 0 110-2h1a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"></path></svg>
                </div>
              </div>
              <div class="pl-3 w-full">
                <div class="text-gray-500 font-normal text-sm mb-1.5 dark:text-gray-400">New message from <span class="font-semibold text-gray-900 dark:text-white">Sarah</span>: "Hey, are you ready to start coding?"</div>
                <div class="text-xs font-medium text-primary-600 dark:text-primary-500">a few moments ago</div>
              </div>
            </a>
          </div>
          <a href="#" class="block py-2 text-base font-normal text-center text-gray-900 bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:text-white dark:hover:underline">
            <div class="inline-flex items-center">
              <svg aria-hidden="true" class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg>
              View all
            </div>
          </a>
        </div>

        <!-- Theme toggle button -->
        <button id="theme-toggle" data-tooltip-target="tooltip-toggle" type="button" class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-hidden focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-xs text-sm p-2.5">
          <svg id="theme-toggle-dark-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path></svg>
          <svg id="theme-toggle-light-icon" class="hidden w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path></svg>
        </button>

        <!-- Quick actions button -->
        <button type="button" data-dropdown-toggle="apps-dropdown" class="p-2 text-gray-500 rounded-xs hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600">
          <span class="sr-only">Quick actions</span>
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
        </button>
        <!-- Quick actions dropdown -->
        <div class="hidden overflow-hidden z-50 my-4 max-w-sm text-base list-none bg-white rounded-xs divide-y divide-gray-100 shadow-lg dark:bg-gray-700 dark:divide-gray-600" id="apps-dropdown">
          <div class="block py-2 px-4 text-base font-semibold text-center text-gray-700 bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            Quick Actions
          </div>
          <div class="grid grid-cols-3 gap-4 p-4">
            <%= link_to "#", class: "block p-4 text-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 group" do %>
              <svg aria-hidden="true" class="mx-auto mb-1 w-7 h-7 text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zm2 5V6a2 2 0 10-4 0v1h4zm-6 3a1 1 0 112 0 1 1 0 01-2 0zm7-1a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd"></path></svg>
              <div class="text-sm font-medium text-gray-900 dark:text-white">Courses</div>
            <% end %>
            <%= link_to "#", class: "block p-4 text-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 group" do %>
              <svg aria-hidden="true" class="mx-auto mb-1 w-7 h-7 text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path></svg>
              <div class="text-sm font-medium text-gray-900 dark:text-white">Community</div>
            <% end %>
            <%= link_to "#", class: "block p-4 text-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 group" do %>
              <svg aria-hidden="true" class="mx-auto mb-1 w-7 h-7 text-gray-400 group-hover:text-gray-500 dark:text-gray-400 dark:group-hover:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z" clip-rule="evenodd"></path></svg>
              <div class="text-sm font-medium text-gray-900 dark:text-white">Resources</div>
            <% end %>
          </div>
        </div>

        <!-- User menu button -->
        <button type="button" class="flex mx-3 text-sm bg-gray-800 rounded-full md:mr-0 shrink-0 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" id="userMenuDropdownButton" aria-expanded="false" data-dropdown-toggle="userMenuDropdown">
          <span class="sr-only">Open user menu</span>
          <%= render 'shared/avatar', user: current_user, size: 8, alt_text: "User avatar" %> <%# Using default alt text from partial %>
        </button>
        <!-- User dropdown menu -->
        <div class="hidden z-50 my-4 w-56 text-base list-none bg-white rounded-xs divide-y divide-gray-100 shadow-xs dark:bg-gray-700 dark:divide-gray-600" id="userMenuDropdown">
          <div class="py-3 px-4">
            <span class="block text-sm font-semibold text-gray-900 dark:text-white"><%= current_user&.username || "Guest" %></span>
            <span class="block text-sm font-light text-gray-500 truncate dark:text-gray-400"><%= current_user&.email_address || "Not logged in" %></span>
          </div>
          <ul class="py-1 font-light text-gray-500 dark:text-gray-400" aria-labelledby="userMenuDropdownButton">
            <li>
              <%= link_to "Profile", "#", class: "block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white" %>
            </li>
            <li>
              <%= link_to "Account Settings", "#", class: "block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-400 dark:hover:text-white" %>
            </li>
          </ul>
          <ul class="py-1 font-light text-gray-500 dark:text-gray-400" aria-labelledby="userMenuDropdownButton">
            <li>
              <%= link_to "#", class: "flex items-center py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do %>
                <svg class="mr-2 w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path></svg>
                My Favorites
              <% end %>
            </li>
            <li>
              <%= link_to "#", class: "flex items-center py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" do %>
                <svg class="mr-2 w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path></svg>
                My Collections
              <% end %>
            </li>
          </ul>
          <ul class="py-1 font-light text-gray-500 dark:text-gray-400" aria-labelledby="dropdown">
            <li>
              <% if authenticated? %>
                <%= button_to "Sign Out", sign_out_path, method: :delete, class: "block w-full text-left py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" %>
              <% else %>
                <%= link_to "Sign In", sign_in_path, class: "block py-2 px-4 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white" %>
              <% end %>
            </li>
          </ul>
        </div>

        <!-- Mobile menu button -->
        <button type="button" id="toggleMobileMenuButton" data-collapse-toggle="toggleMobileMenu" class="items-center p-2 text-gray-500 rounded-lg md:ml-2 lg:hidden hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600">
          <span class="sr-only">Open menu</span>
          <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
        </button>
      </div>
    </div>
  </nav>

  <!-- Mobile menu -->
  <nav class="bg-white dark:bg-gray-900">
    <ul id="toggleMobileMenu" class="hidden flex-col mt-0 pt-16 w-full text-sm font-medium lg:hidden">
      <li class="block border-b dark:border-gray-700">
        <%= link_to "Home", root_path, class: "block py-3 px-4 text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0", aria: { current: "page" } %>
      </li>
      <li class="block border-b dark:border-gray-700">
        <%= link_to "Courses", "#", class: "block py-3 px-4 text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0" %>
      </li>
      <li class="block border-b dark:border-gray-700">
        <%= link_to "Community", "#", class: "block py-3 px-4 text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0" %>
      </li>
      <li class="block border-b dark:border-gray-700">
        <%= link_to "Resources", "#", class: "block py-3 px-4 text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0" %>
      </li>
      <li class="block border-b dark:border-gray-700">
        <button type="button" data-collapse-toggle="dropdownMobileNavbar" class="flex justify-between items-center py-3 px-4 w-full text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0">
          More
          <svg class="w-6 h-6 text-gray-500 dark:text-gray-400" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path></svg>
        </button>
        <ul id="dropdownMobileNavbar" class="hidden">
          <li class="block border-t border-b dark:border-gray-700">
            <%= link_to "Blog", "#", class: "block py-3 px-4 text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0" %>
          </li>
          <li class="block border-b dark:border-gray-700">
            <%= link_to "Events", "#", class: "block py-3 px-4 text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0" %>
          </li>
          <li class="block">
            <%= link_to "About Us", "#", class: "block py-3 px-4 text-gray-900 lg:py-0 dark:text-white lg:hover:underline lg:px-0" %>
          </li>
        </ul>
      </li>
    </ul>
  </nav>
</header>
