{"cSpell.words": ["actioncable", "actiontext", "activerecord", "activestorage", "agentid", "autocorrect", "autocorrection", "autofocus", "behaviour", "behaviours", "bootsnap", "clinerules", "<PERSON>irls", "codingirlsclub", "conv", "copilotable", "corpid", "corpsecret", "cursorrules", "dbconsole", "demodulize", "do<PERSON>o", "echostr", "<PERSON><PERSON><PERSON>", "errmsg", "evenodd", "flowbite", "focusable", "geodb", "haml", "headimgurl", "healthcheck", "Hotwire's", "hotwired", "ical", "ILIKE", "importmap", "initdb", "jsapi", "KAMAL", "KBUPL", "knowledgebase", "langchain", "Langchainrb", "linecap", "linejoin", "markercluster", "messageable", "miniprogram", "mmdb", "msgtype", "nowrap", "ollama", "omniauth", "paramstr", "prefs", "prismjs", "Propshaft", "QDRANT", "referer", "rescuable", "rubocop", "sendcloud", "smsapi", "snsapi", "strftime", "stylesheet", "sundevilyang", "tencentyun", "testuser", "topcolor", "touser", "trix", "unarchived", "undp", "unionid", "upserting", "userinfo", "Validatable", "Vectorsearch", "versionable", "wechat", "wechats", "whodunnit", "windsurfrules"], "tailwindCSS.experimental.classRegex": [["class:\\s*\"([^\"]*)", "\"([^\"]*)\""], ["class:\\s*'([^']*)", "'([^']*)'"], ["class:\\s*{([^}]*)}", "([^}]*)"], ["className:\\s*\"([^\"]*)", "\"([^\"]*)\""], ["className:\\s*'([^']*)", "'([^']*)'"], ["className:\\s*{([^}]*)}", "([^}]*)"]], "tailwindCSS.includeLanguages": {"ruby": "html", "erb": "html", "javascript": "javascript", "typescript": "javascript"}, "files.exclude": {"logseq/*": true}}