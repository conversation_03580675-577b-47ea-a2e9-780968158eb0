# frozen_string_literal: true

require "langchain/llm/doubao"
require_relative "../../app/services/ai/llm_service"

LangchainrbRails.configure do |config|
  config.vectorsearch = Langchain::Vectorsearch::Qdrant.new(
    url: Ai::LLMService.qdrant_url,
    api_key: Ai::LLMService.qdrant_api_key,
    llm: Ai::LLMService.create_llm,
    index_name: "ai_pro_cgc"
  )
end

private

def qdrant_url
  Rails.env.production? ? Rails.application.credentials.dig(:qdrant, :url) : "http://localhost:6333"
end

def qdrant_api_key
  Rails.env.production? ? Rails.application.credentials.dig(:qdrant, :api_key) : nil
end
