<div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { items: [[t('ai.conversations.title'), ai_conversations_path], [t('ai.conversations.new'), nil]] } %>

        <div class="mb-6">
          <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white transition-colors duration-200"><%= t('ai.conversations.new') %></h1>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-8 border border-gray-200 dark:border-gray-700 transition-all duration-200">
          <%= form_with model: @conversation, url: ai_conversations_path, scope: :conversation, class: "space-y-8" do |f| %>
            <% if @conversation.errors.any? %>
              <div class="p-4 mb-6 text-sm text-red-800 dark:text-red-300 rounded-lg bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 transition-colors duration-200">
                <div class="flex items-center mb-1">
                  <svg class="w-5 h-5 mr-2 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                  <h2 class="font-medium"><%= t('posts.form.error', count: @conversation.errors.count) %></h2>
                </div>
                <ul class="mt-1.5 list-disc list-inside ml-6">
                  <% @conversation.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <div class="space-y-2">
              <%= f.label :title, t('posts.fields.title'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
              <%= f.text_field :title, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: t('ai.conversations.chat.assistant_prompt') %>
              <p class="mt-1 text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200"><%= t('ai.conversations.chat.assistant_prompt') %></p>
            </div>

            <div class="space-y-4">
              <label class="block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200"><%= t('ai.conversations.chat.assistant_selection') %></label>
              <p class="text-sm text-gray-500 dark:text-gray-400 transition-colors duration-200"><%= t('ai.conversations.chat.assistant_prompt') %></p>

              <div class="mt-4 grid grid-cols-1 lg:grid-cols-2 gap-4">
                <% current_user.assistants.each do |assistant| %>
                  <div class="relative">
                    <%= f.radio_button :assistant_id, assistant.id, class: "peer sr-only", checked: assistant == current_user.assistants.first %>
                    <label for="conversation_assistant_id_<%= assistant.id %>" class="flex flex-col h-full p-5 border-2 border-gray-200 dark:border-gray-700 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 rounded-lg cursor-pointer peer-checked:shadow-md hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200">
                      <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400 mr-3 transition-colors duration-200">
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                          </svg>
                        </div>
                        <span class="text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200"><%= assistant.name %></span>
                      </div>

                      <% if assistant.instructions.present? %>
                        <p class="mt-3 text-sm text-gray-600 dark:text-gray-300 line-clamp-3 transition-colors duration-200">
                          <%= assistant.instructions %>
                        </p>
                      <% end %>

                      <div class="absolute top-4 right-4 w-5 h-5 rounded-full border-2 border-gray-300 dark:border-gray-600 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 peer-checked:bg-primary-500 dark:peer-checked:bg-primary-400 flex items-center justify-center transition-all duration-200">
                        <svg class="w-3 h-3 text-white opacity-0 peer-checked:opacity-100" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="flex justify-end pt-4">
              <%= f.submit t('ai.conversations.new'), class: "inline-flex items-center justify-center text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 dark:focus:ring-primary-800 font-medium rounded-lg text-base px-6 py-3.5 text-center shadow-xs hover:shadow-md transition-all duration-200" %>
            </div>
          <% end %>
        </div>
      </div>

      <div class="col-span-1 mt-4 xl:mt-0">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 transition-colors duration-200"><%= t('ai.assistants.tips.title') %></h3>
          <ul class="space-y-3 text-gray-600 dark:text-gray-300 transition-colors duration-200">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('ai.assistants.tips.basic.name') %></span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('ai.assistants.tips.tools.custom') %></span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('ai.assistants.tips.practices.items')[3] %></span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('ai.assistants.tips.practices.items')[0] %></span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
