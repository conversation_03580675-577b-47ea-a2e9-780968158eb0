FactoryBot.define do
  factory :team do
    sequence(:name) { |n| "Team #{n}" }
    category { %w[tech product operations marketing].sample }
    description { "Team Description #{SecureRandom.hex(4)}" }
    status { Team::STATUSES.sample }
    avatar { "https://example.com/avatar.jpg" }

    trait :active do
      status { 'active' }
    end

    trait :inactive do
      status { 'inactive' }
    end

    # Team with members
    trait :with_members do
      transient do
        members_count { 3 }
        admin_count { 1 }
      end

      after(:create) do |team, evaluator|
        create_list(:team_member, evaluator.members_count, :member, team: team)
        create_list(:team_member, evaluator.admin_count, :admin, team: team)
      end
    end

    # Team with knowledge bases
    trait :with_knowledge_bases do
      transient do
        kb_count { 2 }
      end

      after(:create) do |team, evaluator|
        create_list(:knowledge_base, evaluator.kb_count, :team, team: team)
      end
    end
  end
end
