<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-(--breakpoint-xl) lg:py-16">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= t('base_units.index.title') %></h1>
        <p class="mt-2 text-lg text-gray-500 dark:text-gray-400"><%= t('base_units.index.subtitle') %></p>
      </div>

      <div class="flex items-center gap-4" data-controller="upload" data-upload-url-value="/knowledge_bases/base_units" data-upload-form-data-value="<%= { custom_param: 'value' }.to_json %>">
        <button class="text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                data-action="click->upload#triggerFileInput">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          <%= t('common.actions.upload') %>
        </button>

        <input type="file"
               class="hidden"
               data-upload-target="fileInput"
               data-action="change->upload#handleFileSelect"
               accept="<%= BaseUnit::ALLOWED_TYPES.keys.join(',') %>"
               multiple>
      </div>
    </div>

    <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
      <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" class="px-6 py-3"><%= t('base_units.fields.name') %></th>
            <th scope="col" class="px-6 py-3"><%= t('base_units.fields.category') %></th>
            <th scope="col" class="px-6 py-3"><%= t('base_units.fields.size') %></th>
            <th scope="col" class="px-6 py-3"><%= t('base_units.fields.status') %></th>
            <th scope="col" class="px-6 py-3"><%= t('common.time_formats.created_at') %></th>
            <th scope="col" class="px-6 py-3 text-right"><%= t('common.actions.title') %></th>
          </tr>
        </thead>
        <tbody>
          <% @base_units.each do |base_unit| %>
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
              <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
                <%= base_unit.name %>
              </td>
              <td class="px-6 py-4"><%= BaseUnit::ALLOWED_TYPES[base_unit.content_type] %></td>
              <td class="px-6 py-4"><%= number_to_human_size(base_unit.file_size) %></td>
              <td class="px-6 py-4">
                <% if base_unit.status == 'ready' %>
                  <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
                    <%= t('base_units.status.ready') %>
                  </span>
                <% else %>
                  <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-yellow-900 dark:text-yellow-300">
                    <%= t('base_units.status.processing') %>
                  </span>
                <% end %>
              </td>
              <td class="px-6 py-4"><%= l base_unit.created_at, format: :short %></td>
              <td class="px-6 py-4">
                <div class="flex items-center justify-end gap-2">
                  <%= link_to base_unit_path(base_unit), class: "text-gray-500 hover:text-gray-900 dark:hover:text-white", title: t('base_units.actions.view') do %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  <% end %>

                  <%= link_to download_base_unit_path(base_unit), class: "text-gray-500 hover:text-gray-900 dark:hover:text-white", title: t('base_units.actions.download'), data: { turbo: false } do %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                  <% end %>

                  <% if base_unit.posts.exists? %>
                    <%= link_to post_path(base_unit.posts.first), class: "text-gray-500 hover:text-gray-900 dark:hover:text-white", title: t('base_units.actions.view_post') do %>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                      </svg>
                    <% end %>
                  <% else %>
                    <%= link_to convert_to_md_base_unit_path(base_unit), class: "text-gray-500 hover:text-gray-900 dark:hover:text-white", title: t('base_units.actions.convert_to_post') do %>
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M15 11l-3 3m0 0l-3-3m3 3V4m0 0h4"></path>
                      </svg>
                    <% end %>
                  <% end %>

                  <%= button_to base_unit_path(base_unit),
                      method: :delete,
                      class: "text-red-500 hover:text-red-700 dark:hover:text-red-400",
                      title: t('common.actions.delete'),
                      form: {
                        data: {
                          turbo_confirm: t('common.messages.confirm')
                        }
                      } do %>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
</section>
