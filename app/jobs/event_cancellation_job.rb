class EventCancellationJob < ApplicationJob
  queue_as :default

  def perform(event)
    begin
      # 向所有已注册用户发送取消通知
      notification_count = 0

      event.registered_users.each do |user|
        begin
          EventCancellationMailer.notification(user, event, "Event cancelled by organizer").deliver_later
          notification_count += 1
        rescue => e
          Rails.logger.error "Failed to send cancellation notification to user #{user.id}: #{e.message}"
        end
      end

      Rails.logger.info "Sent cancellation notifications for event #{event.id} to #{notification_count} users"
    rescue => e
      Rails.logger.error "Failed to send event cancellation notifications: #{e.message}"
    end
  end
end
