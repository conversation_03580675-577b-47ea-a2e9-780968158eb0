source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.1"
# The modern asset pipeline for Rails [https://github.com/rails/propshaft]
gem "propshaft"
# Use sqlite3 as the database for Active Record
gem "sqlite3", ">= 2.1"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
# Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"
# Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"
# Use Tailwind CSS [https://github.com/rails/tailwindcss-rails]
gem "tailwindcss-rails", "~> 4.3"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"

# Use Active Model has_secure_password [https://guides.rubyonrails.org/active_model_basics.html#securepassword]
gem "bcrypt", "~> 3.1.7"

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ windows jruby ]

# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cache"
gem "solid_queue"
gem "solid_cable"

# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false

# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false

# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false

# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "image_processing", "~> 1.14"
gem "kaminari"

# OmniAuth for third-party authentication
gem "omniauth", "~> 2.1", ">= 2.1.2"
gem "omniauth-rails_csrf_protection", "~> 1.0", ">= 1.0.2"
gem "omniauth-wechat-oauth2", "~> 0.2.3"
gem "omniauth-google-oauth2", "~> 1.2"
gem "omniauth-github", "~> 2.0"

gem "wechat"

group :development, :test do
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"

  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false

  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false

  gem "factory_bot_rails"
  gem "faker"
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  # System testing
  gem "capybara"
  gem "selenium-webdriver"
  gem "cucumber-rails", require: false

  # Database setup
  gem "database_cleaner"
  gem "db-query-matchers"

  # Unit testing
  gem "rspec-rails"
  gem "shoulda-matchers"
  gem "webmock"
end

# Langchain
gem "langchainrb_rails", git: "https://github.com/patterns-ai-core/langchainrb_rails.git"
gem "langchainrb", "~> 0.19.5"

# AI
gem "qdrant-ruby"
gem "ruby-openai", "~> 8.1"

# PDF
gem "pdf-reader", "~> 2.14"
# Word
gem "docx", "~> 0.9.1"

# HTTP
gem "faraday", "~> 2.13"

# Markdown
gem "redcarpet"

# AWS
gem "aws-sdk-s3"
gem "action_policy", "~> 0.7.3"

gem "flowbite", "~> 3.1"

gem "wikipedia-client", "~> 1.17"

gem "eqn", "~> 1.6"

# Admin interface
gem "administrate", "1.0.0.beta3"

gem "noticed", "~> 2.7"
