class Post < ApplicationRecord
  include LangchainrbRails::ActiveRecord::Hooks

  vectorsearch do
    payload do
      {
        id: id,
        user_id: user_id,
        title: title,
        status: status,
        content: content&.to_plain_text
      }
    end
  end

  def as_payload
    {
      id: id,
      user_id: user_id,
      title: title,
      status: status,
      content: content&.to_plain_text
    }
  end

  def as_vector
    <<~TEXT
      Title: #{title}
      Status: #{status}
      User: #{user_id}
      Content: #{content&.to_plain_text}
    TEXT
  end

  after_save :upsert_to_vectorsearch
  after_destroy :destroy_from_vectorsearch

  # 添加 concern 分离关注点
  include Publishable

  #  使用枚举替代字符串状态
  enum :status, { draft: "draft", published: "published" }

  belongs_to :user
  belongs_to :base_unit, optional: true
  has_many :knowledge_base_posts, dependent: :destroy
  has_many :knowledge_bases, through: :knowledge_base_posts

  has_rich_text :content

  # 添加回调验证
  validates :title, presence: true, length: { minimum: 3, maximum: 255 }
  validate :content_presence

  # 使用 scope 优化查询
  scope :recent, -> { order(created_at: :desc) }
  scope :published, -> { where(status: :published) }
  scope :drafts, -> { where(status: :draft) }
  scope :by_user, ->(user) { where(user: user) }

  private

  def content_presence
    errors.add(:content, "can't be blank") if content.blank?
  end
end
