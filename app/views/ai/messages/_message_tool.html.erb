<div id="message_<%= message.id %>" class="flex items-start gap-2.5 mb-4">
  <div class="w-8 h-8 rounded-full bg-secondary-100 dark:bg-secondary-900 flex items-center justify-center">
    <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
    </svg>
  </div>

  <div class="flex flex-col w-full max-w-[320px] leading-1.5 p-4 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-e-xl rounded-es-xl">
    <div class="flex items-center space-x-2 rtl:space-x-reverse">
      <% tool_usage = message.tool_usages.first %>

      <span class="text-sm font-semibold text-secondary-700 dark:text-secondary-300">
        <%= tool_usage&.function_name || "Tool" %>
      </span>

      <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
        <%= message.created_at.strftime("%H:%M") %>
      </span>

      <% if tool_usage %>
        <% status_color = case tool_usage.status
          when "success"
            "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
          when "failed"
            "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
          else
            "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
          end
        %>
        <span class="<%= status_color %> text-xs font-medium px-2.5 py-0.5 rounded-sm">
          <%= tool_usage.status %>
        </span>
      <% end %>
    </div>

    <% if message.content.present? %>
      <div class="format dark:format-invert max-w-none py-2.5">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          <% if tool_usage&.status == "success" %>
            <div class="font-medium mb-1">Tool Executed Result:</div>
          <% end %>
          <%= message.content %>
        </div>
      </div>
    <% else %>
      <div class="flex items-center py-2.5">
        <div class="animate-pulse flex space-x-2">
          <div class="h-2 w-2 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
          <div class="h-2 w-2 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
          <div class="h-2 w-2 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
        </div>
      </div>
    <% end %>

    <% if tool_usage&.arguments.present? %>
      <div class="mt-2">
        <div class="text-xs text-gray-600 dark:text-gray-400">
          <details>
            <summary class="cursor-pointer">View Parameters</summary>
            <pre class="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-sm overflow-x-auto">
<%= JSON.pretty_generate(tool_usage.arguments) %></pre>
          </details>
        </div>
      </div>
    <% end %>
  </div>
</div>
