<%# 订阅按钮 partial，可根据 plan 参数灵活调整样式和行为 %>
<%# 订阅按钮样式优化 %>
<% if plan[:plan_type].to_sym == :free %>
  <button type="button" class="w-full text-white bg-gray-500 hover:bg-gray-600 focus:ring-4 focus:outline-hidden focus:ring-gray-300 font-medium rounded-lg text-sm px-5 py-3 text-center transition-all duration-200 dark:text-white dark:focus:ring-gray-700 cursor-not-allowed opacity-75" disabled>
    当前方案
  </button>
<% else %>
  <%= form_with(url: registration_path, method: :post, data: { turbo: false }) do |f| %>
    <%= f.hidden_field :step, value: "subscribe" %>
    <%= f.hidden_field :plan_type, value: plan[:plan_type] %>
    <%= f.submit t('auth.registrations.pricing.get_started'),
      class: "w-full text-white bg-primary-600 hover:bg-primary-700 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-3 text-center transition-all duration-200 transform hover:scale-105 dark:text-white dark:focus:ring-primary-800" %>
  <% end %>
<% end %>
