FactoryBot.define do
  factory :team_resource_access do
    association :team_member
    resource { association :knowledge_base }
    resource_type { resource&.class&.name }
    resource_kind { 'knowledge_base' }
    role { 'viewer' }

    trait :editor do
      role { 'editor' }
    end

    trait :maintainer do
      role { 'maintainer' }
    end

    trait :for_knowledge_base do
      # 保持默认设置
    end

    trait :for_learning_atlas do
      resource { association :learning_atlas_garden }
      resource_kind { 'learning_atlas_garden' }
      role { 'student' }
    end
  end
end
