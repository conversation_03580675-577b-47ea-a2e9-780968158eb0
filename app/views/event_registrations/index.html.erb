<% content_for :title, t('events.registration.participants_list') %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="mb-4">
      <%= render partial: 'shared/breadcrumb', locals: { 
        items: [
          [t('events.index.title'), events_path], 
          [@event.title, event_path(@event)],
          [t('events.registration.participants'), nil]
        ] 
      } %>

      <div class="flex items-center justify-between mb-6">
        <div>
          <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white transition-colors duration-200">
            <%= t('events.registration.participants_list') %>
          </h1>
          <p class="mt-2 text-gray-600 dark:text-gray-400 transition-colors duration-200">
            <%= @event.title %> - <%= l(@event.start_time, format: :long) %>
          </p>
        </div>
        
        <%= link_to event_path(@event), 
            class: "inline-flex items-center justify-center text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 focus:ring-4 focus:outline-hidden focus:ring-gray-200 font-medium rounded-lg text-sm px-4 py-2.5 text-center dark:bg-gray-700 dark:hover:bg-gray-600 dark:focus:ring-gray-600 dark:text-gray-200 dark:border-gray-600 shadow-xs hover:shadow-md transition-all duration-200" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          <%= t('common.actions.back') %>
        <% end %>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
          <%= t('events.registration.total_registrations') %>
        </div>
        <div class="text-2xl font-bold text-gray-900 dark:text-white">
          <%= @registrations.count %>
          <% if @event.max_participants %>
            <span class="text-sm font-normal text-gray-500">/ <%= @event.max_participants %></span>
          <% end %>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
          <%= t('events.registration.confirmed') %>
        </div>
        <div class="text-2xl font-bold text-green-600 dark:text-green-400">
          <%= @registrations.where(status: 'confirmed').count %>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
          <%= t('events.registration.pending') %>
        </div>
        <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
          <%= @registrations.where(status: 'pending').count %>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
        <div class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
          <%= t('events.registration.total_revenue') %>
        </div>
        <div class="text-2xl font-bold text-primary-600 dark:text-primary-400">
          ¥<%= @registrations.sum(:amount_paid) %>
        </div>
      </div>
    </div>

    <!-- 参与者列表 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="p-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          <%= t('events.registration.participants') %>
        </h2>

        <div class="overflow-x-auto">
          <table class="w-full text-sm text-left">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
              <tr>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registration.participant_info') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registration.registered_at') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registration.amount_paid') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registration.status') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('common.actions.actions') %>
                </th>
              </tr>
            </thead>
            <tbody>
              <% @registrations.each do |registration| %>
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td class="px-6 py-4">
                    <div class="flex items-center">
                      <%= render 'shared/avatar', user: registration.user, size: 'sm' %>
                      <div class="ml-3">
                        <div class="font-medium text-gray-900 dark:text-white">
                          <%= registration.user.username || registration.user.email_address %>
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                          <%= registration.user.email_address %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <%= l(registration.registered_at, format: :short) %>
                  </td>
                  <td class="px-6 py-4">
                    ¥<%= registration.amount_paid %>
                  </td>
                  <td class="px-6 py-4">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                      <%= case registration.status
                          when 'confirmed' then 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          when 'pending' then 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                          when 'cancelled' then 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          else 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                          end %>">
                      <%= t("events.registration.status.#{registration.status}") %>
                    </span>
                  </td>
                  <td class="px-6 py-4">
                    <div class="flex space-x-2">
                      <% if registration.pending? %>
                        <%= link_to confirm_event_event_registration_path(@event, registration), 
                            method: :patch,
                            class: "text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 font-medium text-sm" do %>
                          <%= t('common.actions.confirm') %>
                        <% end %>
                      <% end %>
                      
                      <% unless registration.cancelled? %>
                        <%= link_to cancel_event_event_registration_path(@event, registration), 
                            method: :patch,
                            data: { confirm: t('events.confirmations.cancel_registration') },
                            class: "text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 font-medium text-sm" do %>
                          <%= t('common.actions.cancel') %>
                        <% end %>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <% if @registrations.empty? %>
          <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              <%= t('events.registration.no_participants') %>
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              <%= t('events.registration.wait_for_registrations') %>
            </p>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div> 