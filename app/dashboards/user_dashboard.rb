require "administrate/base_dashboard"

class UserDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    accessible_knowledge_bases: Field::<PERSON><PERSON><PERSON>,
    admin: Field::<PERSON><PERSON><PERSON>,
    assistants: Field::<PERSON><PERSON><PERSON>,
    avatar_attachment: Field::<PERSON><PERSON>ne,
    avatar_blob: Field::HasOne,
    base_units: Field::Has<PERSON>any,
    conversations: Field::<PERSON><PERSON><PERSON>,
    email_address: Field::String,
    event_registrations: Field::<PERSON><PERSON><PERSON>,
    gender: Field::String,
    hobbies: Field::String.with_options(searchable: false),
    knowledge_bases: Field::<PERSON><PERSON>any,
    location: Field::String,
    omni_auth_identities: Field::<PERSON><PERSON>any,
    password_digest: Field::String,
    phone_number: Field::String,
    phone_verified: Field::<PERSON><PERSON><PERSON>,
    posts: Field::<PERSON><PERSON><PERSON>,
    preferences: Field::String.with_options(searchable: false),
    prompts: Field::<PERSON><PERSON><PERSON>,
    registered_events: Field::<PERSON><PERSON><PERSON>,
    sessions: Field::<PERSON><PERSON>any,
    subscriptions: Field::<PERSON><PERSON><PERSON>,
    team_members: Field::<PERSON><PERSON><PERSON>,
    team_resource_accesses: Field::HasMany,
    teams: Field::HasMany,
    username: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    username
    email_address
    admin
    phone_verified
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    username
    email_address
    admin
    phone_number
    phone_verified
    gender
    location
    hobbies
    avatar_attachment
    teams
    knowledge_bases
    assistants
    posts
    conversations
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    username
    email_address
    admin
    phone_number
    phone_verified
    gender
    location
    hobbies
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {
    admin: ->(resources) { resources.where(admin: true) },
    regular: ->(resources) { resources.where(admin: false) },
    verified: ->(resources) { resources.where(phone_verified: true) }
  }.freeze

  # Overwrite this method to customize how users are displayed
  # across all pages of the admin dashboard.
  def display_resource(user)
    "#{user.username} (#{user.email_address})"
  end
end
