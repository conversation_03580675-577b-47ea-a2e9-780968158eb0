// tailwindcss-animate@1.0.7 downloaded from https://ga.jspm.io/npm:tailwindcss-animate@1.0.7/index.js

import*as t from"tailwindcss/plugin";var a="default"in t?t.default:t;var e={};const i=a;function filterDefault(t){return Object.fromEntries(Object.entries(t).filter((([t])=>"DEFAULT"!==t)))}e=i((({addUtilities:t,matchUtilities:a,theme:e})=>{t({"@keyframes enter":e("keyframes.enter"),"@keyframes exit":e("keyframes.exit"),".animate-in":{animationName:"enter",animationDuration:e("animationDuration.DEFAULT"),"--tw-enter-opacity":"initial","--tw-enter-scale":"initial","--tw-enter-rotate":"initial","--tw-enter-translate-x":"initial","--tw-enter-translate-y":"initial"},".animate-out":{animationName:"exit",animationDuration:e("animationDuration.DEFAULT"),"--tw-exit-opacity":"initial","--tw-exit-scale":"initial","--tw-exit-rotate":"initial","--tw-exit-translate-x":"initial","--tw-exit-translate-y":"initial"}});a({"fade-in":t=>({"--tw-enter-opacity":t}),"fade-out":t=>({"--tw-exit-opacity":t})},{values:e("animationOpacity")});a({"zoom-in":t=>({"--tw-enter-scale":t}),"zoom-out":t=>({"--tw-exit-scale":t})},{values:e("animationScale")});a({"spin-in":t=>({"--tw-enter-rotate":t}),"spin-out":t=>({"--tw-exit-rotate":t})},{values:e("animationRotate")});a({"slide-in-from-top":t=>({"--tw-enter-translate-y":`-${t}`}),"slide-in-from-bottom":t=>({"--tw-enter-translate-y":t}),"slide-in-from-left":t=>({"--tw-enter-translate-x":`-${t}`}),"slide-in-from-right":t=>({"--tw-enter-translate-x":t}),"slide-out-to-top":t=>({"--tw-exit-translate-y":`-${t}`}),"slide-out-to-bottom":t=>({"--tw-exit-translate-y":t}),"slide-out-to-left":t=>({"--tw-exit-translate-x":`-${t}`}),"slide-out-to-right":t=>({"--tw-exit-translate-x":t})},{values:e("animationTranslate")});a({duration:t=>({animationDuration:t})},{values:filterDefault(e("animationDuration"))});a({delay:t=>({animationDelay:t})},{values:e("animationDelay")});a({ease:t=>({animationTimingFunction:t})},{values:filterDefault(e("animationTimingFunction"))});t({".running":{animationPlayState:"running"},".paused":{animationPlayState:"paused"}});a({"fill-mode":t=>({animationFillMode:t})},{values:e("animationFillMode")});a({direction:t=>({animationDirection:t})},{values:e("animationDirection")});a({repeat:t=>({animationIterationCount:t})},{values:e("animationRepeat")})}),{theme:{extend:{animationDelay:({theme:t})=>({...t("transitionDelay")}),animationDuration:({theme:t})=>({0:"0ms",...t("transitionDuration")}),animationTimingFunction:({theme:t})=>({...t("transitionTimingFunction")}),animationFillMode:{none:"none",forwards:"forwards",backwards:"backwards",both:"both"},animationDirection:{normal:"normal",reverse:"reverse",alternate:"alternate","alternate-reverse":"alternate-reverse"},animationOpacity:({theme:t})=>({DEFAULT:0,...t("opacity")}),animationTranslate:({theme:t})=>({DEFAULT:"100%",...t("translate")}),animationScale:({theme:t})=>({DEFAULT:0,...t("scale")}),animationRotate:({theme:t})=>({DEFAULT:"30deg",...t("rotate")}),animationRepeat:{0:"0",1:"1",infinite:"infinite"},keyframes:{enter:{from:{opacity:"var(--tw-enter-opacity, 1)",transform:"translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))"}},exit:{to:{opacity:"var(--tw-exit-opacity, 1)",transform:"translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))"}}}}}});var n=e;export{n as default};

