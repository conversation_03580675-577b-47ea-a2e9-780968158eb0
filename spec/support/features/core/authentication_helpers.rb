# frozen_string_literal: true

module <PERSON><PERSON><PERSON><PERSON>
  def sign_in(user)
    visit sign_in_path
    fill_in I18n.t('auth.sessions.email'), with: user.email_address
    fill_in I18n.t('auth.sessions.password'), with: user.password
    click_button I18n.t('auth.sign_in')
    # Check for the main content area which should exist on dashboard pages
    expect(page).to have_selector('main', wait: 5) # Wait up to 5 seconds for main element
  end


  def sign_out
    if page.has_button?(I18n.t('auth.sign_out'))
      click_button I18n.t('auth.sign_out')
    elsif page.has_css?('form.button_to[action="/sign_out"]')
      find('form.button_to[action="/sign_out"] button').click
    else
      # 备用方案，直接设置 Current.session 为 nil
      Current.session = nil
      visit root_path # 确保重新加载页面来反映登出状态
    end

    # 验证登出成功
    expect(page).not_to have_content(I18n.t('sidebar.main.dashboard')), I18n.t('auth.sign_out_failed')
  end
end
