<div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-xs dark:border-gray-700 sm:p-6 dark:bg-gray-800">
  <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white pb-2"><%= t('ai.assistants.tips.title') %></h3>
  </div>
  
  <div class="space-y-4 text-sm text-gray-500 dark:text-gray-400">
    <!-- 基本设置提示 -->
    <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:border-gray-700 dark:bg-gray-800">
      <h4 class="mb-2 text-base font-medium text-gray-900 dark:text-white"><%= t('ai.assistants.tips.basic.title') %></h4>
      <ul class="list-disc list-inside space-y-2">
        <li><span class="font-medium"><%= t('ai.assistants.fields.name') %></span>: <%= t('ai.assistants.tips.basic.name') %></li>
        <li><span class="font-medium"><%= t('ai.assistants.fields.instructions') %></span>: <%= t('ai.assistants.tips.basic.instructions') %></li>
      </ul>
    </div>

    <!-- Prompts 提示 -->
    <div class="p-4 bg-indigo-50 border border-indigo-200 rounded-lg dark:border-indigo-900 dark:bg-indigo-900">
      <h4 class="mb-2 text-base font-medium text-indigo-900 dark:text-indigo-300"><%= t('ai.assistants.tips.prompts.title') %></h4>
      <ul class="list-disc list-inside space-y-2 text-indigo-800 dark:text-indigo-400">
        <li><span class="font-medium"><%= t('ai.assistants.tips.prompts.select').split(':').first %></span>: <%= t('ai.assistants.tips.prompts.select').split(':').last %></li>
        <li><span class="font-medium"><%= t('ai.assistants.tips.prompts.combine').split(':').first %></span>: <%= t('ai.assistants.tips.prompts.combine').split(':').last %></li>
        <li><span class="font-medium"><%= t('ai.assistants.tips.prompts.customize').split(':').first %></span>: <%= t('ai.assistants.tips.prompts.customize').split(':').last %></li>
      </ul>
    </div>

    <!-- 工具选择提示 -->
    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg dark:border-blue-900 dark:bg-blue-900">
      <h4 class="mb-2 text-base font-medium text-blue-900 dark:text-blue-300"><%= t('ai.assistants.tips.tools.title') %></h4>
      <ul class="space-y-2 text-blue-800 dark:text-blue-400">
        <li><span class="font-medium"><%= t('ai.assistants.tips.tools.quick_setup').split(':').first %></span>: <%= t('ai.assistants.tips.tools.quick_setup').split(':').last %></li>
        <li><span class="font-medium"><%= t('ai.assistants.tips.tools.custom').split(':').first %></span>: <%= t('ai.assistants.tips.tools.custom').split(':').last %></li>
        <li><span class="font-medium"><%= t('ai.assistants.tips.tools.management').split(':').first %></span>: <%= t('ai.assistants.tips.tools.management').split(':').last %></li>
      </ul>
    </div>

    <!-- 使用模式说明 -->
    <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg dark:border-yellow-900 dark:bg-yellow-900">
      <h4 class="mb-2 text-base font-medium text-yellow-900 dark:text-yellow-300"><%= t('ai.assistants.tips.modes.title') %></h4>
      <ul class="list-disc list-inside space-y-2 text-yellow-800 dark:text-yellow-400">
        <li><span class="font-medium"><%= t('ai.assistants.tips.modes.auto').split(':').first %></span>: <%= t('ai.assistants.tips.modes.auto').split(':').last %></li>
        <li><span class="font-medium"><%= t('ai.assistants.tips.modes.none').split(':').first %></span>: <%= t('ai.assistants.tips.modes.none').split(':').last %></li>
        <li><span class="font-medium"><%= t('ai.assistants.tips.modes.any').split(':').first %></span>: <%= t('ai.assistants.tips.modes.any').split(':').last %></li>
      </ul>
    </div>

    <!-- 最佳实践 -->
    <div class="p-4 bg-green-50 border border-green-200 rounded-lg dark:border-green-900 dark:bg-green-900">
      <h4 class="mb-2 text-base font-medium text-green-900 dark:text-green-300"><%= t('ai.assistants.tips.practices.title') %></h4>
      <ul class="list-disc list-inside space-y-2 text-green-800 dark:text-green-400">
        <% t('ai.assistants.tips.practices.items').each do |item| %>
          <li><%= item %></li>
        <% end %>
      </ul>
    </div>
  </div>
</div>
