class BaseUnitPolicy < ApplicationPolicy
  pre_check :admin_or_owner_or_shared?

  def show?
    true # 前置检查已处理
  end

  def update?
    admin? || record.user == user
  end

  def destroy?
    admin? || record.user == user
  end

  # 定义作用域过滤规则
  relation_scope do |relation|
    return relation if user&.admin?  # Admin可以看到所有基础单元
    next relation.none unless user

    # 用户可以看到自己的和共享给自己的基础单元
    relation.where(user_id: user.id)
            .or(relation.joins(:shared_base_units).where(shared_base_units: { user_id: user.id }))
            .distinct
  end

  private

  def admin_or_owner_or_shared?
    return true if admin?
    return true if record.user == user
    return true if record.shared_with?(user)

    deny! :access_denied, "No access to this resource"
  end
end
