require 'rails_helper'

RSpec.describe SystemNotifier, type: :notifier do
  describe 'notification methods' do
    let(:user) { create(:user) }
    let(:message_text) { "系统维护通知" }
    
    before do
      SystemNotifier.with(message: message_text).deliver(user)
    end
    
    let(:notification) { user.notifications.last }

    describe '#message' do
      it 'returns the system message from params' do
        expect(notification.message).to eq(message_text)
      end
    end

    describe '#url' do
      it 'returns the root path' do
        expect(notification.url).to eq(Rails.application.routes.url_helpers.root_path)
      end
    end

    describe '#avatar_user' do
      it 'returns nil for system notifications' do
        expect(notification.avatar_user).to be_nil
      end
    end
  end

  describe 'delivery' do
    let(:user) { create(:user) }
    let(:message_text) { "系统维护将在今晚进行" }

    context 'when delivering to a single user' do
      it 'creates a notification record' do
        expect {
          SystemNotifier.with(message: message_text).deliver(user)
        }.to change { user.notifications.count }.by(1)
      end

      it 'stores the correct message in params' do
        SystemNotifier.with(message: message_text).deliver(user)
        
        notification = user.notifications.last
        expect(notification.params[:message]).to eq(message_text)
      end
    end

    context 'when delivering to multiple users' do
      let(:users) { create_list(:user, 3) }

      it 'creates notifications for all users' do
        expect {
          SystemNotifier.with(message: message_text).deliver(users)
        }.to change { Noticed::Notification.count }.by(3)
      end
    end

    context 'email delivery conditions' do
      let(:user_with_email_enabled) { create(:user) }
      let(:user_with_email_disabled) { create(:user) }

      before do
        # 设置用户的通知偏好 - 正确的结构
        user_with_email_enabled.update_preferences({
          "notification" => {
            "email" => true,
            "system" => { "email" => true }
          }
        })
        
        user_with_email_disabled.update_preferences({
          "notification" => {
            "email" => true,
            "system" => { "email" => false }
          }
        })
      end

      it 'checks email notification preference correctly for enabled user' do
        expect(user_with_email_enabled.notification_enabled?(:system, :email)).to be true
      end

      it 'checks email notification preference correctly for disabled user' do
        expect(user_with_email_disabled.notification_enabled?(:system, :email)).to be false
      end
    end
  end

  describe 'ActionCable delivery configuration' do
    it 'has ActionCable delivery method configured' do
      delivery_methods = SystemNotifier.delivery_methods
      action_cable_method = delivery_methods.find { |method| method.last.name == :action_cable }
      
      expect(action_cable_method).to be_present
      expect(action_cable_method.last.config[:channel]).to eq("NotificationChannel")
    end
  end

  describe 'validation' do
    it 'requires message parameter' do
      expect {
        SystemNotifier.with({}).deliver(create(:user))
      }.to raise_error(Noticed::ValidationError)
    end
    
    it 'allows valid message parameter' do
      expect {
        SystemNotifier.with(message: "Valid message").deliver(create(:user))
      }.not_to raise_error
    end
  end
end
