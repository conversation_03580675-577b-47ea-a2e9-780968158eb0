class Sessions::OmniAuthsController < ApplicationController
  allow_unauthenticated_access only: [ :create, :failure ]

  def create
    auth = request.env["omniauth.auth"]
    uid = auth["uid"]
    provider = auth["provider"]
    omniauth_params = request.env["omniauth.params"] || {}
    # Determine a post-authentication redirect path
    if authenticated?
      # 当用户已登录时，优先使用传入的 origin_path；若无则默认跳转到设置页面
      redirect_path = omniauth_params["origin_path"].presence || settings_users_path
    else
      # 未登录用户默认跳转到首页
      redirect_path = omniauth_params["origin_path"].presence || root_path
    end

    identity = OmniAuthIdentity.find_by_auth(auth)

    if authenticated?
      # --- User is logged in: Link account ---
      if identity.nil?
        # No identity found for this provider/uid, create one for the current user
        OmniAuthIdentity.create_from_auth(auth, Current.user)  # Allow the user model to update itself with new info (e.g., location, gender)
        Current.user.signed_in_with_oauth(auth)
        redirect_to redirect_path, notice: t("auth.omniauth.linked", provider: provider.humanize)
      elsif Current.user == identity.user
        # Identity found and already linked to the current user
        # Optionally update user info from OAuth provider again
        Current.user.signed_in_with_oauth(auth)
        redirect_to redirect_path, notice: t("auth.omniauth.already_linked", provider: provider.humanize)
      else
        # Identity found but linked to a different user - this shouldn't happen if UIDs are unique per provider
        # Log this potential issue
        Rails.logger.warn "OAuth Mismatch: Provider #{provider} UID #{uid} is linked to user #{identity.user_id}, but current user is #{Current.user.id}"
        redirect_to redirect_path, alert: t("auth.omniauth.mismatch", provider: provider.humanize)
      end
    else
      # --- User is not logged in: Sign in or Sign up ---
      if identity
        # Identity found, sign in the associated user
        user = identity.user
        # Optionally update user info from OAuth provider on sign-in
        user.signed_in_with_oauth(auth)
        start_new_session_for user, source: provider
        redirect_to redirect_path, notice: t("auth.omniauth.signed_in", provider: provider.humanize)
      else
        # No identity found, try to find a user by email or create a new user
        user = nil
        # Try finding a user by email ONLY if OAuth provides email
        if auth.info.email.present?
          user = User.find_by(email_address: auth.info.email)
        end

        if user
          # User found by email, link the new identity and sign in
          OmniAuthIdentity.create_from_auth(auth, user)
          user.signed_in_with_oauth(auth) # Update user attributes
          start_new_session_for user, source: provider
          redirect_to redirect_path, notice: t("auth.omniauth.signed_in", provider: provider.humanize)
        else
          # No user found by email (or email wasn't provided), creates a new user
          user = User.create_from_oauth(auth) # This now handles missing email
          if user&.persisted?
            # User created successfully, create identity and sign in
            OmniAuthIdentity.create_from_auth(auth, user)
            # No need to call signed_in_with_oauth again, create_from_oauth already called assign_attributes_from_auth
            start_new_session_for user, source: provider
            redirect_to redirect_path, notice: t("auth.omniauth.signed_up_and_in", provider: provider.humanize) # Use a different notice for sign-up
          else
            # User creation failed (validation errors, etc.)
            # Log the specific errors if available from the model (create_from_oauth now returns nil on failure)
            Rails.logger.error "OAuth user creation failed for provider #{provider}, UID #{uid}. Errors: #{user&.errors&.full_messages&.join(", ")}"
            redirect_to new_registration_path, alert: t("auth.omniauth.creation_failed", provider: provider.humanize)
          end
        end
      end
    end
  end


  def failure
    provider = params[:strategy] # OmniAuth sets the failed strategy in params
    redirect_to new_session_path, alert: t("auth.omniauth.failure", provider: provider&.humanize || "Authentication")
  end

  # New action to unbind an OAuth provider
  def unbind
    provider = params[:provider]
    identity = Current.user.omni_auth_identities.find_by(provider: provider)

    if identity.nil?
      redirect_to settings_users_path, alert: t("users.settings.disconnect_failed_not_found", provider: provider.humanize)
    elsif !Current.user.can_unbind_oauth?(provider)
      redirect_to settings_users_path, alert: t("users.settings.cannot_disconnect")
    elsif identity.destroy
      redirect_to settings_users_path, notice: t("users.settings.disconnected_successfully", provider: provider.humanize)
    else
      # Log error if destroy fails unexpectedly
      Rails.logger.error "Failed to unbind OAuth provider #{provider} for user #{Current.user.id}. Errors: #{identity.errors.full_messages.join(", ")}"
      redirect_to settings_users_path, alert: t("users.settings.disconnect_failed", provider: provider.humanize)
    end
  end
end
