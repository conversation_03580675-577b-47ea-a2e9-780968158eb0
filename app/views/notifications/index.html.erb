<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <!-- 面包屑导航 -->
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render 'shared/breadcrumb', items: { t('notifications.breadcrumbs.account') => settings_users_path, t('notifications.breadcrumbs.notifications') => nil } %>

        <!-- 页面标题 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white"><%= t('notifications.index.title') %></h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= t('notifications.index.subtitle') %></p>
          </div>
          <div class="flex items-center space-x-4 mt-4 md:mt-0">
            <% if @unread_count.positive? %>
              <%= button_to t('notifications.index.actions.mark_all'), mark_all_notifications_path,
                    method: :patch,
                    class: "inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-lg",
                    data: { turbo_confirm: t('notifications.index.confirm_mark_all') } %>
            <% end %>
          </div>
        </div>
        
        <!-- 过滤按钮 -->
        <div class="flex items-center space-x-1 mb-6">
          <%= link_to notifications_path(filter: 'all'), 
                      class: "inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{
                        @filter == 'all' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }" do %>
            <%= t('notifications.index.filters.all') %>
            <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-gray-600 bg-gray-200 rounded-full dark:bg-gray-600 dark:text-gray-200">
              <%= @total_count %>
            </span>
          <% end %>
          
          <%= link_to notifications_path(filter: 'unread'), 
                      class: "inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{
                        @filter == 'unread' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }" do %>
            <%= t('notifications.index.filters.unread') %>
            <% if @unread_count > 0 %>
              <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-primary-600 bg-primary-100 rounded-full dark:bg-primary-800 dark:text-primary-200">
                <%= @unread_count %>
              </span>
            <% end %>
          <% end %>
          
          <%= link_to notifications_path(filter: 'read'), 
                      class: "inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 #{
                        @filter == 'read' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }" do %>
            <%= t('notifications.index.filters.read') %>
            <% if @read_count > 0 %>
              <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-gray-600 bg-gray-200 rounded-full dark:bg-gray-600 dark:text-gray-200">
                <%= @read_count %>
              </span>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- 通知列表 -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xs">
      <% if @notifications.any? %>
        <% @notifications_by_date.each do |date_group, notifications| %>
          <!-- 日期分组标题 -->
          <div class="px-6 py-3 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white"><%= date_group %></h3>
          </div>
          
          <div class="divide-y divide-gray-200 dark:divide-gray-700">
            <% notifications.each_with_index do |notification, index| %>
          <% 
            # Based on notification type, get relevant information and icon
            icon_bg_color = ['bg-primary-700', 'bg-gray-900', 'bg-red-600', 'bg-green-400', 'bg-purple-500'][index % 5]
            
            # Check if notification is read
            is_read = notification.read?
            
            if notification.event.type == 'SystemNotifier'
              message = notification.event.params[:message]
              icon_svg = '<path d="M8.707 7.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l2-2a1 1 0 00-1.414-1.414L11 7.586V3a1 1 0 10-2 0v4.586l-.293-.293z"></path><path d="M3 5a2 2 0 012-2h1a1 1 0 010 2H5v7h2l1 2h4l1-2h2V5h-1a1 1 0 110-2h1a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"></path>'
              avatar_user = nil
            elsif notification.event.type == 'EventReminderNotifier'
              event = Event.find(notification.event.params[:event]) rescue nil
              message_parts = case notification.event.params[:reminder_type]
                        when 'day_before'
                          { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.day_before') }
                        when 'hour_before'
                          { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.hour_before') }
                        when 'starting_soon'
                          { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.starting_soon') }
                        else
                          { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.reminder') }
                        end
              message = "<span class=\"font-semibold #{is_read ? 'text-gray-700 dark:text-gray-300' : 'text-gray-900 dark:text-white'}\">#{message_parts[:bold]}</span>#{message_parts[:text]}"
              icon_svg = '<path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>'
              avatar_user = event&.user
            else
              message = t('notifications.show.types.notification')
              icon_svg = '<path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>'
              avatar_user = nil
            end
          %>
          
          <a href="<%= notification_path(notification) %>" 
             class="flex px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 <%= is_read ? 'bg-white dark:bg-gray-800' : 'bg-primary-50 dark:bg-primary-950/20' %> border-l-4 <%= is_read ? 'border-transparent' : 'border-primary-500' %>"
             data-action="click->notifications#preventDoubleClick">
            <div class="flex-shrink-0 relative">
              <% if avatar_user %>
                <%= render 'shared/avatar', user: avatar_user, size: 11, alt_text: "User avatar", css_class: "rounded-full w-11 h-11 #{is_read ? 'opacity-75' : ''}" %>
              <% else %>
                <div class="inline-flex items-center justify-center w-11 h-11 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600 #{is_read ? 'opacity-75' : ''}">
                  <span class="font-medium text-gray-600 dark:text-gray-300">SYS</span>
                </div>
              <% end %>
              <div class="absolute flex items-center justify-center w-5 h-5 -mt-5 ml-6 border border-white rounded-full <%= icon_bg_color %> dark:border-gray-700 #{is_read ? 'opacity-75' : ''}">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <%= icon_svg.html_safe %>
                </svg>
              </div>
              <!-- 未读状态指示器 -->
              <% unless is_read %>
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-primary-500 rounded-full border-2 border-white dark:border-gray-800"></div>
              <% end %>
            </div>
            <div class="w-full pl-3">
              <div class="<%= is_read ? 'text-gray-500 dark:text-gray-400' : 'text-gray-700 dark:text-gray-200' %> font-normal text-sm mb-1.5">
                <%= message.html_safe %>
              </div>
              <div class="flex items-center justify-between">
                <div class="text-xs font-medium <%= is_read ? 'text-gray-400 dark:text-gray-500' : 'text-primary-700 dark:text-primary-400' %>">
                  <%= t('common.time_ago', time: time_ago_in_words(notification.created_at)) %>
                </div>
                <% unless is_read %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-800 dark:text-primary-100">
                    <%= t('notifications.index.status.new') %>
                  </span>
                <% end %>
              </div>
            </div>
          </a>
            <% end %>
          </div>
        <% end %>
      <% else %>
        <div class="px-6 py-16 text-center text-gray-500 dark:text-gray-400">
          <% if @filter == 'unread' %>
            <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4"><%= t('notifications.index.empty_states.no_unread') %></h3>
            <p class="text-gray-500 dark:text-gray-400 mt-1"><%= t('notifications.index.empty_states.no_unread_message') %></p>
          <% elsif @filter == 'read' %>
            <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4"><%= t('notifications.index.empty_states.no_read') %></h3>
            <p class="text-gray-500 dark:text-gray-400 mt-1"><%= t('notifications.index.empty_states.no_read_message') %></p>
          <% else %>
            <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4"><%= t('notifications.index.empty_states.no_notifications') %></h3>
            <p class="text-gray-500 dark:text-gray-400 mt-1"><%= t('notifications.index.empty_states.no_notifications_message') %></p>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
</div>
