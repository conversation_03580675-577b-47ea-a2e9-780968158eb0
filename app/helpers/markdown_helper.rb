module MarkdownHelper
  require "redcarpet"

  # 自定义渲染器，继承 Redcarpet::Render::HTML
  class CustomHTMLRenderer < Redcarpet::Render::HTML
    # 重写 block_code 方法以支持 PrismJS 插件
    def block_code(code, language)
      lang = language.present? ? language.downcase : "plaintext"
      code_lang_class = "language-#{lang}"
      pre_classes = []

      # 为 Treeview 添加特定类，其他代码块添加行号类
      if lang == "treeview"
        pre_classes << "language-treeview" # Treeview 插件需要 pre 也有 language 类
      else
        pre_classes << "line-numbers" # 为其他语言启用行号
      end

      # 使用 ERB::Util.html_escape 对代码进行转义
      escaped_code = ERB::Util.html_escape(code)

      # 返回带有正确类的 HTML 结构
      # Toolbar 插件会自动附加到 pre 元素
      # Show Language 插件会读取 code 元素的类
      "<pre class=\"#{pre_classes.join(" ")}\"><code class=\"#{code_lang_class}\">#{escaped_code}</code></pre>"
    end

    # 可选：重写 codespan 方法以处理行内代码（如果需要特定类）
    # def codespan(code)
    #   escaped_code = ERB::Util.html_escape(code)
    #   "<code class=\"inline-code\">#{escaped_code}</code>" # Example class
    # end
  end

  def markdown?(text)
    return false if text.blank?

    # 检查常见的 Markdown 特征
    markdown_patterns = [
      /^#+ /, # 标题
      /\[.+?\]\(.+?\)/, # 链接
      /`[^`]+`/, # 行内代码
      /```[\s\S]*?```/, # 代码块
      /\*\*.+?\*\*/, # 粗体
      /\*.+?\*/, # 斜体
      /^\s*[-*+] /, # 无序列表
      /^\s*\d+\. /, # 有序列表
      /^\s*>/, # 引用
      /\|.+\|.+\|/, # 表格
      /^-{3,}$/ # 分隔线
    ]

    markdown_patterns.any? { |pattern| text.match?(pattern) }
  end

  def markdown(text)
    return "" if text.blank?

    # 使用我们自定义的渲染器
    renderer = CustomHTMLRenderer.new(
      hard_wrap: true,
      link_attributes: { target: "_blank" },
      safe_links_only: true,
      escape_html: false, # 我们在 block_code/codespan 中手动转义
      with_toc_data: true,
      filter_html: false
    )

    # 创建 Markdown 解析器，添加更多扩展功能
    markdown = Redcarpet::Markdown.new(
      renderer, # 使用自定义渲染器
      autolink: true,
      tables: true,
      fenced_code_blocks: true, # 必须启用才能调用 block_code
      strikethrough: true,
      superscript: true,
      underline: true,
      highlight: true, # 这个 highlight 选项与语法高亮无关，是标记 <mark>
      quote: true,
      footnotes: true,
      lax_spacing: true,
      space_after_headers: true,
      no_intra_emphasis: true # Often useful to avoid unintended emphasis in code-like text
    )

    # 渲染Markdown文本为HTML
    html_output = markdown.render(text)

    # 输出为安全的HTML
    html_output.html_safe
  end
end
