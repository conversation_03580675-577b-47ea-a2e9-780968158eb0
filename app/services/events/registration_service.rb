module Events
  class RegistrationService < ApplicationService
    include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>

    def initialize(event, user, params = {})
      @event = event
      @user = user
      @params = params
    end

    def call
      return failure(t("events.registration.system_error")) if @event.nil?
      return failure(t("events.registration.event_not_accessible")) unless event_accessible?
      return failure(t("events.registration.event_full")) if event_full?
      return failure(t("events.registration.already_registered")) if already_registered?
      return failure(t("events.registration.event_cancelled")) if event_cancelled?

      ActiveRecord::Base.transaction do
        registration = create_registration

        if registration.persisted?
          send_confirmation_email(registration)
          track_registration_event(registration)
          success(registration)
        else
          failure(registration.errors.full_messages.join(", "))
        end
      end
    rescue StandardError => e
      Rails.logger.error "Registration failed: #{e.message}"
      failure(t("events.registration.system_error"))
    end

    private

    attr_reader :event, :user, :params

    def event_accessible?
      EventPolicy.new(@event, user: @user).accessible?
    end

    def event_full?
      @event.full?
    end

    def already_registered?
      @event.registered_users.include?(@user)
    end

    def event_cancelled?
      @event.cancelled?
    end

    def create_registration
      # 查找是否有已取消的注册记录
      existing_registration = @event.event_registrations.find_by(user: @user)

      if existing_registration
        # 更新已有记录
        existing_registration.update!(
          status: "confirmed",
          amount_paid: calculate_amount,
          notes: @params[:notes],
          registered_at: Time.current
        )
        existing_registration
      else
        # 创建新记录
        @event.event_registrations.create!(
          user: @user,
          status: "confirmed",
          amount_paid: calculate_amount,
          notes: @params[:notes],
          registered_at: Time.current
        )
      end
    end

    def calculate_amount
      return 0 if @event.price.zero?

      # 如果用户是团队成员且有会员价格，使用会员价格
      if @event.member_price && user_has_member_discount?
        @event.member_price
      else
        @event.price
      end
    end

    def user_has_member_discount?
      @event.accessible_teams.any? { |team| @user.teams.include?(team) }
    end

    def send_confirmation_email(registration)
      EventRegistrationMailer.confirmation(registration).deliver_later
      Rails.logger.info "Registration confirmation email queued for User #{@user.id}, Event #{@event.id}"
    end

    def track_registration_event(registration)
      Rails.logger.info "Event registration created: User #{@user.id} registered for Event #{@event.id}"
      # 可以在这里添加分析统计
    end
  end
end
