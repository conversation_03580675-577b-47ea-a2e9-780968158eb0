<%#
  Events 视图切换器组件 - 动态版本

  用法：
  render 'shared/events_view_switcher',
      current_view: 'grid',           # 当前激活的视图 ('grid', 'calendar', 或 'map')
      params_except: [:view, :date]   # 需要从URL参数中排除的键（可选，默认为空数组）

  功能：
  - 网格视图（Grid）：显示事件卡片列表
  - 日历视图（Calendar）：显示月视图日历（页面跳转）
  - 地图视图（Map）：动态显示地图视图（JavaScript 控制）

  样式：
  - 当前视图会高亮显示（primary 颜色）
  - 响应式设计，小屏幕时隐藏文字标签
  - 动态视图切换不会导致页面重新加载
%>

<%
  # 传入的参数:
  # current_view: 当前视图 ('grid', 'calendar', 或 'map')
  # params_except: 需要排除的参数键数组 (默认: [])

  current_view ||= 'grid'
  params_except ||= []
  base_params = params.except(*params_except).permit!
%>

<!-- 视图切换器 (不创建独立控制器，与主页面控制器协同工作) -->
<div class="inline-flex rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-1">

  <!-- Grid View -->
  <button type="button"
          data-action="click->view-switcher#showGrid"
          data-view-switcher-target="gridButton"
          class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 <%= current_view == 'grid' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300' %>">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
    </svg>
    <span class="hidden sm:inline"><%= t('common.views.grid') %></span>
  </button>

  <!-- Calendar View -->
  <button type="button"
          data-action="click->view-switcher#showCalendar"
          data-view-switcher-target="calendarButton"
          class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 <%= current_view == 'calendar' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300' %>">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z"></path>
    </svg>
    <span class="hidden sm:inline"><%= t('common.views.calendar') %></span>
  </button>

  <!-- Map View -->
  <button type="button"
          data-action="click->view-switcher#showMap"
          data-view-switcher-target="mapButton"
          class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 <%= current_view == 'map' ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300' %>">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
    </svg>
    <span class="hidden sm:inline"><%= t('common.views.map') %></span>
  </button>
</div>
