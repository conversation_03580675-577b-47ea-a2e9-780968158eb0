require 'rails_helper'

RSpec.describe EventCancellationJob, type: :job do
  include ActiveJob::TestHelper

  let(:event) { create(:event, :published, :upcoming) }
  let(:users) { create_list(:user, 3) }

  before do
    users.each { |user| create(:event_registration, event: event, user: user) }
  end

  describe '#perform' do
    it 'sends cancellation notification to all registered users' do
      expect(EventCancellationMailer).to receive(:notification).exactly(3).times
        .and_return(double(deliver_later: true))

      described_class.perform_now(event)
    end

    it 'handles events with no registered users gracefully' do
      empty_event = create(:event, :published, :upcoming)

      expect { described_class.perform_now(empty_event) }.not_to raise_error
    end

    it 'continues processing if one email fails' do
      call_count = 0
      allow(EventCancellationMailer).to receive(:notification) do
        call_count += 1
        if call_count == 1
          raise StandardError, 'Email failed'
        else
          double(deliver_later: true)
        end
      end

      expect { described_class.perform_now(event) }.not_to raise_error
    end

    xit 'logs the number of notifications sent' do
      allow(EventCancellationMailer).to receive(:notification)
        .and_return(double(deliver_later: true))

      expect(Rails.logger).to receive(:info).with(/Sent cancellation notifications for event/)

      described_class.perform_now(event)
    end

    context 'when event has many registered users' do
      let(:many_users) { create_list(:user, 50) }

      before do
        many_users.each { |user| create(:event_registration, event: event, user: user) }
      end

      it 'sends notifications to all users without timeout' do
        expect(EventCancellationMailer).to receive(:notification).exactly(53).times
          .and_return(double(deliver_later: true))

        described_class.perform_now(event)
      end
    end

    context 'error handling' do
      it 'logs errors but does not re-raise them' do
        allow(EventCancellationMailer).to receive(:notification)
          .and_raise(StandardError, 'Test error')

        expect(Rails.logger).to receive(:error).at_least(:once)

        expect { described_class.perform_now(event) }.not_to raise_error
      end
    end
  end

  describe 'job configuration' do
    it 'is configured with correct queue' do
      expect(described_class.queue_name).to eq('default')
    end

    it 'can be enqueued with perform_later' do
      expect {
        described_class.perform_later(event)
      }.to have_enqueued_job(described_class).with(event)
    end
  end
end
