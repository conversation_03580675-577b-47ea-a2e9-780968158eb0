require "administrate/base_dashboard"

class ActiveStorageBlobDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    key: Field::String,
    filename: Field::String,
    content_type: Field::String,
    metadata: Field::Text,
    service_name: Field::String,
    byte_size: Field::Number,
    checksum: Field::String,
    attachments: Field::HasMany.with_options(class_name: "ActiveStorage::Attachment"),
    created_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  COLLECTION_ATTRIBUTES = %i[
    id
    filename
    content_type
    byte_size
    service_name
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    key
    filename
    content_type
    metadata
    service_name
    byte_size
    checksum
    attachments
    created_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    filename
    content_type
    service_name
    byte_size
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  COLLECTION_FILTERS = {
    images: ->(resources) { resources.where(content_type: ["image/png", "image/jpg", "image/jpeg"]) },
    documents: ->(resources) { resources.where.not(content_type: ["image/png", "image/jpg", "image/jpeg"]) }
  }.freeze

  # Overwrite this method to customize how blobs are displayed
  # across all pages of the admin dashboard.
  def display_resource(blob)
    "#{blob.filename} (#{number_to_human_size(blob.byte_size)})"
  end

  private

  def number_to_human_size(size)
    if size < 1024
      "#{size} B"
    elsif size < 1024**2
      "#{(size / 1024.0).round(1)} KB"
    elsif size < 1024**3
      "#{(size / (1024.0**2)).round(1)} MB"
    else
      "#{(size / (1024.0**3)).round(1)} GB"
    end
  end
end
