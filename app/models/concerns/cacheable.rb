module Cacheable
  extend ActiveSupport::Concern

  included do
    # 缓存相关回调
    after_commit :clear_cache
  end

  class_methods do
    # 类方法 - 缓存键生成
    def cache_key(id, version = nil)
      key = "#{model_name.collection}/#{id}"
      version ? "#{key}-#{version}" : key
    end

    # 类方法 - 带缓存的查找
    def cached_find(id)
      Rails.cache.fetch(cache_key(id, cache_version(id)), expires_in: 1.hour) do
        find(id)
      end
    end

    # 类方法 - 批量缓存查找
    def cached_find_by_ids(ids)
      ids.map { |id| cached_find(id) }
    end

    private

    def cache_version(id)
      # 获取最新更新时间戳作为版本号
      where(id: id).pick(:updated_at).to_i
    end
  end

  # 清除缓存的实例方法
  def clear_cache
    # 生成基础缓存键
    base_key = [ cache_key_with_version ]
    if respond_to?(:clear_associated_caches)
      clear_associated_caches(base_key)
    else
      # 如果没有 clear_associated_caches，则只清除主缓存
      Rails.cache.delete(base_key + [ "associations" ])
    end
  end

  # 版本化缓存键
  def cache_version
    updated_at.to_i
  end

  # 带版本的缓存键
  def cache_key_with_version
    self.class.cache_key(id, cache_version)
  end
end
