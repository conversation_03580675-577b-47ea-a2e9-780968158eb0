<!-- 聊天输入表单 -->
<%# Removed sticky, bottom, z-index, border, shadow-sm classes %>
<div id="chat_form" class="bg-white dark:bg-gray-800 p-4 w-full">
  <%= form_with(
     url: ai_messages_path,
     model: Message.new,
     data: {
       # Removed controllers, kept action and providers
       # Added ai-chat target for the form
       action: "turbo:submit-end->ai-chat#reset",
       ai_chat_target: "form",
       slash_commands_providers_value: '["prompt"]'
    }
  ) do |f| %>
    <%= f.hidden_field :messageable_type, value: conversation.class.name %>
    <%= f.hidden_field :messageable_id, value: conversation.id %>
    <div class="flex items-start gap-6">

      <%# Input Area Column %>
      <div class="flex-1 min-h-[44px] flex flex-col">
        <%# Input Area Wrapper %>
        <div>
          <div class="relative">
            <%= f.text_area :content,
              class: "block w-full px-4 py-2.5 text-sm text-gray-900 bg-white dark:bg-gray-700 dark:text-white border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-3 focus:ring-primary-500/50 focus:border-primary-500 transition-all duration-200",
              placeholder: t('ai.conversations.placeholder'),
              rows: 2,
              data: {
                ai_chat_target: "input", # Target for ai-chat controller
                slash_commands_target: "input", # Target for slash-commands controller
                action: "keydown->ai-chat#handleKeydown input->ai-chat#adjustHeight", # Actions for ai-chat controller
                max_height: 200
              }
            %>
            <%# Target for slash-commands controller %>
            <div data-slash-commands-target="suggestions" class="hidden absolute z-20 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg mt-1 shadow-lg max-h-60 overflow-y-auto"></div>
          </div>
        </div>
      </div>

      <%= button_tag type: "submit",
        class: "inline-flex items-center justify-center rounded-lg px-4 py-2.5 transition-colors duration-200 font-medium bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 text-white min-w-[100px]",
        data: { ai_chat_target: "submit" } do %> <%# Target for ai-chat controller %>
        <span><%= t('ai.conversations.send') %></span>
        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
        </svg>
      <% end %>
    </div>
  <% end %>

  <%# Clear Conversation Button %>
  <% if conversation&.id.present? %>
    <div class="mt-2 flex justify-start">
      <%= button_to clear_conversation_ai_conversation_path(conversation),
          method: :post,
          class: "p-1.5 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-lg focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500",
          title: t('ai.conversations.clear'),
          form_class: "inline-block",
          data: { turbo_confirm: t('common.messages.confirm') } do %>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
        <span class="sr-only"><%= t('ai.conversations.clear') %></span>
      <% end %>
    </div>
  <% end %>
</div>
