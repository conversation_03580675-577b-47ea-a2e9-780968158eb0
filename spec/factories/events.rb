FactoryBot.define do
  factory :event do
    title { "Sample Event" }
    description { "This is a sample event description" }
    start_time { 1.week.from_now }
    end_time { 1.week.from_now + 2.hours }
    price { 0 }
    visibility { :public_visibility }
    status { :published }
    is_online { false }
    association :user

    trait :private do
      visibility { :private_visibility }
    end

    trait :team_only do
      visibility { :team_visibility }
    end

    trait :draft do
      status { :draft }
      published_at { nil }
    end

    trait :published do
      status { :published }
      published_at { 1.day.ago }
    end

    trait :cancelled do
      status { :cancelled }
    end

    trait :completed do
      status { :completed }
    end

    trait :upcoming do
      start_time { 1.week.from_now }
      end_time { 1.week.from_now + 2.hours }
    end

    trait :past do
      start_time { 1.week.ago }
      end_time { 1.week.ago + 2.hours }
    end

    trait :with_location do
      association :event_location
    end

    trait :with_category do
      association :event_category
    end

    trait :online do
      is_online { true }
      meeting_link { "https://zoom.us/j/123456789" }
    end

    trait :paid do
      price { 50.0 }
    end

    trait :with_limit do
      max_participants { 10 }
    end
  end
end
