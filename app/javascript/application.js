// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails
import "@hotwired/turbo-rails";
import "controllers";
import "trix";
import "@rails/actiontext";
import { Application } from "@hotwired/stimulus";
import { initFlowbite } from "flowbite";
import "prismjs"; // Import for side effects only

// Stimulus 初始化
const application = Application.start();

// Configure Stimulus development experience
application.debug = false;
window.Stimulus = application;

export { application };

// 安全的 Flowbite 初始化函数
function safeInitFlowbite() {
  try {
    // 确保 DOM 已准备好
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        initFlowbiteSelectively();
      });
    } else {
      initFlowbiteSelectively();
    }
  } catch (error) {
    console.warn('Flowbite initialization failed:', error);
  }
}

// 选择性 Flowbite 初始化 - 跳过有问题的模态框
function initFlowbiteSelectively() {
  try {
    // 临时隐藏有问题的模态框元素，防止 Flowbite 尝试初始化它们
    const problematicModals = ['day-events-modal'];
    const hiddenElements = [];

    problematicModals.forEach(modalId => {
      const element = document.getElementById(modalId);
      if (element) {
        // 临时移除 data 属性，防止 Flowbite 处理
        const originalDataToggle = element.getAttribute('data-modal-toggle');
        const originalDataHide = element.getAttribute('data-modal-hide');
        const originalDataShow = element.getAttribute('data-modal-show');

        if (originalDataToggle) element.removeAttribute('data-modal-toggle');
        if (originalDataHide) element.removeAttribute('data-modal-hide');
        if (originalDataShow) element.removeAttribute('data-modal-show');

        hiddenElements.push({
          element,
          originalDataToggle,
          originalDataHide,
          originalDataShow
        });
      }
    });

    // 初始化 Flowbite
    initFlowbite();

    // 恢复被隐藏的元素的 data 属性
    hiddenElements.forEach(({ element, originalDataToggle, originalDataHide, originalDataShow }) => {
      if (originalDataToggle) element.setAttribute('data-modal-toggle', originalDataToggle);
      if (originalDataHide) element.setAttribute('data-modal-hide', originalDataHide);
      if (originalDataShow) element.setAttribute('data-modal-show', originalDataShow);
    });

    console.log('Flowbite initialized successfully with selective modal handling');
  } catch (error) {
    console.warn('Selective Flowbite initialization failed:', error);
  }
}

// 初始化 Flowbite
safeInitFlowbite();

// Turbo 事件处理
document.addEventListener("turbo:load", () => {
  safeInitFlowbite();
});

document.addEventListener("turbo:render", () => {
  safeInitFlowbite();
});

// 清理 Flowbite 组件以防止内存泄漏
document.addEventListener("turbo:before-cache", () => {
  try {
    // 隐藏所有打开的模态框
    const modals = document.querySelectorAll('[data-modal-toggle]');
    modals.forEach(modal => {
      if (!modal.classList.contains('hidden')) {
        modal.classList.add('hidden');
        modal.setAttribute('aria-hidden', 'true');
      }
    });

    // 清理 Flowbite 实例（如果可用）
    if (typeof window.FlowbiteInstances !== 'undefined') {
      // 这里可以添加更多清理逻辑
      console.log('Cleaning up Flowbite instances before cache');
    }
  } catch (error) {
    console.warn('Error during Flowbite cleanup:', error);
  }
});

// Initial highlight for page load (after DOM is ready)
document.addEventListener("DOMContentLoaded", (event) => {
  // Try accessing Prism globally
  if (typeof Prism !== 'undefined') {
    Prism.highlightAll();
  }
});
