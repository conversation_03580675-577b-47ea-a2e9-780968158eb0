<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-(--breakpoint-xl) lg:py-16">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= t('knowledge_bases.posts.title') %></h1>
        <p class="mt-2 text-lg text-gray-500 dark:text-gray-400"><%= t('knowledge_bases.posts.subtitle') %></p>
      </div>
    </div>

    <div class="flex gap-8">
      <!-- 可用文章区 -->
      <div class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-xs p-6 border border-gray-200 dark:border-gray-700"
           data-controller="knowledge-base"
           data-knowledge-base-update-url-value="<%= update_post_association_knowledge_base_path(@knowledge_base) %>"
           data-action="drop->knowledge-base#detach dragover->knowledge-base#allowDrop">
        <h2 class="text-xl font-semibold mb-4">
          <span class="bg-primary-100 text-primary-800 text-sm font-medium px-3 py-1 rounded-full dark:bg-primary-900 dark:text-primary-300">
            <%= t('knowledge_bases.posts.available_posts') %> (<%= @available_posts.count %>)
          </span>
        </h2>
        <%= render partial: "available_posts", locals: { available_posts: @available_posts } %>
      </div>

      <!-- 已关联文章区 -->
      <div class="flex-1 bg-white dark:bg-gray-800 rounded-lg shadow-xs p-6 border border-gray-200 dark:border-gray-700"
           data-controller="knowledge-base"
           data-knowledge-base-update-url-value="<%= update_post_association_knowledge_base_path(@knowledge_base) %>"
           data-action="drop->knowledge-base#attach dragover->knowledge-base#allowDrop">
        <h2 class="text-xl font-semibold mb-4">
          <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full dark:bg-green-900 dark:text-green-300">
            <%= t('knowledge_bases.posts.associated_posts') %>（<%= @posts.count %>）
          </span>
        </h2>
        <%= render partial: "posts", locals: { posts: @posts } %>
      </div>
    </div>
  </div>
</section>

<%= turbo_stream.replace "available-posts" do %>
  <div id="available-posts" class="space-y-3">
    <% @available_posts.each do |post| %>
      <div class="p-4 border rounded-lg hover:bg-gray-50 cursor-move transition-colors"
           draggable="true"
           data-post-id="<%= post.id %>"
           data-action="dragstart->knowledge-base#dragStart">
        <h3 class="font-medium"><%= post.title %></h3>
        <p class="text-sm text-gray-500 mt-1"><%= truncate(post.content.to_plain_text, length: 50) %></p>
      </div>
    <% end %>
  </div>
<% end %>

<%= turbo_stream.replace "associated-posts" do %>
  <div id="associated-posts" class="space-y-3">
    <% @posts.each do |post| %>
      <div class="p-4 border rounded-lg hover:bg-gray-50 cursor-move transition-colors"
           draggable="true"
           data-post-id="<%= post.id %>"
           data-action="dragstart->knowledge-base#dragStart">
        <h3 class="font-medium"><%= post.title %></h3>
        <p class="text-sm text-gray-500 mt-1"><%= truncate(post.content.to_plain_text, length: 50) %></p>
      </div>
    <% end %>
  </div>
<% end %>
