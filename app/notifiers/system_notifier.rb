# To deliver this notification:
#
# SystemNotifier.with(message: "System maintenance scheduled").deliver(User.all)

class SystemNotifier < ApplicationNotifier
  # ActionCable delivery for real-time notifications
  deliver_by :action_cable do |config|
    config.channel = "NotificationChannel"
    config.stream = -> { recipient }
    config.message = -> {
      {
        id: notification.id,
        type: notification.type,
        message: message,
        url: url,
        created_at: notification.created_at.iso8601
      }
    }
  end

  # Email delivery (conditional based on user preferences)
  deliver_by :email do |config|
    config.mailer = "NotificationMailer"
    config.method = "system_notification"
    config.if = -> { recipient.notification_enabled?(:system, :email) }
  end
  
  # WeChat delivery (conditional based on user preferences and binding)
  deliver_by :wechat do |config|
    config.template_id = "SYSTEM_NOTIFICATION_TEMPLATE_ID" # 需要在微信后台申请
    config.account = :service
    config.if = -> { recipient.wechat_notification_enabled?(:system) }
  end

  # Required params
  required_param :message

  notification_methods do
    def message
      params[:message]
    end

    def url
      Rails.application.routes.url_helpers.root_path
    end

    def avatar_user
      # 对于系统通知，使用默认的系统头像（返回 nil）
      nil
    end
  end
end
