当(/^我访问首页$/) do
  visit root_path
end

当(/^我访问关于我们页面$/) do
  visit about_path
end

当(/^我访问隐私政策页面$/) do
  visit privacy_policy_path
end

当(/^我访问服务条款页面$/) do
  visit terms_path
end

那么(/^我应该看到"([^"]*)"标题$/) do |title|
  expect(page).to have_content(title)
end

那么(/^我应该看到"([^"]*)"副标题$/) do |subtitle|
  expect(page).to have_content(subtitle)
end

那么(/^我应该看到使命和愿景部分$/) do
  within('.mission-vision') do
    expect(page).to have_content('使命')
    expect(page).to have_content('愿景')
  end
end

那么(/^我应该看到核心产品部分$/) do
  within('.core-products') do
    expect(page).to have_content('核心产品')
    expect(page).to have_content('Coding Girls Day')
    expect(page).to have_content('"400+300"职业培训')
    expect(page).to have_content('软件开发高阶赋能')
  end
end

那么(/^我应该看到项目成果部分$/) do
  within('.achievements') do
    expect(page).to have_content('项目成果')
    expect(page).to have_content('个城市')
    expect(page).to have_content('所高校')
    expect(page).to have_content('位学员')
    expect(page).to have_content('位教练')
  end
end

那么(/^我应该看到"([^"]*)"部分$/) do |section|
  expect(page).to have_content(section)
end

那么(/^我应该看到联系邮箱"([^"]*)"$/) do |email|
  expect(page).to have_content(email)
end
