class NotificationChannel < ApplicationCable::Channel
  def subscribed
    stream_for current_user
  end

  def unsubscribed
    # Any cleanup needed when channel is unsubscribed
  end

  def mark_as_read(data)
    notification = current_user.notifications.find(data['id'])
    notification.mark_as_read!
    
    # Broadcast updated unread count to user
    broadcast_to current_user, {
      type: 'notification_read',
      id: data['id'],
      unread_count: current_user.unread_notifications_count
    }
  rescue ActiveRecord::RecordNotFound
    # Handle case where notification doesn't exist
    Rails.logger.warn "Notification not found: #{data['id']}"
  end

  def mark_all_as_read
    current_user.mark_all_notifications_as_read!
    
    # Broadcast updated unread count to user
    broadcast_to current_user, {
      type: 'all_notifications_read',
      unread_count: 0
    }
  end
end
