name: Job - Deploy to <PERSON>

on:
  workflow_call:
    inputs:
      kamal-destination:
        required: true
        type: string
      # 新增输入：指定 registry server 地址
      kamal-registry-server:
        required: false # 对于 Docker Hub 可以不填
        type: string
    secrets:
      KAMAL_REGISTRY_USERNAME:
        required: true
      KAMAL_REGISTRY_PASSWORD:
        required: true
      SSH_PRIVATE_KEY:
        required: true
      RAILS_MASTER_KEY:
        required: true

env:
  DOCKER_BUILDKIT: 1
  KAMAL_REGISTRY_USERNAME: ${{ secrets.KAMAL_REGISTRY_USERNAME }}
  KAMAL_REGISTRY_PASSWORD: ${{ secrets.KAMAL_REGISTRY_PASSWORD }}
  RAILS_MASTER_KEY: ${{ secrets.RAILS_MASTER_KEY }}

jobs:
  deploy:
    name: kamal deploy
    timeout-minutes: 10
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - uses: webfactory/ssh-agent@v0.9.1
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
      - name: Log in to the correct registry # 修改点：登录正确的 Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ inputs.kamal-registry-server }} # 使用输入指定 server
          username: ${{ secrets.KAMAL_REGISTRY_USERNAME }}
          password: ${{ secrets.KAMAL_REGISTRY_PASSWORD }}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - run: gem install kamal
      - run: kamal lock release -d ${{ inputs.kamal-destination }}
      - run: kamal deploy -d ${{ inputs.kamal-destination }} --verbose # 建议加上 --verbose
