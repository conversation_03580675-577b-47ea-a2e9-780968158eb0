class EventReminderNotifier < ApplicationNotifier
  # 事件提醒通知
  # @param event [Event] 事件对象
  # @param reminder_type [String] 提醒类型：'day_before', 'hour_before', 'starting_soon'

  required_param :event
  required_param :reminder_type

  # WeChat delivery (conditional based on user preferences and binding)
  deliver_by :wechat do |config|
    template_config = WechatNotificationTemplateService.get_template_config("event_reminder")
    config.template_id = template_config["template_id"]
    config.account = :service
    config.if = -> { recipient.wechat_notification_enabled?(:event_reminder) }
    config.url = -> { Rails.application.routes.url_helpers.event_url(params[:event], host: Rails.application.routes.default_url_options[:host] || "localhost") }
    config.data = -> { WechatNotificationTemplateService.build_event_reminder_data(params[:event], params[:reminder_type]) }
  end

  notification_methods do
    def message
      case params[:reminder_type]
      when "day_before"
        "活动提醒：您报名的活动「#{params[:event].title}」将在明天 #{params[:event].start_time.strftime('%H:%M')} 开始"
      when "hour_before"
        "活动即将开始：您报名的活动「#{params[:event].title}」将在1小时后开始"
      when "starting_soon"
        "活动开始了：您报名的活动「#{params[:event].title}」现在开始了"
      else
        "活动提醒：您有一个活动「#{params[:event].title}」"
      end
    end

    def url
      Rails.application.routes.url_helpers.event_path(params[:event])
    end

    def title
      "活动提醒"
    end

    def avatar_user
      # 对于事件提醒，显示事件创建者的头像
      params[:event]&.user
    end
  end
end
