# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AI Pro CGC is a Rails 8.0 application that provides intelligent knowledge management and AI assistant capabilities for learning communities. The project uses modern Rails features like Hotwire, Solid Queue, and integrates with AI providers through Langchain.rb.

## Development Commands

### Essential Commands

- **Start development server**: `bin/dev` (runs Rails server + Tailwind CSS watcher via Foreman)
- **Run all tests**: `bundle exec rspec && bundle exec cucumber`
- **Run specific test**: `bundle exec rspec spec/path/to/spec.rb:line_number`
- **Run linter**: `rubocop` or `rubocop -a` (auto-fix)
- **Security scan**: `brakeman`
- **Rails console**: `rails console` or `rails c`
- **Database tasks**:
  - `rails db:create db:migrate` (setup)
  - `rails db:seed` (load sample data)
  - `rails db:reset` (drop, create, migrate, seed)

### Background Jobs

- **Start job processor**: `bundle exec rake solid_queue:start`
- Jobs are processed using Solid Queue (database-backed)

### Custom Rake Tasks

- **WeChat menu creation**: `rails wechat:create_service_menu` (requires server IP whitelisting)
- **Search index management**: Available in `lib/tasks/search_index.rake`
- **Cucumber tasks**: `rails cucumber` or specific features via cucumber-rails

## Architecture Overview

### Core Domain Models

The application is organized around these key concepts:

- **User**: Central authentication model with multiple auth methods (email, phone, OAuth)
- **Team**: Collaborative workspace with role-based access (owner, admin, member)
- **KnowledgeBase**: Container for documents and embeddings, can be personal/team/public
- **Assistant**: AI assistants with customizable prompts and tool access
- **Conversation**: Chat sessions between users and assistants with RAG support
- **Post**: Content items that can be associated with knowledge bases
- **BaseUnit**: Processing units for document chunks and embeddings
- **Event**: Scheduled events with registration and location management
- **TeamResourceAccess**: Fine-grained permissions for team resources

### Service Layer Pattern

Complex business logic is extracted into service objects in `app/services/`:

- AI operations: `app/services/ai/` (chat, embeddings, document processing)
- Events: `app/services/events/` (registration, cancellation)
- SMS and messaging services
- Langchain tool registry for AI integrations

### Authorization

Uses Action Policy for authorization with policies in `app/policies/`. Each model has a corresponding policy defining access rules based on user roles and ownership.

### Background Processing

- Document processing and embedding generation happen asynchronously via jobs
- AI conversations are streamed using Action Cable for real-time responses
- File uploads are processed in the background with progress tracking

### Frontend Architecture

- Turbo for page navigation and real-time updates
- Stimulus controllers in `app/javascript/controllers/` for interactive features
- Tailwind CSS v4 for styling with Flowbite components
- No build step for JavaScript (uses Importmap)
- Specialized controllers for AI chat, particle effects, calendar, maps, and upload handling

## Testing Approach

### RSpec for Unit/Integration Tests

- Models: Test validations, associations, and methods
- Services: Test business logic with mocked dependencies
- Controllers: Test request/response cycles
- Policies: Test authorization rules

### Cucumber for Acceptance Tests

- Feature files in `features/` describe user stories
- Step definitions test full user workflows
- Uses Capybara for browser automation

### Test Utilities

- FactoryBot for test data: `create(:user)`, `build(:team)`
- Faker for realistic data generation
- WebMock for external API stubbing (configured to allow localhost)
- Database Cleaner ensures test isolation
- Action Policy test helpers for authorization testing
- Time travel support via ActiveSupport::Testing::TimeHelpers

## Key Integrations

### AI/LLM Integration

- Langchain.rb manages LLM interactions
- Supports OpenAI and Doubao providers (with custom Doubao implementation in `lib/langchain/llm/`)
- Vector embeddings stored in Qdrant
- RAG implemented via message service and conversation handling
- Wikipedia integration for external knowledge

### WeChat Integration

- Public account support in `app/controllers/wechat/`
- OAuth authentication via OmniAuth
- Message handling and menu management
- Session management for WeChat users
- Custom conversion utilities for API format compatibility

### File Processing

- Active Storage for file uploads
- Document processors for PDF (`pdf-reader`), Word (`docx`), TXT, Markdown
- Background jobs handle text extraction and embedding
- Storage validation and file size limits
- AWS S3 integration for production file storage

### Event Management

- Calendar integration with multiple view types
- Map integration using Leaflet for location display
- Registration system with instructor and location management
- Full-text search capabilities for events

## Code Style Guidelines

- Follow Rubocop Rails Omakase style guide
- Service objects for complex operations
- Thin controllers, fat models principle
- Use concerns for shared behavior
- Prefer composition over inheritance
- Write descriptive test names

## Common Pitfalls

1. **Credentials**: API keys are in `config/credentials.yml.enc`. Edit with `rails credentials:edit`
2. **Background Jobs**: Ensure Solid Queue is running for async operations
3. **Vector Database**: Qdrant must be running for RAG features
4. **WeChat Dev**: Requires ngrok or similar for webhook testing
5. **Turbo Streams**: Remember to handle both HTML and Turbo Stream formats in controllers
6. **Admin Access**: Admin features require `admin: true` on user records
7. **Team Permissions**: TeamResourceAccess model handles fine-grained permissions for knowledge bases
8. **I18n**: Application supports multiple languages - test language switching carefully

## Deployment Notes

- Uses Kamal for deployment (config in `.kamal/`)
- Staging and production environments configured
- GitHub Actions handle CI/CD pipeline
- PostgreSQL required for production
- Redis optional (Solid Cable provides WebSocket support)

## Admin Interface

- Uses Administrate gem (v1.0.0.beta3) for admin interface
- Admin dashboards for all major models (users, teams, events, knowledge bases, etc.)
- Access restricted to users with `admin: true`
- Custom styling and integration with main application theme

## Important File Locations

- **Custom Langchain providers**: `lib/langchain/llm/doubao.rb`
- **Markdown converter**: `lib/markdown_converter.rb`
- **Model concerns**: `app/models/concerns/` (archivable, cacheable, visibility, etc.)
- **Feature test helpers**: `spec/support/features/` (organized by domain)
- **WeChat menu configs**: `config/wechat_menus/`
- **Comprehensive docs**: `docs/` (architecture, admin guide, API docs, RAG system)

## Memories

- No need to include FeaturesHelper in Feature tests, it is configured in @spec/rails_helper.rb
