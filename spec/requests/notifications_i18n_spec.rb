require 'rails_helper'

RSpec.describe "Notifications I18n", type: :request do
  let(:user) { create(:user) }
  
  before do
    # Mock authentication for request specs
    allow_any_instance_of(ApplicationController).to receive(:require_authentication)
    allow_any_instance_of(ApplicationController).to receive(:authenticated?).and_return(true)
    allow_any_instance_of(ApplicationController).to receive(:current_user).and_return(user)
    
    # Mock ActionPolicy methods
    allow_any_instance_of(ApplicationController).to receive(:authorize!)
    allow_any_instance_of(ApplicationController).to receive(:verify_authorized)
    allow_any_instance_of(ApplicationController).to receive(:authorized_scope).and_return(Noticed::Notification.where(recipient: user))
    
    # Create some notifications for the user
    create_list(:notification, 2, recipient: user)
  end

  after do
    # Reset locale after each test
    I18n.locale = I18n.default_locale
  end

  describe "GET /notifications" do
    context "when I18n.locale is set to :en" do
      it "displays English page title" do
        I18n.locale = :en
        get notifications_path
        
        expect(response).to have_http_status(:success)
        # Check for key translated elements
        expect(response.body).to include("Notifications")
        expect(response.body).to include("Manage your notifications and alerts")
        expect(response.body).to include("Mark all as read")
        expect(response.body).to include("All")
        expect(response.body).to include("Unread")
        expect(response.body).to include("Read")
      end
    end

    context "when I18n.locale is set to :zh" do
      it "displays Chinese page title" do
        I18n.locale = :zh
        get notifications_path
        
        expect(response).to have_http_status(:success)
        # Check for key translated elements
        expect(response.body).to include("通知")
        expect(response.body).to include("管理您的通知和提醒")
        expect(response.body).to include("全部标记为已读")
        expect(response.body).to include("全部")
        expect(response.body).to include("未读")
        expect(response.body).to include("已读")
      end
    end

    context "when switching locale dynamically" do
      it "switches page title text between Chinese and English" do
        # Test English first
        I18n.locale = :en
        get notifications_path
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include("Notifications")
        # Don't check for exclusion of Chinese text since both may appear in layout

        # Then test Chinese
        I18n.locale = :zh
        get notifications_path
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include("通知")
        # Don't check for exclusion of English text since both may appear in layout
      end
    end
  end

  describe "PATCH /notifications/mark_all with I18n" do
    context "when I18n.locale is set to :en" do
      it "returns English success message" do
        I18n.locale = :en
        patch mark_all_notifications_path
        
        expect(response).to have_http_status(:found)
        expect(response).to redirect_to(notifications_path(format: :html))
        
        # Check the flash message directly from session
        expect(flash[:notice]).to eq("All notifications marked as read")
      end
    end

    context "when I18n.locale is set to :zh" do
      it "returns Chinese success message" do
        I18n.locale = :zh
        patch mark_all_notifications_path
        
        expect(response).to have_http_status(:found)
        expect(response).to redirect_to(notifications_path(format: :html))
        
        # Check the flash message directly from session
        expect(flash[:notice]).to eq("所有通知已标记为已读")
      end
    end
  end

  describe "GET /notifications/:id with I18n" do
    let(:notification) { create(:notification, recipient: user) }

    context "when I18n.locale is set to :en" do
      it "displays English notification detail page" do
        I18n.locale = :en
        get notification_path(notification)
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include("Notification Detail")
        expect(response.body).to include("← Back to All Notifications")
        expect(response.body).to include("Notification Time:")
      end
    end

    context "when I18n.locale is set to :zh" do
      it "displays Chinese notification detail page" do
        I18n.locale = :zh
        get notification_path(notification)
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include("通知详情")
        expect(response.body).to include("← 返回所有通知")
        expect(response.body).to include("通知时间：")
      end
    end
  end

  describe "error messages with I18n" do
    context "when accessing non-existent notification" do
      it "returns English error message when locale is :en" do
        I18n.locale = :en
        get notification_path(99999)
        
        expect(response).to have_http_status(:found)
        expect(response).to redirect_to(notifications_path)
        
        # Check flash alert directly from session
        expect(flash[:alert]).to eq("Notification not found")
      end

      it "returns Chinese error message when locale is :zh" do
        I18n.locale = :zh
        get notification_path(99999)
        
        expect(response).to have_http_status(:found)
        expect(response).to redirect_to(notifications_path)
        
        # Check flash alert directly from session
        expect(flash[:alert]).to eq("通知不存在")
      end
    end
  end
end
