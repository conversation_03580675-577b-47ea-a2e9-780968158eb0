class AddUnionidToOmniAuthIdentities < ActiveRecord::Migration[8.0]
  def change
    # Add unionid column, allowing null values
    add_column :omni_auth_identities, :unionid, :string, null: true

    # Remove the default index on unionid if it exists (though unlikely needed here)
    # remove_index :omni_auth_identities, :unionid, if_exists: true

    # Add a composite index on provider and unionid for efficient lookups
    add_index :omni_auth_identities, [ :provider, :unionid ]
    add_index :omni_auth_identities, [ :provider, :uid ], unique: true
  end
end
