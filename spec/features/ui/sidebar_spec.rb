require 'rails_helper'

RSpec.feature "Sidebar", type: :feature, js: true do
  let(:user) { create(:user) }

  before do
    sign_in(user)
    # 确保页面已加载
    expect(page).to have_css('body')
  end

  # 侧边栏基本元素
  context "侧边栏基本元素" do
    it "包含基本导航元素" do
      within('#sidebar') do
        # 检查主要导航链接
        expect(page).to have_link('Dashboard')
        expect(page).to have_content('AI')
        expect(page).to have_content('Knowledge Bases')
        expect(page).to have_link('My Post')
        expect(page).to have_link('Base Units')
        expect(page).to have_content('Account Settings')
      end
    end
  end

  # AI 功能菜单
  context "AI 功能菜单" do
    it "展开AI子菜单并显示相关链接" do
      # 点击AI下拉菜单
      find('button[aria-controls="dropdown-ai"]').click

      # 检查子菜单项目是否显示
      expect(page).to have_css('#dropdown-ai', visible: true)

      within('#dropdown-ai') do
        expect(page).to have_link('My Assistants')
        expect(page).to have_link('AI Chat')
      end
    end
  end

  # Events 功能菜单
  context "Events 功能菜单" do
    it "展开Events子菜单并显示相关链接" do
      # 确保页面已完全加载
      expect(page).to have_content(I18n.t('events.index.title'))

      # 点击Events下拉菜单
      find('button[aria-controls="dropdown-events"]').click

      # 检查子菜单项目是否显示
      expect(page).to have_css('#dropdown-events', visible: true)

      within('#dropdown-events') do
        expect(page).to have_link(I18n.t('events.index.all'))
        expect(page).to have_link(I18n.t('events.calendar.title'))
        expect(page).to have_link(I18n.t('common.views.map'))
      end
    end

    it "Event Calendar链接导航到正确的日历视图" do
      # 确保页面已完全加载
      expect(page).to have_content(I18n.t('events.index.title'))

      # 点击Events下拉菜单
      find('button[aria-controls="dropdown-events"]').click

      # 检查子菜单是否显示
      expect(page).to have_css('#dropdown-events', visible: true)

      within('#dropdown-events') do
        # 点击Event Calendar链接
        click_link I18n.t('events.calendar.title')
      end

      # 验证导航到了正确的路径
      expect(page).to have_current_path(calendar_events_path)
    end

    it "Event Calendar链接具有正确的href属性" do
      # 点击Events下拉菜单
      find('button[aria-controls="dropdown-events"]').click

      # 检查子菜单是否显示
      expect(page).to have_css('#dropdown-events', visible: true)

      within('#dropdown-events') do
        # 验证Event Calendar链接的href属性
        expect(page).to have_link(I18n.t('events.calendar.title'), href: calendar_events_path)
      end
    end
  end

  # 知识库菜单
  context "知识库菜单" do
    it "展开知识库子菜单并显示相关链接" do
      # 确保页面已完全加载
      expect(page).to have_content('Knowledge Bases')

      # 点击知识库下拉菜单
      find('button[aria-controls="dropdown-knowledgebase"]').click

      # 检查子菜单项目是否显示
      expect(page).to have_css('#dropdown-knowledgebase', visible: true)

      within('#dropdown-knowledgebase') do
        expect(page).to have_link('Knowledge Bases')
        expect(page).to have_link('Archived Knowledge Bases')
        expect(page).to have_link('Create Knowledge Base')
      end
    end
  end

  # 账户设置菜单
  context "账户设置菜单" do
    it "展开账户设置子菜单并显示相关链接" do
      # 点击账户设置下拉菜单
      find('button[aria-controls="dropdown-settings"]').click

      # 验证子菜单链接
      within('#dropdown-settings') do
        expect(page).to have_link(I18n.t('users.profile.title'), href: settings_users_path)
      end
    end
  end

  # 帮助文档区域
  context "帮助文档区域" do
    it "显示帮助文档相关链接" do
      # 查找并检查帮助文档区域的链接
      within('#sidebar .pt-2.space-y-2') do
        expect(page).to have_link('About')
        expect(page).to have_link('Terms of Service')
      end
    end
  end

  # 语言选择功能
  context "语言选择功能" do
    it "显示语言选择器并可以打开语言选择下拉菜单" do
      # 查找并点击语言选择器按钮
      find('button[data-dropdown-toggle="language-dropdown"]').click

      # 检查下拉菜单是否显示
      expect(page).to have_css('#language-dropdown', visible: true)

      # 检查下拉菜单内容
      within('#language-dropdown') do
        expect(page).to have_content('中文 (简体)')
        expect(page).to have_content('English')
      end
    end
  end

  # 移动端菜单按钮
  # TODO: 移动端测试在CI环境中不稳定
  xcontext "移动端菜单按钮" do
    before do
      # 调整浏览器窗口大小模拟移动设备
      page.driver.browser.manage.window.resize_to(375, 667)

      # 确保页面重新加载以适应新窗口大小
      visit current_path
    end

    it "在移动视图中显示菜单切换按钮并可以打开侧边栏" do
      # 检查移动端菜单按钮
      expect(page).to have_css('button[data-drawer-target="sidebar"]', visible: true)

      # 点击菜单按钮
      find('button[data-drawer-target="sidebar"]').click

      # 给JavaScript动画一些时间来执行
      sleep 1

      # 在移动视图中，侧边栏应该在点击按钮后显示出来
      # 但由于样式和实现的不同，我们只检查菜单按钮是否可以点击
      expect(page).to have_css('button[data-drawer-target="sidebar"]')
    end
  end
end
