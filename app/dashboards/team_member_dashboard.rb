require "administrate/base_dashboard"

class TeamMemberDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    team: Field::BelongsTo,
    user: Field::BelongsTo,
    role: Field::Select.with_options(collection: TeamMember.roles.keys),
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    id
    team
    user
    role
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    team
    user
    role
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    team
    user
    role
  ].freeze

  COLLECTION_FILTERS = {}.freeze

  def display_resource(team_member)
    "#{team_member.user.username} - #{team_member.team.name}"
  end
end

