require "administrate/base_dashboard"

class EventDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    accessible_teams: Field::HasMany,
    archived: Field::<PERSON><PERSON>an,
    confirmed_users: Field::HasMany,
    description: Field::Text,
    difficulty_level: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    end_time: Field::DateTime,
    event_category: Field::BelongsTo,
    event_instructors: Field::Has<PERSON>any,
    event_location: Field::BelongsTo,
    event_registrations: Field::Has<PERSON>any,
    instructors: Field::HasMany,
    is_online: Field::<PERSON>olean,
    max_participants: Field::Number,
    meeting_link: Field::String,
    member_price: Field::String.with_options(searchable: false),
    min_participants: Field::Number,
    prerequisites: Field::Text,
    price: Field::String.with_options(searchable: false),
    published_at: Field::DateTime,
    registered_users: Field::Has<PERSON>any,
    start_time: Field::DateTime,
    status: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    team_members: Field::HasMany,
    team_resource_accesses: Field::HasMany,
    title: Field::String,
    user: Field::BelongsTo,
    visibility: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    what_included: Field::Text,
    what_to_bring: Field::Text,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    title
    status
    start_time
    user
    event_category
    max_participants
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    accessible_teams
    archived
    confirmed_users
    description
    difficulty_level
    end_time
    event_category
    event_instructors
    event_location
    event_registrations
    instructors
    is_online
    max_participants
    meeting_link
    member_price
    min_participants
    prerequisites
    price
    published_at
    registered_users
    start_time
    status
    team_members
    team_resource_accesses
    title
    user
    visibility
    what_included
    what_to_bring
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    accessible_teams
    archived
    confirmed_users
    description
    difficulty_level
    end_time
    event_category
    event_instructors
    event_location
    event_registrations
    instructors
    is_online
    max_participants
    meeting_link
    member_price
    min_participants
    prerequisites
    price
    published_at
    registered_users
    start_time
    status
    team_members
    team_resource_accesses
    title
    user
    visibility
    what_included
    what_to_bring
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {
    published: ->(resources) { resources.where.not(published_at: nil) },
    draft: ->(resources) { resources.where(published_at: nil) },
    upcoming: ->(resources) { resources.where("start_time > ?", Time.current) },
    past: ->(resources) { resources.where("start_time < ?", Time.current) },
    online: ->(resources) { resources.where(is_online: true) },
    offline: ->(resources) { resources.where(is_online: false) }
  }.freeze

  # Overwrite this method to customize how events are displayed
  # across all pages of the admin dashboard.
  def display_resource(event)
    "#{event.title} (#{event.start_time&.strftime('%Y-%m-%d')})"
  end
end
