<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-4xl lg:py-16">
    <article class="mx-auto w-full format format-sm sm:format-base lg:format-lg format-blue dark:format-invert">
      <header class="mb-8 lg:mb-12 not-format">
        <div class="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
          <div>
            <h1 class="text-3xl font-extrabold leading-tight text-gray-900 lg:text-4xl dark:text-white mb-2">
              <%= @post.title %>
            </h1>
            <div class="flex flex-wrap items-center text-gray-500 dark:text-gray-400 text-sm">
              <div class="flex items-center mr-4">
                <i class="far fa-user mr-1"></i>
                <span><%= t('posts.show.metadata.author') %> <%= @post.user.username %></span>
              </div>
              <div class="flex items-center mr-4">
                <i class="far fa-clock mr-1"></i>
                <% if @post.published_at %>
                  <span><%= t('posts.show.metadata.published_at') %> <%= @post.published_at.strftime("%Y-%m-%d %H:%M") %></span>
                <% else %>
                  <span><%= t('common.time_formats.created_at') %> <%= @post.created_at.strftime("%Y-%m-%d %H:%M") %></span>
                <% end %>
              </div>
              <div class="flex items-center">
                <i class="far fa-bookmark mr-1"></i>
                <% if @post.status == 'published' %>
                  <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-xs dark:bg-green-900 dark:text-green-300"><%= t('posts.show.status.published') %></span>
                <% else %>
                  <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-xs dark:bg-yellow-900 dark:text-yellow-300"><%= t('posts.show.status.draft') %></span>
                <% end %>
              </div>
            </div>
          </div>

          <% if @post.user == current_user %>
            <div class="flex gap-2 mt-4 md:mt-0">
              <% if @post.status == 'draft' %>
                <%= link_to t('posts.actions.publish'), publish_post_path(@post),
                    class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800",
                    data: { turbo_method: :patch } %>
              <% else %>
                <%= link_to t('posts.actions.unpublish'), unpublish_post_path(@post),
                    class: "text-primary-500 hover:text-white border border-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-white dark:hover:bg-primary-600 dark:focus:ring-primary-800",
                    data: { turbo_method: :patch } %>
              <% end %>
              <%= link_to t('posts.actions.edit'), edit_post_path(@post), class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>
            </div>
          <% end %>
        </div>
      </header>

      <div class="prose prose-lg max-w-none dark:prose-invert">
        <%= @post.content %>
      </div>
    </article>
  </div>
</section>
