class Conversation < ApplicationRecord
  belongs_to :user
  belongs_to :assistant
  has_many :messages, as: :messageable, dependent: :destroy

  validates :title, presence: true

  # 查找最新的对话（不限定用户, 消息+对话的日期一起对比获取最新的）
  scope :latest, -> {
    left_joins(:messages)  # 使用左连接保持没有消息的对话
      .select("conversations.*, COALESCE(MAX(messages.created_at), conversations.created_at) AS latest_activity")
      .group("conversations.id")
      .order("latest_activity DESC")
  }

  def current_assistant
    Assistant.find_by(id: assistant_id)
  end

  def switch_assistant(new_assistant)
    update(assistant_id: new_assistant.id)
  end
end
