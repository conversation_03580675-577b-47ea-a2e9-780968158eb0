<section class="bg-white dark:bg-gray-900">
  <div class="px-4 py-8 mx-auto max-w-(--breakpoint-xl) lg:py-16">
    <div class="flex flex-col gap-4 justify-between items-center mb-8 md:flex-row">
      <div>
        <h1 class="text-3xl font-extrabold text-gray-900 dark:text-white"><%= t('knowledge_bases.index.title') %></h1>
        <p class="mt-2 text-lg text-gray-500 dark:text-gray-400">Manage and organize all your knowledge resources</p>
      </div>

      <div class="flex gap-4">
        <%= link_to new_knowledge_base_path, class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 inline-flex items-center" do %>
          <i class="fas fa-plus mr-2"></i><%= t('knowledge_bases.actions.create') %>
        <% end %>
      </div>
    </div>

    <div class="mb-6">
      <%= form_with url: knowledge_bases_path, method: :get, class: "flex flex-col md:flex-row items-center gap-4" do |f| %>
        <div class="flex-1 w-full">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <%= f.text_field :search,
                  placeholder: "Search by name or description...",
                  value: params[:search],
                  class: "bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" %>
          </div>
        </div>

        <div class="md:w-48">
          <div class="relative">
            <%= f.select :visibility,
                  options_for_select(
                    KnowledgeBase.visibilities.keys.map { |v| [v.humanize, v] },
                    params[:visibility]
                  ),
                  { include_blank: 'All Visibilities' },
                  class: "appearance-none bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" %>
            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <i class="fas fa-chevron-down text-gray-400"></i>
            </div>
          </div>
        </div>

        <%= f.submit t('common.filters.apply'), class: "w-full md:w-auto text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>
      <% end %>
    </div>

    <div class="mt-4 flex space-x-3 mb-6">
      <%= link_to t('knowledge_bases.index.title'), knowledge_bases_path,
          class: "inline-flex items-center rounded-lg px-4 py-2 text-sm font-medium #{current_page?(knowledge_bases_path) ? 'bg-primary-500 text-white hover:bg-primary-600' : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-primary-500'} shadow-xs focus:outline-hidden focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200" %>
      <%= link_to t('knowledge_bases.index.archived_title'), archived_knowledge_bases_path,
          class: "inline-flex items-center rounded-lg px-4 py-2 text-sm font-medium #{current_page?(archived_knowledge_bases_path) ? 'bg-primary-500 text-white hover:bg-primary-600' : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 hover:text-primary-500'} shadow-xs focus:outline-hidden focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200" %>
    </div>

    <%= form_tag(current_page?(archived_knowledge_bases_path) ? bulk_unarchive_knowledge_bases_path : bulk_archive_knowledge_bases_path,
                method: :post,
                id: "bulk_action_form",
                class: "space-y-4") do %>
      <div class="bulk-action-controls flex items-center gap-4 mb-4">
        <div class="relative flex-1 md:max-w-xs">
          <%= select_tag "bulk_action",
            options_for_select([
                        current_page?(archived_knowledge_bases_path) ?
                          ["Unarchive Selected", "unarchive"] :
                          ["Archive Selected", "archive"]
                      ]),
            prompt: "Select Bulk Action",
            class: "appearance-none bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 pr-8 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" %>
          <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <i class="fas fa-chevron-down text-gray-400"></i>
          </div>
        </div>
        <%= submit_tag "Apply",
              class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-4 py-2.5 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800",
              data: { disable_with: "Processing..." } %>
      </div>

      <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <% @knowledge_bases.each do |kb| %>
          <div id="knowledge_base_<%= kb.id %>" class="knowledge-base-card max-w-sm bg-white rounded-lg border border-gray-200 shadow-xs transition-shadow duration-300 ease-in-out dark:bg-gray-800 dark:border-gray-700 hover:shadow-lg">
            <div class="p-5">
              <div class="flex items-center mb-4">
                <%= check_box_tag "knowledge_base_ids[]",
                  kb.id,
                  false,
                  id: "knowledge_base_ids_#{kb.id}",
                  class: "w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded-xs focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" %>
                <h2 class="ml-3 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                    <% if allowed_to?(:show?, kb) %>
                      <%= link_to kb.name, knowledge_base_path(kb), class: "hover:text-primary-600 dark:hover:text-primary-500" %>
                    <% else %>
                      <%= kb.name %>
                    <% end %>
                </h2>
              </div>

              <div class="flex items-center mb-3">
                <span class="<%= visibility_badge_classes(kb.visibility) %>">
                  <%= kb.visibility.humanize %>
                </span>
                <% if kb.archived? %>
                  <span class="bg-gray-100 text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-xs dark:bg-gray-700 dark:text-gray-300">
                    <%= t('knowledge_bases.actions.archive') %>
                  </span>
                <% end %>
              </div>

              <p class="mb-3 font-normal text-gray-700 dark:text-gray-400">
                <%= truncate(kb.description, length: 100) %>
              </p>

            </div>
          </div>
        <% end %>
      </div>
    <% end %>

    <div class="mt-8">
      <%= paginate @knowledge_bases %>
    </div>
  </div>
</section>
