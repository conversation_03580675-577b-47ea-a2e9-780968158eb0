# 基本単位モジュールの日本語翻訳
ja:
  base_units:
    index:
      title: "基本単位"
      subtitle: "すべてのナレッジベースファイルを管理"
    show:
      title: "ファイル詳細"
      subtitle: "ファイルの詳細情報を表示"
      sections:
        file_info: "ファイル情報"
        file_preview: "ファイルプレビュー"
      fields:
        filename: "ファイル名"
        file_type: "ファイルタイプ"
        file_size: "ファイルサイズ"
        upload_time: "アップロード日時"
      messages:
        preview_not_supported: "このファイルタイプのプレビューはサポートされていません。ファイルを"
        download_to_view: "ダウンロードして表示できます"
        processing: "ファイルを処理中です。しばらくお待ちください..."
        encoding_error: "ファイルエンコーディング解析エラー: %{error}"
        loading: "読み込み中..."
      actions:
        return_to_list: "一覧へ戻る"
    fields:
      name: "名前"
      category: "カテゴリ"
      size: "サイズ"
      status: "ステータス"
    status:
      ready: "準備完了"
      processing: "処理中"
    actions:
      view: "表示"
      download: "ダウンロード"
      view_post: "投稿を表示"
      convert_to_post: "投稿に変換"
    categories:
      length: "長さ"
      mass: "質量"
      time: "時間"
      temperature: "温度"
      currency: "通貨"
      custom: "カスタム"
