class EventRegistrationsController < ApplicationController
  include Authentication

  before_action :require_authentication
  before_action :set_event
  before_action :set_registration, only: [ :show, :destroy, :confirm, :cancel ]

  def index
    # 只有活动创建者可以查看注册列表
    authorize! @event, to: :manage_registrations?

    @registrations = @event.event_registrations.includes(:user).order(:registered_at)
  end

  def show
    # 检查用户是否可以查看这个注册记录（自己的注册或活动创建者）
    unless @registration.user == current_user || @event.user == current_user
      raise ActionPolicy::Unauthorized
    end
  end

  def create
    # 使用 EventPolicy 检查注册权限
    authorize! @event, to: :register?

    result = Events::RegistrationService.call(@event, current_user, registration_params)

    if result.success?
      redirect_to @event, notice: t("events.registration.success")
    else
      redirect_to @event, alert: result.error
    end
  end

  def destroy
    # 检查用户是否可以取消这个注册（自己的注册或活动创建者）
    unless @registration.user == current_user || @event.user == current_user
      raise ActionPolicy::Unauthorized
    end

    result = Events::CancellationService.call(@registration)

    if result.success?
      redirect_to @event, notice: t("events.registration.cancelled")
    else
      redirect_to @event, alert: result.error
    end
  end

  def confirm
    # 只有活动创建者可以确认注册
    authorize! @event, to: :manage_registrations?

    if @registration.update(status: "confirmed")
      redirect_to event_registrations_path(@event), notice: t("events.registration.confirmed")
    else
      redirect_to event_registrations_path(@event), alert: t("events.registration.confirm_failed")
    end
  end

  def cancel
    # 只有活动创建者可以取消注册
    authorize! @event, to: :manage_registrations?

    if @registration.update(status: "cancelled")
      redirect_to event_registrations_path(@event), notice: t("events.registration.cancelled")
    else
      redirect_to event_registrations_path(@event), alert: t("events.registration.cancel_failed")
    end
  end

  private

  def set_event
    @event = Event.find(params[:event_id])
  end

  def set_registration
    @registration = @event.event_registrations.find(params[:id])
  end

  def registration_params
    params.fetch(:event_registration, {}).permit(:notes)
  end
end
