class EventsController < ApplicationController
  include Authentication  # 复用现有认证 concern

  allow_unauthenticated_access only: [ :index, :show, :search_suggestions, :map_data, :nearby_events, :search_locations, :calendar, :day_events ]
  before_action :set_event, only: [ :show, :edit, :update, :destroy, :publish, :cancel, :registrations ]

  def index
    authorize! Event, to: :index?
    @events = authorized_scope(Event.all)  # 使用 ActionPolicy 的作用域过滤
    @events = @events.published.unarchived
                     .includes(:user, :event_category, :event_location, :instructors)

    # 根据 tab 参数应用时间筛选
    @current_tab = params[:tab] || "upcoming"
    @events = apply_time_filter(@events)

    @events = apply_filters(@events)

    # 视图处理 - 支持 grid, calendar, map 视图
    @current_view = params[:view] || "grid"

    # 为所有视图准备日历数据（因为 JavaScript 可能会切换到日历视图）
    @date = params[:date] ? Date.parse(params[:date]) : Date.current
    @start_date = @date.beginning_of_month.beginning_of_week(:sunday)
    @end_date = @date.end_of_month.end_of_week(:sunday)

    case @current_view
    when "calendar"
      # 获取日期范围内的事件用于日历显示
      @events_by_date = @events.where(start_time: @start_date..@end_date.end_of_day)
                              .includes(:event_category, :event_location)
                              .group_by { |event| event.start_time.to_date }
    when "map"
      # 地图视图在同一个页面中，不需要重定向
      @events = @events.page(params[:page])
      # 也准备日历数据以防 JavaScript 切换
      @events_by_date = @events.where(start_time: @start_date..@end_date.end_of_day)
                              .includes(:event_category, :event_location)
                              .group_by { |event| event.start_time.to_date }
    else # 'grid' 或其他默认为网格视图
      @events = @events.page(params[:page])
      # 也准备日历数据以防 JavaScript 切换
      @events_by_date = @events.where(start_time: @start_date..@end_date.end_of_day)
                              .includes(:event_category, :event_location)
                              .group_by { |event| event.start_time.to_date }
    end

    respond_to do |format|
      format.html
      format.json { render json: @events }
    end
  end

  def show
    # 确保即使在允许未认证访问的情况下也恢复 session
    resume_session if cookies.signed[:session_id]

    authorize! @event, to: :show?
    if current_user
      @registration = current_user.event_registrations.where(event: @event).where.not(status: "cancelled").first
    else
      @registration = nil
    end
    @can_register = allowed_to?(:register?, @event)

    # 预加载报名者信息，避免 N+1 查询
    @event_registrations = @event.event_registrations.includes(:user).order(:registered_at)
  end

  def new
    @event = Event.new
    @event.user = current_user
    authorize! @event, to: :create?
  end

  def create
    @event = Event.new(event_params)
    @event.user = current_user
    authorize! @event, to: :create?

    if @event.save
      redirect_to @event, notice: t("events.created_successfully")
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    authorize! @event, to: :update?
  end

  def update
    authorize! @event, to: :update?

    if @event.update(event_params)
      redirect_to @event, notice: t("events.updated_successfully")
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    authorize! @event, to: :destroy?
    @event.archive! # 使用归档而不是删除
    redirect_to events_path, notice: t("events.archived_successfully")
  end

  def publish
    authorize! @event, to: :publish?

    Rails.logger.info "=== EventsController#publish START ==="
    Rails.logger.info "Event ID: #{@event.id}, Status: #{@event.status}"
    Rails.logger.info "User: #{current_user&.email_address}"

    result = @event.publish

    Rails.logger.info "Publish result: #{result}"
    Rails.logger.info "Event status after publish: #{@event.reload.status}"
    Rails.logger.info "Event errors: #{@event.errors.full_messages}" unless result

    if result
      Rails.logger.info "Redirecting with success notice"
      redirect_to @event, notice: t("events.published_successfully")
    else
      Rails.logger.error "Publish failed, redirecting with error"
      redirect_to @event, alert: t("events.publish_failed")
    end
  end

  def cancel
    authorize! @event, to: :cancel?

    if @event.update(status: "cancelled")
      # 发送取消通知
      EventCancellationJob.perform_later(@event)
      redirect_to @event, notice: t("events.cancelled_successfully")
    else
      redirect_to @event, alert: t("events.cancel_failed")
    end
  end

  # API 端点：搜索建议
  def search_suggestions
    authorize! Event, to: :index?  # 搜索建议使用与 index 相同的权限

    query = params[:q]
    limit = params[:limit] || 10

    # 为搜索建议创建缓存键
    cache_key = [
      "events_search_suggestions",
      Digest::MD5.hexdigest(query.to_s.downcase),
      limit,
      Event.maximum(:updated_at)&.to_i
    ].compact.join("/")

    cached_suggestions = Rails.cache.fetch(cache_key, expires_in: 30.minutes) do
      Event.search_suggestions(query, limit: limit)
    end

    render json: {
      suggestions: cached_suggestions,
      cache_generated_at: Time.current.iso8601
    }
  end

  # 地图数据API
  def map_data
    authorize! Event, to: :index?

    cache_key = [
      "events_map_data",
      params[:category_id],
      params[:city],
      params[:search],
      params[:tab],
      params[:date_range],
      params[:price_range],
      params[:difficulty_level],
      params[:sort_by],
      Event.maximum(:updated_at)&.to_i,
      EventLocation.maximum(:updated_at)&.to_i
    ].compact.join("/")

    cached_data = Rails.cache.fetch(cache_key, expires_in: 15.minutes) do
      events = authorized_scope(Event.all)
      events = events.published.unarchived
                     .includes(:user, :event_category, :event_location)

      # 根据 tab 参数应用时间筛选
      current_tab = params[:tab] || "upcoming"
      events = apply_time_filter_for_tab(events, current_tab)

      events = apply_filters(events)

      # 只返回有地理位置的事件
      map_events = events.joins(:event_location)
                        .where.not(event_locations: { latitude: nil, longitude: nil })
                        .limit(500) # 限制数据量以提高性能

      {
        events: map_events.map do |event|
          serialize_event_for_map(event)
        end,
        meta: {
          total_count: map_events.count,
          bounds: calculate_map_bounds(map_events),
          cache_generated_at: Time.current.iso8601
        }
      }
    end

    render json: cached_data
  end

  # 附近事件搜索
  def nearby_events
    authorize! Event, to: :index?

    lat = params[:latitude].to_f
    lng = params[:longitude].to_f
    radius = params[:radius]&.to_f || 10 # 默认10公里

    # 验证坐标有效性
    if lat.zero? && lng.zero?
      render json: { error: "Invalid coordinates" }, status: :bad_request
      return
    end

    # 为附近事件搜索创建缓存键
    # 使用坐标的精确到小数点后3位（约110米精度）来平衡缓存效率和准确性
    lat_rounded = lat.round(3)
    lng_rounded = lng.round(3)

    cache_key = [
      "events_nearby",
      lat_rounded,
      lng_rounded,
      radius,
      Event.maximum(:updated_at)&.to_i,
      EventLocation.maximum(:updated_at)&.to_i
    ].compact.join("/")

    cached_data = Rails.cache.fetch(cache_key, expires_in: 10.minutes) do
      events = Event.joins(:event_location)
                    .merge(EventLocation.within_radius(lat, lng, radius))
                    .published.upcoming.unarchived
                    .includes(:event_category, :event_location, :user)
                    .limit(100)

      {
        events: events.map { |event| serialize_event_for_map(event) },
        center: [ lat, lng ],
        radius: radius,
        count: events.count,
        cache_generated_at: Time.current.iso8601
      }
    end

    render json: cached_data
  end

  # 位置搜索建议
  def search_locations
    authorize! Event, to: :index?

    query = params[:q]
    limit = params[:limit] || 10

    # 为位置搜索建议创建缓存键
    cache_key = [
      "locations_search_suggestions",
      Digest::MD5.hexdigest(query.to_s.downcase),
      limit,
      EventLocation.maximum(:updated_at)&.to_i
    ].compact.join("/")

    cached_locations = Rails.cache.fetch(cache_key, expires_in: 1.hour) do
      EventLocation.search_suggestions(query, limit: limit)
    end

    render json: {
      locations: cached_locations,
      cache_generated_at: Time.current.iso8601
    }
  end

  def registrations
    authorize! @event, to: :manage_registrations?
    @registrations = @event.event_registrations.includes(:user).order(:registered_at)
  end

  def calendar
    authorize! Event, to: :index?

    # 设置日历视图参数，与 index 方法保持一致
    @events = authorized_scope(Event.all)
    @events = @events.published.unarchived
                     .includes(:user, :event_category, :event_location, :instructors)

    # 根据 tab 参数应用时间筛选
    @current_tab = params[:tab] || "upcoming"
    @events = apply_time_filter(@events)
    @events = apply_filters(@events)

    # 设置日历视图
    @current_view = "calendar"

    # 为日历视图准备数据
    @date = params[:date] ? Date.parse(params[:date]) : Date.current
    @start_date = @date.beginning_of_month.beginning_of_week(:sunday)
    @end_date = @date.end_of_month.end_of_week(:sunday)

    # 获取日期范围内的事件用于日历显示
    @events_by_date = @events.where(start_time: @start_date..@end_date.end_of_day)
                            .includes(:event_category, :event_location)
                            .group_by { |event| event.start_time.to_date }

    respond_to do |format|
      format.html { render :index }  # 使用相同的 index 模板
      format.json { render json: @events }
    end
  end

  def day_events
    authorize! Event, to: :index?

    # 获取指定日期的事件
    date = Date.parse(params[:date])
    @events = authorized_scope(Event.all)
              .published
              .unarchived
              .where(start_time: date.beginning_of_day..date.end_of_day)
              .includes(:event_category, :event_location, :user)
              .order(:start_time)

    respond_to do |format|
      format.html { render partial: "events/day_events_list", locals: { events: @events } }
      format.json { render json: @events }
    end
  end

  private

  def apply_time_filter(events)
    case @current_tab
    when "upcoming"
      events.upcoming
    when "past"
      events.past
    when "all"
      events
    else
      events.upcoming
    end
  end

  # 为地图API使用的时间筛选方法
  def apply_time_filter_for_tab(events, tab)
    case tab
    when "upcoming"
      events.upcoming
    when "past"
      events.past
    when "all"
      events
    else
      events.upcoming
    end
  end

  def apply_filters(events)
    events = events.by_category(params[:category_id]) if params[:category_id].present?
    events = events.in_city(params[:city]) if params[:city].present?

    # Rails 8 FTS5 搜索
    if params[:search].present?
      begin
        case params[:search_type]
        when "phrase"
          events = events.phrase_search(params[:search])
        when "field"
          events = events.search_in_field(params[:field], params[:search])
        when "advanced"
          events = events.advanced_search(params[:search])
        else
          events = events.fts_search(params[:search])
        end
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "Search failed, using fallback: #{e.message}"
        events = events.fallback_search(params[:search])
      end
    end

    # 明确指定表名以避免字段名冲突
    events = events.where(events: { visibility: params[:visibility] }) if params[:visibility].present?

    # 时间范围筛选
    if params[:date_range].present?
      events = case params[:date_range]
      when "today"
        events.where("DATE(start_time) = ?", Date.current)
      when "this_week"
        events.where(start_time: Date.current.beginning_of_week..Date.current.end_of_week)
      when "this_month"
        events.where(start_time: Date.current.beginning_of_month..Date.current.end_of_month)
      when "next_month"
        next_month = Date.current.next_month
        events.where(start_time: next_month.beginning_of_month..next_month.end_of_month)
      else
        events
      end
    end

    # 价格范围筛选
    if params[:price_range].present?
      events = case params[:price_range]
      when "free"
        events.where(price: 0)
      when "under_50"
        events.where("price > 0 AND price < 50")
      when "50_to_100"
        events.where(price: 50..100)
      when "over_100"
        events.where("price > 100")
      else
        events
      end
    end

    # 难度级别筛选
    events = events.where(difficulty_level: params[:difficulty_level]) if params[:difficulty_level].present?

    # 排序
    events = case params[:sort_by]
    when "date_desc"
      events.order(start_time: :desc)
    when "price_asc"
      events.order(price: :asc)
    when "price_desc"
      events.order(price: :desc)
    when "name_asc"
      events.order(title: :asc)
    when "popularity"
      # 按照确认的报名人数排序
      events.left_joins(:event_registrations)
            .where(event_registrations: { status: [ "confirmed", "attended" ] })
            .group("events.id")
            .order("COUNT(event_registrations.id) DESC, events.start_time ASC")
    else # 默认 'date_asc'
      events.order(start_time: :asc)
    end

    events
  end

  # 序列化事件数据用于地图显示
  def serialize_event_for_map(event)
    {
      id: event.id,
      title: event.title,
      description: truncate_text(event.description, 100),
      latitude: event.event_location.latitude,
      longitude: event.event_location.longitude,
      location_name: event.event_location.name,
      address: event.event_location.address,
      start_time: event.start_time.iso8601,
      end_time: event.end_time.iso8601,
      category: {
        name: event.event_category&.name,
        color: event.event_category&.color || "#3B82F6"
      },
      price: event.price,
      member_price: event.member_price,
      max_participants: event.max_participants,
      registered_count: event.event_registrations.confirmed.count,
      is_online: event.is_online,
      difficulty_level: event.difficulty_level,
      url: event_path(event),
      can_register: allowed_to?(:register?, event)
    }
  end

  # 计算地图边界
  def calculate_map_bounds(events)
    return nil if events.empty?

    lats = events.map { |e| e.event_location.latitude }.compact
    lngs = events.map { |e| e.event_location.longitude }.compact

    return nil if lats.empty? || lngs.empty?

    {
      north: lats.max,
      south: lats.min,
      east: lngs.max,
      west: lngs.min
    }
  end

  # 文本截断辅助方法
  def truncate_text(text, length = 100)
    return "" if text.blank?

    if text.length > length
      "#{text[0, length]}..."
    else
      text
    end
  end

  def set_event
    @event = Event.find(params[:id])
  end

  def event_params
    params.require(:event).permit(
      :title, :description, :prerequisites, :start_time, :end_time,
      :price, :member_price, :max_participants, :min_participants,
      :status, :difficulty_level, :what_to_bring, :what_included, :is_online,
      :meeting_link, :visibility, :event_category_id, :event_location_id,
      :cover_image, documents: []
    )
  end
end
