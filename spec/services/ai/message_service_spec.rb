require 'rails_helper'

RSpec.describe Ai::MessageService do
  # 共享上下文，用于测试数据准备
  let(:user) { create(:user) }

  describe '.get_messageable_history' do
    describe '消息历史格式化' do
      context '对话类型消息容器' do
        let(:conversation) { create(:conversation, user: user) }
        let!(:message1) { create(:message, messageable: conversation, role: 'user', content: 'Hello', created_at: 1.hour.ago) }
        let!(:message2) { create(:message, messageable: conversation, role: 'assistant', content: 'Hi there!', created_at: 50.minutes.ago) }
        # 确保 message3 的内容不是 "I'm thinking..." 以免被过滤
        let!(:message3) { create(:message, :with_tool_calls, content: "Assistant with tools", messageable: conversation, created_at: 40.minutes.ago) }
        # 添加一个会被过滤的消息
        let!(:processing_message) { create(:message, :assistant, content: "I'm thinking...", messageable: conversation, created_at: 35.minutes.ago) }

        it '返回格式化的历史消息数组' do
          history = described_class.get_messageable_history(conversation)

          # 基本验证 (期望数量应排除 processing_message)
          expect(history).to be_an(Array)
          expect(history.size).to eq(3) # message1, message2, message3

           # 验证各类消息格式
           expect(history[0]).to include(
             id: message1.id, # 添加 ID 验证
             role: 'user',
             content: 'Hello',
             tool_calls: [],
             tool_call_id: nil
           )

           expect(history[1]).to include(
             id: message2.id, # 添加 ID 验证
             role: 'assistant',
             content: 'Hi there!'
           )

           # 验证工具调用消息
           tool_call_message = history[2]
           expect(tool_call_message).to include(
             id: message3.id,
             role: 'assistant',
             content: "Assistant with tools" # 确认 :with_tool_calls factory 是否设置 content
           )
          expect(tool_call_message[:tool_calls]).to be_present
        end

        it '按创建时间升序排序消息' do
          history = described_class.get_messageable_history(conversation)

          expect(history.map { |m| m[:id] }).to eq([ message1.id, message2.id, message3.id ]) # 按 ID 验证顺序
        end

        it '包含工具使用记录的消息' do
          # 创建带工具调用ID的工具消息
          tool_message = create(:message, :tool, messageable: conversation, tool_call_id: 'call_123', created_at: 30.minutes.ago)

          history = described_class.get_messageable_history(conversation)
          expect(history.size).to eq(4) # message1, message2, message3, tool_message
           # 找到工具消息
           tool_message_history = history.find { |m| m[:role] == 'tool' }
           expect(tool_message_history).to include(
             id: tool_message.id, # 添加 ID 验证
             role: 'tool',
             tool_call_id: 'call_123'
           )
        end

        it '使用特定查询方式加载对话消息' do
          # 验证调用链
          messages_relation = conversation.messages # 使用真实的 relation
          for_context_relation = double('ForContextRelation')
          ordered_relation = double('OrderedRelation')

          # 验证 for_langchain_assistant_context 被调用
          expect(messages_relation).to receive(:for_langchain_assistant_context).and_return(for_context_relation)
          expect(for_context_relation).to receive(:ordered).and_return(ordered_relation)
          allow(ordered_relation).to receive(:map).and_return([]) # Stub map

          described_class.get_messageable_history(conversation)
        end
      end

      context '非对话类型的消息容器' do
        let(:assistant) { create(:assistant, user: user) }
        # system 消息会被 for_langchain_assistant_context 过滤掉
        let!(:message1) { create(:message, messageable: assistant, role: 'system', content: 'System message', created_at: 1.day.ago) }
        let!(:message2) { create(:message, messageable: assistant, role: 'user', content: 'User message', created_at: 23.hours.ago) }

        it '返回格式化的历史消息数组' do
          history = described_class.get_messageable_history(assistant)

          expect(history).to be_an(Array)
          expect(history.size).to eq(1) # 只包含 user message

           # 使用包含匹配进行更精确的验证
           expect(history[0]).to include(
             id: message2.id, # 添加 ID 验证
             role: 'user',
             content: 'User message'
           )
        end

        it '使用不同的查询方式加载非对话消息' do
          # 验证调用链
          messages_relation = assistant.messages # 使用真实的 relation
          for_context_relation = double('ForContextRelation')
          ordered_relation = double('OrderedRelation')

          expect(messages_relation).to receive(:for_langchain_assistant_context).and_return(for_context_relation)
          expect(for_context_relation).to receive(:ordered).and_return(ordered_relation)
          allow(ordered_relation).to receive(:map).and_return([]) # Stub map

          # 确认不会调用includes (因为 for_langchain_assistant_context 已经处理)
          expect(ordered_relation).not_to receive(:includes)

          described_class.get_messageable_history(assistant)
        end
      end
    end
  end

  describe '.broadcast' do
    describe '消息广播' do
      context '对话类型消息' do
        let(:conversation) { create(:conversation, user: user) }

        context '新创建的消息' do
          let(:message) { build(:message, messageable: conversation) }

          before do
            allow(message).to receive(:previously_new_record?).and_return(true)
          end

          it '使用broadcast_append_to广播新消息' do
            expect(Turbo::StreamsChannel).to receive(:broadcast_append_to).with(
              "conversation_#{conversation.id}",
              target: "messages",
              partial: "ai/messages/message",
              locals: { message: message }
            )

            described_class.broadcast(message)
          end
        end

        context '更新的消息' do
          let(:message) { create(:message, messageable: conversation) }

          before do
            allow(message).to receive(:previously_new_record?).and_return(false)
          end

          it '使用broadcast_replace_to广播更新消息' do
            expect(Turbo::StreamsChannel).to receive(:broadcast_replace_to).with(
              "conversation_#{conversation.id}",
              target: "message_#{message.id}",
              partial: "ai/messages/message",
              locals: { message: message }
            )

            described_class.broadcast(message)
          end
        end
      end

      context '其他类型消息容器' do
        let(:assistant) { create(:assistant, user: user) }
        let(:message) { create(:message, messageable: assistant) }

        before do
          allow(message).to receive(:previously_new_record?).and_return(true)
        end

        it '根据消息容器类型构建正确的流名称' do
          expect(Turbo::StreamsChannel).to receive(:broadcast_append_to).with(
            "assistant_#{assistant.id}",
            target: "messages",
            partial: "ai/messages/message",
            locals: { message: message }
          )

          described_class.broadcast(message)
        end
      end

      context '边缘情况测试' do
        it '处理nil内容的消息' do
          conversation = create(:conversation, user: user)
          message = create(:message, :with_tool_calls, messageable: conversation)

          # 不应引发错误
          expect { described_class.broadcast(message) }.not_to raise_error
        end

        it '处理具有特殊字符的消息内容' do
          conversation = create(:conversation, user: user)
          message = create(:message, messageable: conversation, content: '<script>alert("XSS");</script>')

          # 不应引发错误
          expect { described_class.broadcast(message) }.not_to raise_error
        end
      end
    end
  end

  # 新增：测试类方法
  describe '.create_and_process' do
    let(:conversation) { create(:conversation, user: user) }
    let(:content) { "User input content" }

    before do
      # 阻止异步 Job 执行
      allow(Ai::ResponseJob).to receive(:perform_later)
    end

    it '创建用户消息和助手消息' do
      expect {
        described_class.create_and_process(messageable: conversation, content: content)
      }.to change(conversation.messages, :count).by(2)

      # 获取方法返回的消息实例
      result = described_class.create_and_process(messageable: conversation, content: content)
      user_message = result[:user_message]
      assistant_message = result[:assistant_message]

      # 验证返回的实例
      expect(user_message.role).to eq('user')
      expect(user_message.content).to eq(content)

      expect(assistant_message.role).to eq('assistant')
      expect(assistant_message.content).to eq("I'm thinking...")
      # 验证返回实例的初始状态
      expect(assistant_message.status).to eq(:processing) # 使用 symbol
    end

    it '调用 .process_async' do
      user_message_double = instance_double(Message, id: 1)
      assistant_message_double = instance_double(Message)
      allow(conversation.messages).to receive(:create!).with(role: "user", content: content).and_return(user_message_double)
      allow(conversation.messages).to receive(:create!).with(role: "assistant", content: "I'm thinking...", status: :processing).and_return(assistant_message_double)

      expect(described_class).to receive(:process_async).with(
        messageable: conversation,
        user_message_id: user_message_double.id,
        assistant_message: assistant_message_double
      )
      described_class.create_and_process(messageable: conversation, content: content)
    end

    it '返回创建的用户和助手消息' do
      result = described_class.create_and_process(messageable: conversation, content: content)
      expect(result).to be_a(Hash)
      expect(result[:user_message]).to be_a(Message)
      expect(result[:user_message].role).to eq('user')
      expect(result[:assistant_message]).to be_a(Message)
      expect(result[:assistant_message].role).to eq('assistant')
    end
  end

  describe '.process_async' do
    let(:conversation) { create(:conversation, user: user) }
    let(:user_message) { create(:message, :user, messageable: conversation) }
    let(:assistant_message) { create(:message, :assistant, messageable: conversation) }

    it '调用 Ai::ResponseJob.perform_later' do
      expect(Ai::ResponseJob).to receive(:perform_later).with(
        conversation,
        user_message.id,
        assistant_message
      )
      described_class.process_async(
        messageable: conversation,
        user_message_id: user_message.id,
        assistant_message: assistant_message
      )
    end
  end

  describe '.process_sync' do
    let(:conversation) { create(:conversation, user: user) }
    let(:user_message) { create(:message, :user, messageable: conversation) }
    let(:assistant_message) { create(:message, :assistant, messageable: conversation) }
    let(:service_instance) { instance_double(described_class) }

    before do
      allow(described_class).to receive(:new).and_return(service_instance)
      allow(service_instance).to receive(:process)
    end

    it '实例化服务并调用 process' do
      expect(described_class).to receive(:new).with(
        messageable: conversation,
        user_message_id: user_message.id,
        assistant_message: assistant_message
      ).and_return(service_instance)
      expect(service_instance).to receive(:process)

      described_class.process_sync(
        messageable: conversation,
        user_message_id: user_message.id,
        assistant_message: assistant_message
      )
    end
  end

  # 新增：测试实例方法
  describe '实例方法' do
    let(:conversation) { create(:conversation, user: user) }
    let(:user_message) { create(:message, :user, messageable: conversation) }
     let(:assistant_message) { create(:message, :assistant, messageable: conversation) }
     let(:assistant) { conversation.assistant }
     # 修正 NameError: 移除具体的类约束或使用已知类
     let(:llm_adapter_double) { instance_double('Langchain::LLM::BaseAdapter') } # 使用一个基础类或移除约束
     let(:messages_double) { double('Langchain Messages', any?: false, '<<': nil) } # 模拟消息列表
     # 确保 Langchain::Assistant 被正确模拟
     let(:langchain_assistant_double) { instance_double('Langchain::Assistant', llm_adapter: llm_adapter_double, messages: messages_double) }

     subject(:service) do
      described_class.new(
        messageable: conversation,
        user_message_id: user_message.id,
        assistant_message: assistant_message
      )
    end

    before do
      # 模拟 Assistant 模型的方法
      allow(conversation).to receive(:assistant).and_return(assistant)
      allow(assistant).to receive(:set_langchain_assistant).with(assistant_message, user_message_id: user_message.id)
      # 模拟 Langchain::Assistant 实例及其方法
      allow(assistant).to receive(:instance_variable_get).with(:@langchain_assistant).and_return(langchain_assistant_double)
      # allow(langchain_assistant_double).to receive(:llm_adapter).and_return(llm_adapter_double) # 已在 let 中定义
      # allow(langchain_assistant_double).to receive(:messages).and_return(messages_double) # 已在 let 中定义
      allow(langchain_assistant_double).to receive(:run!)
      # 模拟 build_message
      allow(llm_adapter_double).to receive(:build_message).and_return(double('Langchain Message', role: 'user', content: user_message.content))
      # 模拟 append_new_message_to_cache
      allow(assistant).to receive(:append_new_message_to_cache)
    end

    describe '#initialize' do
      it '正确设置实例变量' do
        expect(service.messageable).to eq(conversation)
        expect(service.user_message).to eq(user_message)
        expect(service.assistant_message).to eq(assistant_message)
      end

      it '当 user_message_id 不存在时抛出错误' do
        expect {
          described_class.new(
            messageable: conversation,
            user_message_id: -1, # 不存在的 ID
            assistant_message: assistant_message
          )
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end

    describe '#process' do
      it '获取 assistant 对象' do
        expect(conversation).to receive(:assistant).and_return(assistant)
        service.process
      end

      it '调用 assistant.set_langchain_assistant' do
        expect(assistant).to receive(:set_langchain_assistant).with(assistant_message, user_message_id: user_message.id)
        service.process
      end

      it '构建当前用户消息的 Langchain 对象' do
        expect(llm_adapter_double).to receive(:build_message).with(role: 'user', content: user_message.content)
        service.process
      end

      context '当用户消息不在缓存中时' do
        before do
          allow(messages_double).to receive(:any?).and_return(false) # 模拟缓存未命中（或消息不存在）
        end

        it '将当前用户消息添加到 Langchain 助手消息列表' do
          expect(messages_double).to receive(:<<).with(an_object_having_attributes(role: 'user', content: user_message.content))
          service.process
        end

        it '调用 assistant.append_new_message_to_cache' do
          expect(assistant).to receive(:append_new_message_to_cache).with(an_object_having_attributes(role: 'user', content: user_message.content))
          service.process
        end
      end

      context '当用户消息已在缓存中时' do
         before do
           # 模拟消息已存在于缓存加载的 messages_double 中
           allow(messages_double).to receive(:any?).and_return(true)
         end

         it '不将重复的用户消息添加到 Langchain 助手消息列表' do
           expect(messages_double).not_to receive(:<<)
           service.process
         end

         it '不调用 assistant.append_new_message_to_cache' do
           expect(assistant).not_to receive(:append_new_message_to_cache)
           service.process
         end
      end


      it '调用 langchain_assistant.run!' do
        expect(langchain_assistant_double).to receive(:run!)
        service.process
      end

      context '发生错误时' do
        let(:error) { StandardError.new("Test error") }

        before do
          allow(langchain_assistant_double).to receive(:run!).and_raise(error)
          # 允许 update 被调用以进行错误处理
          allow(assistant_message).to receive(:update)
        end

        it '调用 handle_error' do
          # 使用 expect_any_instance_of 可能更简单，但这里我们已有 service 实例
          expect(service).to receive(:handle_error).with(error).and_call_original
          expect { service.process }.to raise_error(StandardError, "Test error")
        end

        it '更新助手消息状态为 failed' do
          expect(assistant_message).to receive(:update).with(
            content: "Sorry, an error occurred: Test error",
            status: :failed
          )
          expect { service.process }.to raise_error(StandardError, "Test error")
        end

        it '重新抛出异常' do
          expect { service.process }.to raise_error(StandardError, "Test error")
        end
      end
    end
  end

  # 性能测试示例 - 可选，通常在专门的性能测试套件中
  describe '性能考量', :performance do
    it '处理大量消息时保持高效' do
      skip "仅在性能测试环境中运行" unless ENV['PERFORMANCE_TESTS']

      conversation = create(:conversation, user: user)

      # 创建100条消息
      100.times do |i|
        create(:message,
          messageable: conversation,
          role: i.even? ? 'user' : 'assistant',
          content: "Message #{i}",
          created_at: (100 - i).minutes.ago
        )
      end

      # 性能期望
      expect {
        described_class.get_messageable_history(conversation)
      }.to perform_under(200).ms
    end
  end
end
