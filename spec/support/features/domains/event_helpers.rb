# frozen_string_literal: true

module FeaturesHelper
  # ==========================================
  # 事件创建和管理辅助方法
  # ==========================================

  # 创建事件
  # @param attributes [Hash] 事件属性
  def create_event(attributes = {})
    visit new_event_path
    fill_event_form(attributes)
    click_button 'Create Event'
  end

  # 填写事件表单
  # @param attributes [Hash] 事件属性
  def fill_event_form(attributes = {})
    fill_in 'Title', with: attributes[:title] if attributes[:title]
    fill_in 'Description', with: attributes[:description] if attributes[:description]

    if attributes[:price]
      fill_in 'Price', with: attributes[:price]
    end

    if attributes[:max_participants]
      fill_in 'Max Participants', with: attributes[:max_participants]
    end

    if attributes[:event_category]
      select attributes[:event_category].name, from: 'Category'
    end

    # 简化时间处理 - 跳过复杂的日期时间选择器
    # 在实际测试中，我们可能需要使用工厂来创建带有时间的事件
  end

  # 编辑事件
  # @param event [Event] 事件对象
  # @param attributes [Hash] 更新的属性
  def edit_event(event, attributes = {})
    visit edit_event_path(event)
    fill_event_form(attributes)
    click_button 'Update Event'
  end

  # 发布事件
  # @param event [Event] 事件对象
  def publish_event(event)
    # 发布事件需要通过事件详情页面的发布按钮
    visit event_path(event)
    if page.has_link?('Publish')
      click_link 'Publish'
    elsif page.has_button?('Publish')
      click_button 'Publish'
    end
  end

  # 取消事件
  # @param event [Event] 事件对象
  def cancel_event(event)
    # 取消事件的按钮在编辑页面，而不是详情页面
    visit edit_event_path(event)

    # 查找取消事件的按钮（使用国际化）
    accept_confirm do
      click_button I18n.t('events.edit.cancel_this_event')
    end
  end

  # ==========================================
  # 搜索和筛选辅助方法
  # ==========================================

  # 搜索事件
  # @param query [String] 搜索关键词
  def search_events(query)
    # 使用 aria-label 来定位搜索字段
    fill_in 'Search events', with: query
    click_button I18n.t('events.search.filters')
  rescue Capybara::ElementNotFound
    # 备用方案：使用字段名
    fill_in 'search', with: query
    click_button I18n.t('events.search.filters')
  end

  # 按分类筛选事件
  # @param category_name [String] 分类名称
  def filter_events_by_category(category_name)
    select category_name, from: 'category_id'
    click_button I18n.t('events.search.filters')
  end

  # 按价格筛选事件
  # @param price_range [String] 价格范围
  def filter_events_by_price(price_range)
    select price_range, from: 'price_range'
    click_button I18n.t('events.search.filters')
  end

  # 通用筛选方法
  # @param filters [Hash] 筛选条件
  def filter_events(filters = {})
    if filters[:category]
      select filters[:category], from: 'category_id'
    end

    if filters[:location]
      select filters[:location], from: 'city'
    end

    if filters[:price_range]
      select filters[:price_range], from: 'price_range'
    end

    click_button I18n.t('events.search.filters')
  end

  # ==========================================
  # 断言和验证辅助方法
  # ==========================================

  # 验证事件在列表中
  # @param event [Event] 事件对象
  def expect_event_in_list(event)
    expect(page).to have_content(event.title)
  end

  # 验证事件不在列表中
  # @param event [Event] 事件对象
  def expect_event_not_in_list(event)
    # 更精确的检查，避免在页面其他地方找到标题
    within('.events-list, .grid, main') do
      expect(page).not_to have_content(event.title)
    end
  rescue Capybara::ElementNotFound
    # 如果没有找到容器，使用通用检查
    expect(page).not_to have_content(event.title)
  end

  # 验证成功通知
  # @param message [String] 通知消息
  def expect_success_notice(message)
    expect(page).to have_content(message)
    expect(page).to have_selector('.notice, .alert-success, .flash-success')
  end

  # 验证错误通知
  # @param message [String] 错误消息
  def expect_error_notice(message)
    # 检查页面是否包含错误信息，不强制要求特定的CSS类
    expect(page).to have_content(message)
  end

  # 验证事件详情
  # @param event [Event] 事件对象
  def expect_event_details(event)
    expect(page).to have_content(event.title)
    expect(page).to have_content(event.description) if event.description.present?
  end

  # 验证事件已发布
  def expect_event_published
    expect(page).to have_content(I18n.t('events.published_successfully'))
  end

  # 验证事件已取消
  def expect_event_cancelled
    expect(page).to have_content(I18n.t('events.cancelled_successfully'))
  end

  # ==========================================
  # 事件报名相关辅助方法
  # ==========================================

  # 报名事件
  # @param event [Event] 事件对象
  # @param notes [String] 报名备注
  def register_for_event(event, notes: nil)
    visit event_path(event)
    if notes
      fill_in 'event_registration_notes', with: notes
    end
    click_button I18n.t('events.registration.register_button')
  end

  # 取消报名
  # @param event [Event] 事件对象
  def cancel_registration(event)
    visit event_path(event)
    accept_confirm do
      # 在事件详情页面，取消报名是一个按钮而不是链接
      begin
        click_button I18n.t('events.registration.cancel_registration')
      rescue Capybara::ElementNotFound
        # 尝试英文版本
        click_button 'Cancel Registration'
      end
    end
  end

  # 验证报名成功
  def expect_registration_success
    expect(page).to have_content(I18n.t('events.registration.success'))
    expect(page).to have_content(I18n.t('events.show.already_registered'))
  end

  # 验证取消报名成功
  def expect_cancellation_success
    expect(page).to have_content(I18n.t('events.registration.cancelled'))
    expect(page).to have_button(I18n.t('events.registration.register_button'))
    expect(page).not_to have_content(I18n.t('events.show.already_registered'))
  end

  # ==========================================
  # 等待和JavaScript辅助方法
  # ==========================================

  # 等待搜索建议出现
  def wait_for_search_suggestions
    wait_for_element('[data-search-suggestions-target="results"]')
  end

  # 等待模态框出现
  def wait_for_modal
    wait_for_element('[data-modal], .modal')
  end

  # 等待页面加载完成
  def wait_for_page_load
    expect(page).to have_selector('main', wait: 10)
  end

  # 通用等待元素方法
  def wait_for_element(selector, timeout: 10)
    expect(page).to have_selector(selector, wait: timeout)
  end

  # ==========================================
  # 事件管理权限相关辅助方法
  # ==========================================

  # 验证管理操作可见
  # @param event [Event] 事件对象
  def expect_management_actions_visible(event)
    visit event_path(event)
    expect(page).to have_content(I18n.t('events.actions.management_actions'))
    expect(page).to have_link(I18n.t('events.actions.edit_event'))
    expect(page).to have_link(I18n.t('events.actions.manage_registrations'))
    expect(page).to have_link(I18n.t('events.actions.delete_event'))
  end

  # 验证管理操作不可见
  # @param event [Event] 事件对象
  def expect_management_actions_hidden(event)
    visit event_path(event)
    expect(page).not_to have_content(I18n.t('events.actions.management_actions'))
    expect(page).not_to have_link(I18n.t('events.actions.edit_event'))
    expect(page).not_to have_link(I18n.t('events.actions.manage_registrations'))
    expect(page).not_to have_link(I18n.t('events.actions.delete_event'))
  end

  # ==========================================
  # 事件列表和分页辅助方法
  # ==========================================

  # 切换到指定标签页
  # @param tab [String] 标签页名称 ('upcoming', 'past', 'all')
  def switch_to_events_tab(tab)
    visit events_path(tab: tab)
  end

  # 验证标签页激活状态
  # @param tab_name [String] 标签页名称
  def expect_tab_active(tab_name)
    expect(page).to have_selector('a.text-primary-600', text: I18n.t("events.index.tabs.#{tab_name}"))
  end

  # 验证分页存在
  def expect_pagination_present
    # 检查是否有"加载更多"按钮或分页链接
    expect(
      page.has_link?(I18n.t('common.load_more')) || page.has_content?('Page')
    ).to be true
  end

  # 点击下一页
  def click_next_page
    click_link 'Next'
  end

  # 点击上一页
  def click_previous_page
    click_link 'Previous'
  end

  # ==========================================
  # 文件上传辅助方法
  # ==========================================

  # 上传事件封面图片
  # @param file_path [String] 文件路径
  def upload_event_cover_image(file_path = 'spec/fixtures/files/sample_image.jpg')
    attach_file I18n.t('events.fields.cover_image'), Rails.root.join(file_path)
  end

  # 上传事件文档
  # @param file_path [String] 文件路径
  def upload_event_documents(file_path = 'spec/fixtures/files/sample_document.pdf')
    attach_file I18n.t('events.fields.documents'), Rails.root.join(file_path)
  end

  # 验证文件已上传
  # @param event [Event] 事件对象
  # @param attachment_type [Symbol] 附件类型 (:cover_image, :documents)
  def expect_file_uploaded(event, attachment_type)
    case attachment_type
    when :cover_image
      expect(event.cover_image).to be_attached
    when :documents
      expect(event.documents).to be_attached
    end
  end

  # ==========================================
  # 事件状态和容量辅助方法
  # ==========================================

  # 验证事件已满员
  def expect_event_full
    expect(page).to have_content(I18n.t('events.show.event_full'))
    expect(page).not_to have_button(I18n.t('events.registration.register_button'))
  end

  # 验证事件容量信息
  # @param registered_count [Integer] 已报名人数
  # @param max_participants [Integer] 最大参与人数
  def expect_capacity_info(registered_count, max_participants = nil)
    if max_participants
      expect(page).to have_content("#{registered_count} / #{max_participants}")
      remaining = max_participants - registered_count
      if remaining > 0
        expect(page).to have_content(I18n.t('events.show.spots_remaining', count: remaining))
      end
    else
      expect(page).to have_content("#{registered_count} #{I18n.t('events.show.registered')}")
      expect(page).not_to have_content('spots remaining')
    end
  end

  # 验证免费事件显示
  def expect_free_event_display
    expect(page).to have_content(I18n.t('events.show.free'))
    expect(page).not_to have_content('¥')
  end

  # 验证付费事件显示
  # @param price [Float] 价格
  def expect_paid_event_display(price)
    expect(page).to have_content("¥#{price}")
  end

  # 验证会员优惠价格显示
  # @param regular_price [Float] 常规价格
  # @param member_price [Float] 会员价格
  def expect_member_discount_display(regular_price, member_price)
    expect(page).to have_content("¥#{regular_price}")
    expect(page).to have_content("¥#{member_price}")
    expect(page).to have_content(I18n.t('events.show.member_discount_price', price: member_price))
  end

  # ==========================================
  # 在线事件相关辅助方法
  # ==========================================

  # 验证在线事件显示
  def expect_online_event_display
    expect(page).to have_content(I18n.t('events.show.online_event'))
  end

  # 验证会议链接可见（已报名用户）
  def expect_meeting_link_visible
    expect(page).to have_link(I18n.t('events.show.join_meeting'))
  end

  # 验证会议链接不可见（未报名用户）
  def expect_meeting_link_hidden
    expect(page).not_to have_link(I18n.t('events.show.join_meeting'))
  end

  # ==========================================
  # 错误处理和边界情况辅助方法
  # ==========================================

  # 验证404错误页面
  def expect_404_page
    expect(page).to have_content('404')
  end

  # 验证权限错误
  def expect_authorization_error
    expect(current_path).to eq(root_path)
    expect(page).to have_content('You are not authorized')
  end

  # 验证登录提示
  def expect_login_prompt
    expect(page).to have_content(I18n.t('common.auth.please_login'))
    expect(page).to have_link(I18n.t('common.auth.login_to_register'), href: sign_in_path)
  end

  # 验证表单验证错误
  # @param field_errors [Array<String>] 字段错误消息
  def expect_form_validation_errors(field_errors)
    field_errors.each do |error|
      expect(page).to have_content(error)
    end
  end
end
