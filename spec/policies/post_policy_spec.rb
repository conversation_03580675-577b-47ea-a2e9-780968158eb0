require "rails_helper"

RSpec.describe PostPolicy, type: :policy do
  # See https://actionpolicy.evilmartians.io/#/testing?id=rspec-dsl
  #
  # let(:user) { build_stubbed :user }
  # let(:record) { build_stubbed :post, draft: false }
  # let(:context) { {user: user} }

  let(:user) { create(:user) }
  let(:post) { create(:post, user: user) }
  let(:kb) { create(:knowledge_base) }

  describe_rule :index? do
    pending "add some examples to (or delete) #{__FILE__}"
  end

  describe_rule :create? do
    pending "add some examples to (or delete) #{__FILE__}"
  end

  describe_rule :manage? do
    pending "add some examples to (or delete) #{__FILE__}"
  end

  describe_rule :update? do
    context "as owner" do
      it { expect(described_class.new(post, user: user)).to be_allowed_to(:update?) }
    end

    context "as knowledge base editor" do
      let(:editor) do
        user = create(:user)
        # Create the team member first
        team_member = create(:team_member, user: user, team: kb.owner.teams.first || create(:team)) # Ensure team member belongs to a team, associate with kb owner's team if possible
        # Explicitly create the TeamResourceAccess linking the team_member,
        # the specific knowledge base 'kb', and the editor role.
        create(:team_resource_access,
               team_member: team_member,
               resource: kb, # Link to the specific knowledge base
               role: :editor)
        user # Return the user
      end

      before { post.knowledge_bases << kb } # Link post to the knowledge base

      it { expect(described_class.new(post, user: editor)).to be_allowed_to(:update?) }
    end
  end

  describe_rule :destroy? do
    context "with published post" do
      let(:post) { create(:post, :published, user: user) }
      it { expect(described_class.new(post, user: user)).not_to be_allowed_to(:destroy?) }
    end
  end
end
