import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["button"]
  static values = {
    duration: { type: Number, default: 60 }
  }

  connect() {
    this.remainingTime = this.durationValue
  }

  startCountdown(event) {
    // 阻止立即禁用按钮，等待表单提交成功后再禁用
    event.preventDefault()
    
    // 获取表单数据
    const form = event.target.closest('form')
    const formData = new FormData(form)
    
    // 提交表单
    fetch(form.action, {
      method: form.method,
      body: formData,
      headers: {
        'Accept': 'text/vnd.turbo-stream.html'
      }
    }).then(response => {
      if (response.ok) {
        this.buttonTarget.disabled = true
        this.interval = setInterval(() => {
          this.remainingTime -= 1
          this.buttonTarget.textContent = `resend (${this.remainingTime}s)`
          
          if (this.remainingTime <= 0) {
            clearInterval(this.interval)
            this.buttonTarget.disabled = false
            this.buttonTarget.textContent = "Send verification code"
            this.remainingTime = this.durationValue
          }
        }, 1000)
      }
      return response.text()
    }).then(html => {
      Turbo.renderStreamMessage(html)
    })
  }

  disconnect() {
    if (this.interval) {
      clearInterval(this.interval)
    }
  }
}
