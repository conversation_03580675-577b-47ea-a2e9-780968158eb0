<% if resource&.errors&.any? %>
  <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
    <div class="font-medium"><%= t('shared.error_messages.form_error', count: resource.errors.count) %></div>
    <ul class="mt-1.5 list-disc list-inside">
      <% resource.errors.full_messages.each do |message| %>
        <li><%= message %></li>
      <% end %>
    </ul>
  </div>
<% end %>
