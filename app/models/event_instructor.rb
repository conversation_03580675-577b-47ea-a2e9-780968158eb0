class EventInstructor < ApplicationRecord
  # 关联关系
  belongs_to :event
  belongs_to :user

  # 枚举值定义 - Rails 8 语法
  enum :role, {
    instructor: "instructor",
    co_instructor: "co_instructor",
    assistant: "assistant",
    guest_speaker: "guest_speaker"
  }

  # 验证规则
  validates :role, presence: true
  validates :user_id, uniqueness: { scope: :event_id, message: "is already an instructor for this event" }

  # 作用域
  scope :primary_instructors, -> { where(role: "instructor") }
  scope :by_role, ->(role) { where(role: role) }

  # 实例方法
  def display_name
    title.present? ? "#{user.username} - #{title}" : user.username
  end

  def primary_instructor?
    instructor?
  end
end
