class ToolUsage < ApplicationRecord
  # 建立与消息的从属关系
  belongs_to :message

  # 验证函数名必须存在
  validates :function_name, presence: true
  # 验证函数名在同一消息中必须唯一
  validates :function_name, uniqueness: { scope: :message_id, message: "This function has already been executed for this message" }

  # 将参数序列化为JSON格式存储
  serialize :arguments, coder: JSON
  # 将结果序列化为JSON格式存储
  serialize :result, coder: JSON

  # 定义工具使用状态的枚举类型
  enum :status, {
    pending: "pending",  # 待处理
    success: "success",  # 成功
    failed: "failed"     # 失败
  }, prefix: :tool, suffix: false, validate: true
end
