require "rails_helper"

RSpec.describe TeamPolicy, type: :policy do
  # See https://actionpolicy.evilmartians.io/#/testing?id=rspec-dsl
  #
  # let(:user) { build_stubbed :user }
  # let(:record) { build_stubbed :post, draft: false }
  # let(:context) { {user: user} }

  let(:user) { create(:user) }
  # let(:admin) { create(:user, :admin) } # Removed as admin concept is not implemented
  let(:team) { create(:team) }

  describe_rule :index? do
    context "when signed in" do
      it { expect(described_class.new(team, user: user)).to be_allowed_to(:index?) }
    end

    context "when not signed in" do
      it { expect(described_class.new(team, user: nil)).not_to be_allowed_to(:index?) }
    end
  end

  describe_rule :create? do
    # Removed admin test as admin concept is not implemented
    # it "allows admin" do
    #   expect(described_class.new(team, user: admin)).to be_allowed_to(:create?)
    # end

    # Renamed and updated expectation based on the new rule (any authenticated user can create)
    it "allows authenticated user" do
      expect(described_class.new(team, user: user)).to be_allowed_to(:create?)
    end
  end

  describe_rule :manage? do
    context "as team admin" do
      before { create(:team_member, :admin, team: team, user: user) }
      it { expect(described_class.new(team, user: user)).to be_allowed_to(:manage?) }
    end

    context "as regular member" do
      before { create(:team_member, team: team, user: user) }
      it { expect(described_class.new(team, user: user)).not_to be_allowed_to(:manage?) }
    end
  end
end
