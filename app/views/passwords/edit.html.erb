<section class="bg-gray-50 dark:bg-gray-900">
  <div class="flex flex-col items-center justify-center min-h-screen px-6 py-8 mx-auto">
    <%= link_to root_path, class: "flex items-center mb-6 text-2xl font-semibold text-gray-900 dark:text-white" do %>
      <%= image_tag "https://codingirls.club/cgc_logo_orange_white.png", class: "h-8 mr-2", alt: t('layouts.dashboard.navbar.logo_alt') %>
      <span class="self-center text-2xl font-semibold whitespace-nowrap dark:text-white"><%= t('cgc') %></span>
    <% end %>

    <!-- Card -->
    <div class="w-full max-w-xl p-6 space-y-8 bg-white rounded-lg shadow sm:p-8 dark:bg-gray-800">
      <% if alert = flash[:alert] %>
        <p class="py-2 px-3 bg-red-50 mb-5 text-red-500 font-medium rounded-lg inline-block" id="alert"><%= alert %></p>
      <% end %>

      <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
        <%= t('auth.passwords.reset.title') %>
      </h2>

      <%= form_with url: password_path(params[:token]), method: :put, class: "mt-8 space-y-6" do |form| %>
        <div>
          <%= form.label :password, t('auth.passwords.reset.new_password_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
          <%= form.password_field :password,
              required: true,
              autocomplete: "new-password",
              placeholder: t('auth.passwords.reset.password_placeholder'),
              maxlength: 72,
              class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" %>
        </div>

        <div>
          <%= form.label :password_confirmation, t('auth.passwords.reset.confirm_password_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
          <%= form.password_field :password_confirmation,
              required: true,
              autocomplete: "new-password",
              placeholder: t('auth.passwords.reset.password_placeholder'),
              maxlength: 72,
              class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" %>
        </div>

        <div class="flex items-start">
          <div class="flex items-center h-5">
            <%= form.check_box :terms,
                class: "w-4 h-4 border-gray-300 rounded-xs bg-gray-50 focus:ring-3 focus:ring-primary-300 dark:focus:ring-primary-600 dark:ring-offset-gray-800 dark:bg-gray-700 dark:border-gray-600",
                required: true %>
          </div>
          <div class="ml-3 text-sm">
            <%= form.label :terms, class: "font-medium text-gray-900 dark:text-white" do %>
              <%= t('auth.passwords.terms.accept') %> <%= link_to t('auth.passwords.terms.link'), terms_path, class: "font-medium text-primary-600 hover:underline dark:text-primary-500" %>
            <% end %>
          </div>
        </div>

        <%= form.submit t('auth.passwords.reset.submit'),
            class: "w-full text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>
      <% end %>
    </div>
  </div>
</section>
