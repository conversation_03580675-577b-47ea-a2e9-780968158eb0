require "administrate/base_dashboard"

class EventLocationDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    name: Field::String,
    address: Field::String,
    city: Field::String,
    province: Field::String,
    country: Field::String,
    latitude: Field::Number.with_options(decimals: 6),
    longitude: Field::Number.with_options(decimals: 6),
    events: Field::HasMany,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    name
    city
    address
    events
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    name
    address
    city
    province
    country
    latitude
    longitude
    events
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    name
    address
    city
    province
    country
    latitude
    longitude
  ].freeze

  COLLECTION_FILTERS = {
    with_coordinates: ->(resources) { resources.with_coordinates }
  }.freeze

  def display_resource(event_location)
    "#{event_location.name} (#{event_location.city})"
  end
end