require "administrate/base_dashboard"

class TeamResourceAccessDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    team_member: Field::BelongsTo,
    resource: Field::Polymorphic,
    resource_kind: Field::Select.with_options(collection: TeamResourceAccess.resource_kinds.keys),
    role: Field::Select.with_options(collection: TeamResourceAccess.roles.keys),
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    id
    team_member
    resource
    resource_kind
    role
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    team_member
    resource
    resource_kind
    role
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    team_member
    resource
    resource_kind
    role
  ].freeze

  COLLECTION_FILTERS = {}.freeze

  def display_resource(team_resource_access)
    "#{team_resource_access.team_member.user.username} - #{team_resource_access.resource_kind} - #{team_resource_access.role}"
  end
end
