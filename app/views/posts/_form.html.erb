<%= form_with(model: post, class: "space-y-6") do |f| %>
  <% if post.errors.any? %>
    <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
      <div class="font-medium"><%= t('posts.form.error', count: post.errors.count) %></div>
      <ul class="mt-1.5 list-disc list-inside">
        <% post.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div>
    <%= f.label :title, t('posts.form.title_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
    <%= f.text_field :title, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500", placeholder: t('posts.form.title_placeholder') %>
  </div>

  <div>
    <%= f.label :content, t('posts.form.content_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
     <%= f.rich_textarea :content,
          placeholder: t('posts.form.content_placeholder') %>
  </div>

  <div class="flex items-center space-x-4">
    <%= f.submit t('posts.form.save'), class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>
    <%= link_to t('posts.form.cancel'), posts_path, class: "text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700" %>
  </div>
<% end %>
