require 'rails_helper'

RSpec.describe Event, type: :model do
  describe 'FTS5 functionality' do
    let(:tech_category) { create(:event_category, name: 'Technology') }
    let(:office_location) { create(:event_location, name: 'Office') }

    before do
      # Clean up any existing events and FTS data
      Event.destroy_all
      ActiveRecord::Base.connection.execute("DELETE FROM events_fts")
    end

    it 'automatically updates FTS5 index when event is created' do
      event = create(:event, :published, :upcoming,
                    title: 'Ruby Programming Workshop',
                    description: 'Learn Ruby programming basics',
                    event_category: tech_category,
                    event_location: office_location)

      # Check if event exists in main table
      expect(Event.count).to eq(1)

      # Check if event exists in FTS table
      fts_count = ActiveRecord::Base.connection.execute("SELECT COUNT(*) as count FROM events_fts").first["count"]
      expect(fts_count).to eq(1)

      # Check if FTS search works
      search_results = Event.fts_search('Ruby').count
      expect(search_results).to eq(1)

      # Check if regular search works
      search_results = Event.fts_search('Ruby').to_a
      expect(search_results.length).to eq(1)
      expect(search_results.first.title).to eq('Ruby Programming Workshop')
    end

    it 'updates FTS5 index when event is updated', :pending do
      event = create(:event, :published, :upcoming,
                    title: 'Original Title',
                    description: 'Original description',
                    event_category: tech_category,
                    event_location: office_location)

      # Update the event
      event.update!(title: 'Updated Ruby Workshop')

      # Check if FTS search finds the updated title
      search_results = Event.fts_search('Updated').count
      expect(search_results).to eq(1)

      # Check if old title is no longer found
      search_results = Event.fts_search('Original').count
      expect(search_results).to eq(0)
    end

    it 'removes from FTS5 index when event is destroyed' do
      event = create(:event, :published, :upcoming,
                    title: 'Ruby Programming Workshop',
                    description: 'Learn Ruby programming basics',
                    event_category: tech_category,
                    event_location: office_location)

      # Verify event is in FTS index
      expect(Event.fts_search('Ruby').count).to eq(1)

      # Destroy the event
      event.destroy!

      # Check if event is removed from main table
      expect(Event.count).to eq(0)

      # Check if event is removed from FTS table
      fts_count = ActiveRecord::Base.connection.execute("SELECT COUNT(*) as count FROM events_fts").first["count"]
      expect(fts_count).to eq(0)

      # Check if FTS search no longer finds the event
      search_results = Event.fts_search('Ruby').count
      expect(search_results).to eq(0)
    end
  end
end
