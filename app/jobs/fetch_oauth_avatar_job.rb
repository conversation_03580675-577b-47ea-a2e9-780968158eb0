require "open-uri" # Required for opening URLs

class FetchOauthAvatarJob < ApplicationJob
  queue_as :default

  # Fetches an avatar from a URL and attaches it to a user if they don't have one.
  #
  # @param user_id [Integer] The ID of the user.
  # @param image_url [String] The URL of the image to fetch.
  def perform(user_id, image_url)
    user = User.find_by(id: user_id)
    unless user
      Rails.logger.warn "[FetchOauthAvatarJob] User not found with ID: #{user_id}"
      return
    end

    # Strategy B: Only fetch and attach if the user doesn't already have an avatar.
    if user.avatar.attached?
      Rails.logger.info "[FetchOauthAvatarJob] User #{user_id} already has an avatar. Skipping fetch for URL: #{image_url}"
      return
    end

    begin
      # Download the image from the URL
      # Set a reasonable timeout and handle potential redirects safely.
      downloaded_image = URI.open(image_url, read_timeout: 10, redirect: true)

      # Determine a filename (simple approach)
      filename = "oauth_avatar#{File.extname(URI.parse(image_url).path)}"
      filename = "oauth_avatar.jpg" if File.extname(filename).blank? # Default extension

      # Attach the downloaded image using Active Storage
      user.avatar.attach(io: downloaded_image, filename: filename)

      if user.avatar.attached?
        Rails.logger.info "[FetchOauthAvatarJob] Successfully attached avatar from #{image_url} for user #{user_id}"
      else
        # This might happen if validations fail after attachment, though unlikely for a simple avatar
        Rails.logger.error "[FetchOauthAvatarJob] Failed to attach avatar for user #{user_id} from URL: #{image_url}. Errors: #{user.errors.full_messages.join(", ")}"
      end

    rescue OpenURI::HTTPError => e
      Rails.logger.error "[FetchOauthAvatarJob] HTTP error fetching avatar for user #{user_id} from URL: #{image_url}. Status: #{e.io.status.first}, Message: #{e.message}"
    rescue Timeout::Error => e
      Rails.logger.error "[FetchOauthAvatarJob] Timeout error fetching avatar for user #{user_id} from URL: #{image_url}. Message: #{e.message}"
    rescue StandardError => e
      # Catch other potential errors (network issues, invalid URLs, etc.)
      Rails.logger.error "[FetchOauthAvatarJob] Error fetching or attaching avatar for user #{user_id} from URL: #{image_url}. Error: #{e.class.name}, Message: #{e.message}"
      Rails.logger.error e.backtrace.join("\n") # Log backtrace for debugging
    end
  end
end
