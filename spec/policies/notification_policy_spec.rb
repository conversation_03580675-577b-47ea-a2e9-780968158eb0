require 'rails_helper'

RSpec.describe NotificationPolicy, type: :policy do
  let(:admin_user) { create(:user, admin: true) }
  let(:owner) { create(:user) }
  let(:other_user) { create(:user) }
  let(:notification) { create(:notification, recipient: owner) }

  describe "permissions" do
    # 测试通知列表权限
    describe 'index?' do
      context 'when user is admin' do
        let(:policy) { described_class.new(notification, user: admin_user) }

        it 'allows admin to view notifications index' do
          expect(policy).to be_allowed_to(:index?)
        end
      end

      context 'when user is owner' do
        let(:policy) { described_class.new(notification, user: owner) }

        it 'allows owner to view notifications index' do
          expect(policy).to be_allowed_to(:index?)
        end
      end

      context 'when user is other authenticated user' do
        let(:policy) { described_class.new(notification, user: other_user) }

        it 'allows other authenticated user to view notifications index' do
          expect(policy).to be_allowed_to(:index?)
        end
      end

      context 'when user is guest' do
        let(:policy) { described_class.new(notification, user: nil) }

        it 'denies guest access to notifications index' do
          expect(policy).not_to be_allowed_to(:index?)
        end
      end
    end

    # 测试通知详情权限
    describe 'show?' do
      context 'when user is admin' do
        let(:policy) { described_class.new(notification, user: admin_user) }

        it 'allows admin to view any notification' do
          expect(policy).to be_allowed_to(:show?)
        end
      end

      context 'when user is owner' do
        let(:policy) { described_class.new(notification, user: owner) }

        it 'allows owner to view their own notification' do
          expect(policy).to be_allowed_to(:show?)
        end
      end

      context 'when user is other authenticated user' do
        let(:policy) { described_class.new(notification, user: other_user) }

        it 'denies other user access to someone else\'s notification' do
          expect(policy).not_to be_allowed_to(:show?)
        end
      end

      context 'when user is guest' do
        let(:policy) { described_class.new(notification, user: nil) }

        it 'denies guest access to notification' do
          expect(policy).not_to be_allowed_to(:show?)
        end
      end
    end

    # 测试通知更新权限
    describe 'update?' do
      context 'when user is admin' do
        let(:policy) { described_class.new(notification, user: admin_user) }

        it 'allows admin to update any notification' do
          expect(policy).to be_allowed_to(:update?)
        end
      end

      context 'when user is owner' do
        let(:policy) { described_class.new(notification, user: owner) }

        it 'allows owner to update their own notification' do
          expect(policy).to be_allowed_to(:update?)
        end
      end

      context 'when user is other authenticated user' do
        let(:policy) { described_class.new(notification, user: other_user) }

        it 'denies other user access to update someone else\'s notification' do
          expect(policy).not_to be_allowed_to(:update?)
        end
      end

      context 'when user is guest' do
        let(:policy) { described_class.new(notification, user: nil) }

        it 'denies guest access to update notification' do
          expect(policy).not_to be_allowed_to(:update?)
        end
      end
    end

    # 测试标记所有通知为已读权限
    describe 'mark_all_read?' do
      context 'when user is admin' do
        let(:policy) { described_class.new(notification, user: admin_user) }

        it 'allows admin to mark all notifications as read' do
          expect(policy).to be_allowed_to(:mark_all_read?)
        end
      end

      context 'when user is owner' do
        let(:policy) { described_class.new(notification, user: owner) }

        it 'allows owner to mark all notifications as read' do
          expect(policy).to be_allowed_to(:mark_all_read?)
        end
      end

      context 'when user is other authenticated user' do
        let(:policy) { described_class.new(notification, user: other_user) }

        it 'allows other authenticated user to mark all notifications as read' do
          expect(policy).to be_allowed_to(:mark_all_read?)
        end
      end

      context 'when user is guest' do
        let(:policy) { described_class.new(notification, user: nil) }

        it 'denies guest access to mark all notifications as read' do
          expect(policy).not_to be_allowed_to(:mark_all_read?)
        end
      end
    end
  end

  # 测试数据范围权限
  describe "Scope" do
    describe 'apply_scope' do
      let(:owner_ntf) { create(:notification, recipient: owner) }
      let(:other_ntf) { create(:notification, recipient: other_user) }
      let(:admin_ntf) { create(:notification, recipient: admin_user) }

      before do
        # 确保所有通知都已创建
        owner_ntf
        other_ntf
        admin_ntf
      end

      context 'when user is admin' do
        it 'returns all notifications' do
          scope = described_class.new(Noticed::Notification, user: admin_user)
                                 .apply_scope(Noticed::Notification.all, type: :active_record_relation)
          expect(scope).to match_array([owner_ntf, other_ntf, admin_ntf])
        end
      end

      context 'when user is owner' do
        it 'returns only owner notifications' do
          scope = described_class.new(Noticed::Notification, user: owner)
                                 .apply_scope(Noticed::Notification.all, type: :active_record_relation)
          expect(scope).to match_array([owner_ntf])
        end
      end

      context 'when user is unauthenticated' do
        it 'returns empty array' do
          scope = described_class.new(Noticed::Notification, user: nil)
                                 .apply_scope(Noticed::Notification.all, type: :active_record_relation)
          expect(scope).to match_array([])
        end
      end
    end
  end
end
