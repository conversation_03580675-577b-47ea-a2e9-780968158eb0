name: Workflow - Deployment (production)

on:
  release:
    types: [published]

concurrency:
  group: deployment-production
  cancel-in-progress: false

jobs:
  deploy:
    uses: ./.github/workflows/job-deploy-kamal.yml
    permissions:
      contents: read
      packages: write
      id-token: write
    with:
      kamal-destination: production
      kamal-registry-server: ccr.ccs.tencentyun.com # 新增：传入 TCR server 地址
    secrets:
      # Pass TCR secrets using the names expected by job-deploy-kamal.yml
      KAMAL_REGISTRY_USERNAME: ${{ secrets.KAMAL_TCR_USERNAME }}
      KAMAL_REGISTRY_PASSWORD: ${{ secrets.KAMAL_TCR_PASSWORD }}
      SSH_PRIVATE_KEY: ${{ secrets.PROD_SSH_PRIVATE_KEY }}
      RAILS_MASTER_KEY: ${{ secrets.RAILS_MASTER_KEY }}
