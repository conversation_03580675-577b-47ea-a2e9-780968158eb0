require 'rails_helper'

RSpec.describe 'Navigation Bar', type: :feature do
  describe 'Public Layout (unauthenticated)' do
    before { visit root_path }

    it 'displays public navbar elements' do
      within('nav') do
        expect(page).to have_css('img[alt="CGC Logo"]')
        expect(page).to have_link('Sign In')
        expect(page).to have_link('Sign Up')
      end
    end

    it 'displays the logo with correct company name' do
      within('nav') do
        expect(page).to have_css('img[alt="CGC Logo"]')
        expect(page).to have_content('Coding Girls Club')
      end
    end

    it 'redirects to homepage when clicking logo' do
      visit about_path
      first('nav').first('a').click
      expect(current_path).to eq(root_path)
    end

    context 'with primary navigation links' do
      it 'redirects to homepage when clicking home link' do
        within('nav') do
          click_link 'Home'
        end
        expect(current_path).to eq(root_path)
      end

      it 'redirects to about page when clicking about link' do
        within('nav') do
          click_link 'About'
        end
        expect(current_path).to eq(about_path)
      end
    end

    context 'with authentication links' do
      it 'displays both sign in and sign up links' do
        within('nav') do
          expect(page).to have_link('Sign In')
          expect(page).to have_link('Sign Up')
        end
      end

      it 'redirects to sign in page when clicking sign in link' do
        within('nav') do
          click_link 'Sign In'
        end
        expect(current_path).to eq(sign_in_path)
      end

      it 'redirects to sign up page when clicking sign up link' do
        within('nav') do
          click_link 'Sign Up'
        end
        expect(current_path).to eq(sign_up_path)
      end
    end

    context 'with responsive design', js: true do
      it 'shows mobile menu button on small screens' do
        page.current_window.resize_to(375, 812)
        expect(page).to have_css('button[data-collapse-toggle="mobile-menu-2"]', visible: true)
      end
    end
  end

  describe 'Dashboard Layout (authenticated)' do
    let(:user) { create(:user) }

    before do
      sign_in(user)
      visit root_path
    end

    it 'displays dashboard navbar elements' do
      within('nav') do
        expect(page).to have_css('#user-menu-button-2')
        expect(page).to have_button('Sign Out')
      end
    end

    context 'with authentication status' do
      it 'shows sign out button' do
        expect(page).to have_button('Sign Out')
      end

      it 'hides sign in and sign up links' do
        expect(page).not_to have_link('Sign In')
        expect(page).not_to have_link('Sign Up')
      end

      it 'allows user to sign out successfully' do
        click_button 'Sign Out'
        expect(page).to have_link('Sign In')
        expect(page).to have_current_path(root_path)
      end
    end

    context 'with user profile navigation' do
      it 'displays user avatar or placeholder in the navigation' do
        within('nav') do
          expect(page).to have_css('#user-menu-button-2')
        end
      end

      it 'shows user menu when clicking avatar' do
        find('#user-menu-button-2').click
        expect(page).to have_css('#dropdown-2', visible: true)
      end

      it 'navigates to settings page from user menu' do
        find('#user-menu-button-2').click
        within('#dropdown-2') do
          click_link I18n.t('layouts.dashboard.navbar.user_menu.settings')
        end
        expect(page).to have_current_path(settings_users_path)
        expect(page).to have_content(I18n.t('users.settings.title'))
      end

      it 'shows username in the user menu' do
        find('#user-menu-button-2').click
        within('#dropdown-2') do
          expect(page).to have_content(user.username)
        end
      end
    end

    context 'with notification features' do
      it 'displays notification bell icon' do
        within('nav') do
          expect(page).to have_css('[data-dropdown-toggle="notification-dropdown"]')
        end
      end
    end
  end

  context 'with dark mode support', js: true do
    before do
      visit root_path
      # Directly set dark mode via JavaScript
      page.execute_script("document.documentElement.classList.add('dark'); localStorage.setItem('color-theme', 'dark');")
    end

    it 'applies dark theme correctly' do
      # Check that dark mode is applied
      expect(page).to have_css('html.dark', wait: 5)
      # Verify dark styling is applied to the body
      expect(page).to have_css('body.dark\\:bg-gray-900', wait: 5)
    end

    it 'allows toggling between light and dark mode via JavaScript' do
      # Verify dark mode is initially applied
      expect(page).to have_css('html.dark', wait: 5)
      
      # Toggle theme via JavaScript (simulating the theme controller's toggle action)
      page.execute_script("document.documentElement.classList.remove('dark'); localStorage.setItem('color-theme', 'light');")
      
      # Verify dark mode is removed
      expect(page).not_to have_css('html.dark')
    end
  end
end
