require "administrate/base_dashboard"

class KnowledgeBasePostDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    knowledge_base: Field::BelongsTo,
    post: Field::BelongsTo,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    id
    knowledge_base
    post
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    knowledge_base
    post
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    knowledge_base
    post
  ].freeze

  COLLECTION_FILTERS = {}.freeze

  def display_resource(knowledge_base_post)
    "#{knowledge_base_post.knowledge_base.name} - #{knowledge_base_post.post.title}"
  end
end
