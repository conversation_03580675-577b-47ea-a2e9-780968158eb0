# Events Phase 3: 地图集成详细实施指南

## 概述

Phase 3 专注于为Events功能添加地图视图和地理位置相关功能，提供用户友好的地理位置展示和筛选体验。

## 技术架构

### 技术选型

**推荐方案: Leaflet + OpenStreetMap**
- **优势**: 轻量级(~40KB)、开源免费、无API限制、移动端友好
- **适用场景**: 中小型应用、预算有限、快速开发
- **集成复杂度**: 低

**备选方案: Mapbox GL JS**
- **优势**: 现代化设计、性能优秀、自定义样式丰富
- **适用场景**: 大型应用、需要高级地图功能、有预算支持
- **集成复杂度**: 中等

### 系统架构图

```mermaid
graph TB
    A[Events Index Page] --> B[View Switcher Controller]
    B --> C[Grid View]
    B --> D[Map View]
    
    D --> E[Map Controller]
    E --> F[Leaflet Map Instance]
    E --> G[Event Markers]
    E --> H[Marker Clustering]
    
    I[Events Controller] --> J[map_data Action]
    J --> K[EventLocation Model]
    K --> L[Geographic Data]
    
    M[Search & Filter] --> N[Geographic Filtering]
    N --> O[Distance Calculation]
    N --> P[Boundary Filtering]
```

## 实施步骤

### Step 1: 依赖安装和配置

#### 1.1 添加Leaflet依赖

**方式一: 使用importmap (推荐)**
```ruby
# config/importmap.rb
pin "leaflet", to: "https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
pin "leaflet-css", to: "https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
```

**方式二: 使用yarn/npm**
```bash
yarn add leaflet
# 或
npm install leaflet
```

#### 1.2 样式文件配置

```scss
// app/assets/stylesheets/application.scss
@import url('https://unpkg.com/leaflet@1.9.4/dist/leaflet.css');

// 自定义地图样式
.leaflet-container {
  @apply rounded-lg border border-gray-200 dark:border-gray-700;
}

.leaflet-popup-content-wrapper {
  @apply rounded-lg shadow-lg;
}

.leaflet-popup-content {
  @apply p-0 m-0;
}
```

### Step 2: 后端API开发

#### 2.1 控制器方法实现

```ruby
# app/controllers/events_controller.rb

# 地图数据API
def map_data
  authorize! Event, to: :index?
  
  events = build_optimized_events_query
  
  # 只返回有地理位置的事件
  map_events = events.joins(:event_location)
                    .where.not(event_locations: { latitude: nil, longitude: nil })
                    .includes(:event_category, :event_location, :user)
  
  render json: {
    events: map_events.map do |event|
      {
        id: event.id,
        title: event.title,
        description: truncate(event.description, length: 100),
        latitude: event.event_location.latitude,
        longitude: event.event_location.longitude,
        location_name: event.event_location.name,
        address: event.event_location.address,
        start_time: event.start_time.iso8601,
        end_time: event.end_time.iso8601,
        category: {
          name: event.event_category&.name,
          color: event.event_category&.color || '#3B82F6'
        },
        price: event.price,
        member_price: event.member_price,
        max_participants: event.max_participants,
        registered_count: event.event_registrations.confirmed.count,
        is_online: event.is_online,
        difficulty_level: event.difficulty_level,
        url: event_path(event),
        can_register: event.can_register?(current_user)
      }
    end,
    meta: {
      total_count: map_events.count,
      bounds: calculate_map_bounds(map_events)
    }
  }
end

# 附近事件搜索
def nearby_events
  authorize! Event, to: :index?
  
  lat = params[:latitude].to_f
  lng = params[:longitude].to_f
  radius = params[:radius]&.to_f || 10 # 默认10公里
  
  events = Event.joins(:event_location)
                .merge(EventLocation.within_radius(lat, lng, radius))
                .published.upcoming.unarchived
                .includes(:event_category, :event_location, :user)
  
  render json: {
    events: events.map { |event| serialize_event_for_map(event) },
    center: [lat, lng],
    radius: radius
  }
end

private

def calculate_map_bounds(events)
  return nil if events.empty?
  
  lats = events.map { |e| e.event_location.latitude }
  lngs = events.map { |e| e.event_location.longitude }
  
  {
    north: lats.max,
    south: lats.min,
    east: lngs.max,
    west: lngs.min
  }
end

def serialize_event_for_map(event)
  {
    id: event.id,
    title: event.title,
    latitude: event.event_location.latitude,
    longitude: event.event_location.longitude,
    category_color: event.event_category&.color || '#3B82F6',
    url: event_path(event)
  }
end
```

#### 2.2 模型增强

```ruby
# app/models/event_location.rb
class EventLocation < ApplicationRecord
  has_many :events, dependent: :nullify
  
  # 地理位置验证
  validates :latitude, :longitude, presence: true, if: :coordinates_required?
  validates :latitude, numericality: { 
    greater_than_or_equal_to: -90, 
    less_than_or_equal_to: 90 
  }, allow_nil: true
  validates :longitude, numericality: { 
    greater_than_or_equal_to: -180, 
    less_than_or_equal_to: 180 
  }, allow_nil: true
  
  # 地理位置搜索作用域
  scope :within_radius, ->(lat, lng, radius_km) {
    where(
      "6371 * acos(
        cos(radians(?)) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(?)) + 
        sin(radians(?)) * sin(radians(latitude))
      ) <= ?",
      lat, lng, lat, radius_km
    )
  }
  
  scope :within_bounds, ->(north, south, east, west) {
    where(
      latitude: south..north,
      longitude: west..east
    )
  }
  
  # 计算到指定点的距离（公里）
  def distance_to(lat, lng)
    return nil unless latitude && longitude
    
    rad_per_deg = Math::PI / 180
    rkm = 6371
    rm = rkm * 1000
    
    dlat_rad = (lat - latitude) * rad_per_deg
    dlon_rad = (lng - longitude) * rad_per_deg
    
    lat1_rad = latitude * rad_per_deg
    lat2_rad = lat * rad_per_deg
    
    a = Math.sin(dlat_rad/2)**2 + Math.cos(lat1_rad) * Math.cos(lat2_rad) * Math.sin(dlon_rad/2)**2
    c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    
    rkm * c
  end
  
  # 地理编码（可选，需要集成地理编码服务）
  def geocode_address
    return if address.blank?
    
    # 这里可以集成 Geocoder gem 或其他地理编码服务
    # geocode_result = Geocoder.search(full_address)
    # if geocode_result.any?
    #   self.latitude = geocode_result.first.latitude
    #   self.longitude = geocode_result.first.longitude
    # end
  end
  
  def full_address
    [address, city, province, country].compact.join(', ')
  end
  
  private
  
  def coordinates_required?
    # 如果地址存在但坐标为空，则需要坐标
    address.present? && (latitude.blank? || longitude.blank?)
  end
end
```

### Step 3: 前端组件开发

#### 3.1 地图控制器

```javascript
// app/javascript/controllers/map_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["container", "loading", "error"]
  static values = { 
    events: Array,
    center: Array,
    zoom: Number,
    apiUrl: String
  }
  
  static classes = ["loading", "error"]

  async connect() {
    this.showLoading()
    
    try {
      // 动态导入 Leaflet 以实现懒加载
      const L = await import('leaflet')
      this.L = L.default || L
      
      await this.initializeMap()
      await this.loadEvents()
      this.hideLoading()
    } catch (error) {
      console.error('Map initialization failed:', error)
      this.showError('地图加载失败，请刷新页面重试')
    }
  }

  disconnect() {
    if (this.map) {
      this.map.remove()
      this.map = null
    }
  }

  async initializeMap() {
    const center = this.centerValue || [43.6532, -79.3832] // 默认多伦多
    const zoom = this.zoomValue || 10
    
    this.map = this.L.map(this.containerTarget, {
      center: center,
      zoom: zoom,
      zoomControl: true,
      attributionControl: true
    })
    
    // 添加地图瓦片层
    this.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19
    }).addTo(this.map)
    
    // 初始化标记管理
    this.markers = this.L.layerGroup().addTo(this.map)
    
    // 如果有聚类需求，可以添加聚类插件
    if (this.shouldUseCluster()) {
      await this.initializeCluster()
    }
    
    // 添加地图事件监听
    this.map.on('moveend', () => this.onMapMove())
    this.map.on('zoomend', () => this.onMapZoom())
  }

  async loadEvents() {
    if (this.eventsValue && this.eventsValue.length > 0) {
      this.displayEvents(this.eventsValue)
    } else if (this.apiUrlValue) {
      await this.fetchEventsFromAPI()
    }
  }

  async fetchEventsFromAPI() {
    try {
      const response = await fetch(this.apiUrlValue, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (!response.ok) throw new Error('Failed to fetch events')
      
      const data = await response.json()
      this.displayEvents(data.events)
      
      // 如果有边界数据，调整地图视图
      if (data.meta && data.meta.bounds) {
        this.fitMapToBounds(data.meta.bounds)
      }
    } catch (error) {
      console.error('Failed to load events:', error)
      this.showError('无法加载活动数据')
    }
  }

  displayEvents(events) {
    this.clearMarkers()
    
    events.forEach(event => {
      this.addEventMarker(event)
    })
  }

  addEventMarker(event) {
    if (!event.latitude || !event.longitude) return
    
    const marker = this.L.marker([event.latitude, event.longitude], {
      icon: this.createCustomIcon(event)
    })
    
    marker.bindPopup(this.createPopupContent(event), {
      maxWidth: 300,
      className: 'event-popup'
    })
    
    // 添加点击事件
    marker.on('click', () => this.onMarkerClick(event))
    
    if (this.markerCluster) {
      this.markerCluster.addLayer(marker)
    } else {
      this.markers.addLayer(marker)
    }
  }

  createCustomIcon(event) {
    const color = event.category?.color || '#3B82F6'
    
    return this.L.divIcon({
      className: 'custom-event-marker',
      html: `
        <div class="w-8 h-8 rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white text-xs font-bold"
             style="background-color: ${color}">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
          </svg>
        </div>
      `,
      iconSize: [32, 32],
      iconAnchor: [16, 16],
      popupAnchor: [0, -16]
    })
  }

  createPopupContent(event) {
    const startTime = new Date(event.start_time).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
    
    const priceText = event.price > 0 ? `¥${event.price}` : '免费'
    const spotsText = event.max_participants ? 
      `${event.registered_count}/${event.max_participants}` : 
      `${event.registered_count} 人已报名`
    
    return `
      <div class="p-4 min-w-[280px]">
        <div class="flex items-start justify-between mb-3">
          <h3 class="font-semibold text-lg text-gray-900 leading-tight pr-2">${event.title}</h3>
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 whitespace-nowrap">
            ${event.category?.name || '活动'}
          </span>
        </div>
        
        <div class="space-y-2 mb-4">
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
            </svg>
            ${startTime}
          </div>
          
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
            </svg>
            ${event.location_name}
          </div>
          
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
            </svg>
            ${spotsText}
          </div>
        </div>
        
        <div class="flex items-center justify-between">
          <span class="text-lg font-semibold text-primary-600">${priceText}</span>
          <div class="flex space-x-2">
            <a href="${event.url}" 
               class="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
              查看详情
            </a>
            ${event.can_register ? `
              <button onclick="window.location.href='${event.url}#register'"
                      class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors">
                立即报名
              </button>
            ` : ''}
          </div>
        </div>
      </div>
    `
  }

  // 工具方法
  clearMarkers() {
    if (this.markerCluster) {
      this.markerCluster.clearLayers()
    } else {
      this.markers.clearLayers()
    }
  }

  fitMapToBounds(bounds) {
    if (!bounds) return
    
    const leafletBounds = this.L.latLngBounds([
      [bounds.south, bounds.west],
      [bounds.north, bounds.east]
    ])
    
    this.map.fitBounds(leafletBounds, { padding: [20, 20] })
  }

  shouldUseCluster() {
    return this.eventsValue && this.eventsValue.length > 20
  }

  async initializeCluster() {
    // 如果需要聚类功能，可以在这里初始化
    // 需要额外安装 leaflet.markercluster 插件
  }

  // 事件处理
  onMarkerClick(event) {
    // 可以在这里添加自定义的标记点击处理逻辑
    console.log('Marker clicked:', event)
  }

  onMapMove() {
    // 地图移动时的处理逻辑
    // 可以用于实现动态加载视口内的事件
  }

  onMapZoom() {
    // 地图缩放时的处理逻辑
  }

  // UI 状态管理
  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
  }

  showError(message) {
    this.hideLoading()
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = message
      this.errorTarget.classList.remove('hidden')
    }
  }
}
```

### Step 4: 视图切换控制器

```javascript
// app/javascript/controllers/view_switcher_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["gridContainer", "mapContainer", "gridButton", "mapButton", "mapFilters"]
  static values = { defaultView: String }

  connect() {
    // 从 localStorage 恢复用户偏好，或使用默认视图
    const savedView = localStorage.getItem('events-view-preference') || this.defaultViewValue || 'grid'
    
    if (savedView === 'map') {
      this.showMap()
    } else {
      this.showGrid()
    }
  }

  showGrid() {
    // 显示网格视图
    this.gridContainerTarget.classList.remove('hidden')
    this.mapContainerTarget.classList.add('hidden')
    
    // 更新按钮状态
    this.updateButtonStates('grid')
    
    // 隐藏地图专用筛选器
    if (this.hasMapFiltersTarget) {
      this.mapFiltersTarget.classList.add('hidden')
    }
    
    // 保存用户偏好
    localStorage.setItem('events-view-preference', 'grid')
    
    // 触发自定义事件
    this.dispatch('viewChanged', { detail: { view: 'grid' } })
  }

  showMap() {
    // 显示地图视图
    this.gridContainerTarget.classList.add('hidden')
    this.mapContainerTarget.classList.remove('hidden')
    
    // 更新按钮状态
    this.updateButtonStates('map')
    
    // 显示地图专用筛选器
    if (this.hasMapFiltersTarget) {
      this.mapFiltersTarget.classList.remove('hidden')
    }
    
    // 保存用户偏好
    localStorage.setItem('events-view-preference', 'map')
    
    // 触发地图初始化（如果还未初始化）
    this.dispatch('viewChanged', { detail: { view: 'map' } })
    
    // 延迟触发地图重绘，确保容器已显示
    setTimeout(() => {
      const mapController = this.application.getControllerForElementAndIdentifier(
        this.mapContainerTarget.querySelector('[data-controller*="map"]'), 
        'map'
      )
      if (mapController && mapController.map) {
        mapController.map.invalidateSize()
      }
    }, 100)
  }

  updateButtonStates(activeView) {
    const gridClasses = 'flex items-center px-4 py-2 rounded-lg transition-colors'
    const mapClasses = 'flex items-center px-4 py-2 rounded-lg transition-colors'
    
    if (activeView === 'grid') {
      this.gridButtonTarget.className = `${gridClasses} bg-primary-600 text-white`
      this.mapButtonTarget.className = `${mapClasses} bg-gray-200 text-gray-700 hover:bg-gray-300`
    } else {
      this.gridButtonTarget.className = `${gridClasses} bg-gray-200 text-gray-700 hover:bg-gray-300`
      this.mapButtonTarget.className = `${mapClasses} bg-primary-600 text-white`
    }
  }

  // 响应外部视图切换请求
  switchToView(event) {
    const view = event.detail.view
    if (view === 'map') {
      this.showMap()
    } else {
      this.showGrid()
    }
  }
}
```

### Step 5: 路由配置

```ruby
# config/routes.rb
resources :events do
  collection do
    get :map_data          # 地图数据API
    get :nearby_events     # 附近事件搜索
    get :search_locations  # 位置搜索
  end
end
```

### Step 6: 视图集成

#### 6.1 更新events/index.html.erb

```erb
<!-- 在搜索筛选栏后添加视图切换控件 -->
<div class="col-span-full mb-6">
  <div class="flex items-center justify-between"
       data-controller="view-switcher"
       data-view-switcher-default-view-value="grid">

    <!-- 视图切换按钮 -->
    <div class="flex items-center space-x-2">
      <button data-action="click->view-switcher#showGrid"
              data-view-switcher-target="gridButton"
              class="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg transition-colors">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
        </svg>
        网格视图
      </button>

      <button data-action="click->view-switcher#showMap"
              data-view-switcher-target="mapButton"
              class="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg transition-colors hover:bg-gray-300">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z" clip-rule="evenodd"/>
        </svg>
        地图视图
      </button>
    </div>

    <!-- 地图专用筛选器 -->
    <div data-view-switcher-target="mapFilters" class="hidden">
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <label class="text-sm font-medium text-gray-700 dark:text-gray-300">搜索范围:</label>
          <select data-action="change->map#updateRadius"
                  class="text-sm border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
            <option value="5">5公里</option>
            <option value="10" selected>10公里</option>
            <option value="25">25公里</option>
            <option value="50">50公里</option>
          </select>
        </div>

        <button data-action="click->map#locateUser"
                class="text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-md hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
          <svg class="w-4 h-4 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
          </svg>
          定位我的位置
        </button>
      </div>
    </div>
  </div>
</div>

<!-- 网格视图容器 (现有内容) -->
<div class="col-span-full" data-view-switcher-target="gridContainer">
  <% if @events.any? %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
      <% @events.each do |event| %>
        <%= render 'event_card', event: event %>
      <% end %>
    </div>
    <!-- 分页 -->
    <div class="flex justify-center">
      <%= paginate @events if respond_to?(:paginate) %>
    </div>
  <% else %>
    <!-- 空状态 -->
    <%= render 'empty_state' %>
  <% end %>
</div>

<!-- 地图视图容器 -->
<div class="col-span-full" data-view-switcher-target="mapContainer" class="hidden">
  <div data-controller="map"
       data-map-api-url-value="<%= map_data_events_path %>"
       data-map-center-value="[43.6532, -79.3832]"
       data-map-zoom-value="10"
       class="relative bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700">

    <!-- 地图容器 -->
    <div data-map-target="container"
         class="h-96 lg:h-[600px] rounded-xl"></div>

    <!-- 地图加载状态 -->
    <div data-map-target="loading"
         class="absolute inset-0 bg-gray-100 dark:bg-gray-800 rounded-xl flex items-center justify-center">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
        <p class="text-sm text-gray-600 dark:text-gray-400">加载地图中...</p>
      </div>
    </div>

    <!-- 地图错误状态 -->
    <div data-map-target="error"
         class="hidden absolute inset-0 bg-gray-100 dark:bg-gray-800 rounded-xl flex items-center justify-center">
      <div class="text-center">
        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <p class="text-sm text-gray-600 dark:text-gray-400">地图加载失败</p>
        <button onclick="location.reload()"
                class="mt-2 text-sm text-primary-600 hover:text-primary-700">
          点击重试
        </button>
      </div>
    </div>
  </div>
</div>
```

### Step 7: 地理筛选功能增强

#### 7.1 位置搜索控制器

```javascript
// app/javascript/controllers/location_search_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "suggestions", "selectedLocation"]
  static values = {
    searchUrl: String,
    debounceDelay: { type: Number, default: 300 }
  }

  connect() {
    this.timeout = null
    this.selectedLocation = null
  }

  search() {
    clearTimeout(this.timeout)
    this.timeout = setTimeout(() => {
      this.performSearch()
    }, this.debounceDelayValue)
  }

  async performSearch() {
    const query = this.inputTarget.value.trim()
    if (query.length < 2) {
      this.hideSuggestions()
      return
    }

    try {
      const response = await fetch(`${this.searchUrlValue}?q=${encodeURIComponent(query)}`, {
        headers: { "Accept": "application/json" }
      })

      if (!response.ok) throw new Error('Search failed')

      const data = await response.json()
      this.displaySuggestions(data.locations)
    } catch (error) {
      console.error("Location search failed:", error)
      this.hideSuggestions()
    }
  }

  displaySuggestions(locations) {
    if (!locations || locations.length === 0) {
      this.hideSuggestions()
      return
    }

    const html = locations.map(location => `
      <div class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
           data-action="click->location-search#selectLocation"
           data-location='${JSON.stringify(location)}'>
        <div class="font-medium text-gray-900 dark:text-white">${location.name}</div>
        <div class="text-sm text-gray-600 dark:text-gray-400">${location.full_address}</div>
      </div>
    `).join('')

    this.suggestionsTarget.innerHTML = html
    this.showSuggestions()
  }

  selectLocation(event) {
    const locationData = JSON.parse(event.currentTarget.dataset.location)
    this.selectedLocation = locationData

    this.inputTarget.value = locationData.name
    this.hideSuggestions()

    // 触发位置选择事件
    this.dispatch('locationSelected', {
      detail: {
        location: locationData,
        latitude: locationData.latitude,
        longitude: locationData.longitude
      }
    })
  }

  showSuggestions() {
    this.suggestionsTarget.classList.remove('hidden')
  }

  hideSuggestions() {
    this.suggestionsTarget.classList.add('hidden')
  }

  clearSelection() {
    this.selectedLocation = null
    this.inputTarget.value = ''
    this.hideSuggestions()

    this.dispatch('locationCleared')
  }
}
```

#### 7.2 用户位置获取功能

```javascript
// 在 map_controller.js 中添加以下方法

// 获取用户当前位置
async locateUser() {
  if (!navigator.geolocation) {
    alert('您的浏览器不支持地理定位功能')
    return
  }

  try {
    this.showLocationLoading()

    const position = await this.getCurrentPosition()
    const { latitude, longitude } = position.coords

    // 在地图上显示用户位置
    this.showUserLocation(latitude, longitude)

    // 搜索附近的事件
    await this.loadNearbyEvents(latitude, longitude)

    this.hideLocationLoading()
  } catch (error) {
    this.hideLocationLoading()
    console.error('Location error:', error)

    switch(error.code) {
      case error.PERMISSION_DENIED:
        alert('请允许访问您的位置信息以查看附近的活动')
        break
      case error.POSITION_UNAVAILABLE:
        alert('无法获取您的位置信息')
        break
      case error.TIMEOUT:
        alert('获取位置信息超时，请重试')
        break
      default:
        alert('获取位置信息时发生错误')
        break
    }
  }
}

getCurrentPosition() {
  return new Promise((resolve, reject) => {
    navigator.geolocation.getCurrentPosition(resolve, reject, {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 300000 // 5分钟缓存
    })
  })
}

showUserLocation(lat, lng) {
  // 移除之前的用户位置标记
  if (this.userLocationMarker) {
    this.map.removeLayer(this.userLocationMarker)
  }

  // 创建用户位置标记
  this.userLocationMarker = this.L.marker([lat, lng], {
    icon: this.createUserLocationIcon()
  }).addTo(this.map)

  this.userLocationMarker.bindPopup('您的位置').openPopup()

  // 将地图中心移动到用户位置
  this.map.setView([lat, lng], 13)
}

createUserLocationIcon() {
  return this.L.divIcon({
    className: 'user-location-marker',
    html: `
      <div class="w-6 h-6 bg-blue-500 border-2 border-white rounded-full shadow-lg animate-pulse">
        <div class="w-2 h-2 bg-white rounded-full mx-auto mt-1"></div>
      </div>
    `,
    iconSize: [24, 24],
    iconAnchor: [12, 12]
  })
}

async loadNearbyEvents(lat, lng, radius = 10) {
  try {
    const response = await fetch(`/events/nearby_events?latitude=${lat}&longitude=${lng}&radius=${radius}`, {
      headers: { 'Accept': 'application/json' }
    })

    if (!response.ok) throw new Error('Failed to load nearby events')

    const data = await response.json()
    this.displayEvents(data.events)

    // 显示搜索范围圆圈
    this.showSearchRadius(lat, lng, radius)
  } catch (error) {
    console.error('Failed to load nearby events:', error)
  }
}

showSearchRadius(lat, lng, radius) {
  // 移除之前的搜索范围圆圈
  if (this.searchRadiusCircle) {
    this.map.removeLayer(this.searchRadiusCircle)
  }

  this.searchRadiusCircle = this.L.circle([lat, lng], {
    radius: radius * 1000, // 转换为米
    fillColor: '#3B82F6',
    fillOpacity: 0.1,
    color: '#3B82F6',
    weight: 2,
    opacity: 0.5
  }).addTo(this.map)
}

updateRadius(event) {
  const newRadius = parseInt(event.target.value)

  if (this.userLocationMarker && this.userLocationMarker.getLatLng()) {
    const { lat, lng } = this.userLocationMarker.getLatLng()
    this.loadNearbyEvents(lat, lng, newRadius)
  }
}

showLocationLoading() {
  // 显示定位加载状态
  const button = document.querySelector('[data-action*="locateUser"]')
  if (button) {
    button.innerHTML = `
      <svg class="w-4 h-4 mr-1 inline animate-spin" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      定位中...
    `
    button.disabled = true
  }
}

hideLocationLoading() {
  const button = document.querySelector('[data-action*="locateUser"]')
  if (button) {
    button.innerHTML = `
      <svg class="w-4 h-4 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
      </svg>
      定位我的位置
    `
    button.disabled = false
  }
}
```

## 性能优化和最佳实践

### 1. 懒加载策略

```javascript
// 只在需要时加载地图库
async loadMapLibrary() {
  if (this.mapLibraryLoaded) return this.L

  try {
    const L = await import('leaflet')
    this.L = L.default || L
    this.mapLibraryLoaded = true
    return this.L
  } catch (error) {
    console.error('Failed to load map library:', error)
    throw error
  }
}
```

### 2. 事件聚类优化

```javascript
// 当事件数量较多时使用聚类
shouldUseCluster() {
  return this.eventsValue && this.eventsValue.length > 50
}

async initializeCluster() {
  // 动态加载聚类插件
  const { MarkerClusterGroup } = await import('leaflet.markercluster')

  this.markerCluster = new MarkerClusterGroup({
    maxClusterRadius: 50,
    spiderfyOnMaxZoom: true,
    showCoverageOnHover: false,
    zoomToBoundsOnClick: true
  })

  this.map.addLayer(this.markerCluster)
}
```

### 3. 缓存策略

```ruby
# app/controllers/events_controller.rb
def map_data
  cache_key = [
    'events_map_data',
    params[:category_id],
    params[:city],
    params[:search],
    Event.maximum(:updated_at)&.to_i
  ].compact.join('/')

  Rails.cache.fetch(cache_key, expires_in: 15.minutes) do
    # ... 地图数据生成逻辑
  end
end
```

## 测试策略

### 1. 单元测试

```ruby
# spec/controllers/events_controller_spec.rb
describe 'GET #map_data' do
  let!(:event_with_location) { create(:event, :with_location) }
  let!(:event_without_location) { create(:event) }

  it 'returns only events with locations' do
    get :map_data, format: :json

    expect(response).to have_http_status(:success)
    data = JSON.parse(response.body)
    expect(data['events'].size).to eq(1)
    expect(data['events'].first['id']).to eq(event_with_location.id)
  end
end

# spec/models/event_location_spec.rb
describe EventLocation do
  describe '.within_radius' do
    let!(:nearby_location) { create(:event_location, latitude: 43.6532, longitude: -79.3832) }
    let!(:far_location) { create(:event_location, latitude: 45.5017, longitude: -73.5673) }

    it 'returns locations within specified radius' do
      results = EventLocation.within_radius(43.6532, -79.3832, 10)
      expect(results).to include(nearby_location)
      expect(results).not_to include(far_location)
    end
  end
end
```

### 2. 系统测试

```ruby
# spec/system/events_map_spec.rb
RSpec.describe 'Events Map', type: :system, js: true do
  let!(:event) { create(:event, :with_location) }

  it 'switches between grid and map views' do
    visit events_path

    # 默认显示网格视图
    expect(page).to have_css('[data-view-switcher-target="gridContainer"]:not(.hidden)')
    expect(page).to have_css('[data-view-switcher-target="mapContainer"].hidden')

    # 切换到地图视图
    click_button '地图视图'

    expect(page).to have_css('[data-view-switcher-target="gridContainer"].hidden')
    expect(page).to have_css('[data-view-switcher-target="mapContainer"]:not(.hidden)')

    # 等待地图加载
    expect(page).to have_css('[data-map-target="container"]')
  end
end
```

## 部署注意事项

### 1. 环境变量配置

```ruby
# config/application.rb
config.x.map = {
  default_center: ENV.fetch('MAP_DEFAULT_CENTER', '[43.6532, -79.3832]'),
  default_zoom: ENV.fetch('MAP_DEFAULT_ZOOM', '10').to_i,
  tile_server: ENV.fetch('MAP_TILE_SERVER', 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png')
}
```

### 2. CDN配置

```ruby
# config/environments/production.rb
# 如果使用CDN加载地图库
config.x.map.cdn_url = ENV.fetch('MAP_CDN_URL', 'https://unpkg.com/leaflet@1.9.4/dist/')
```

### 3. 安全考虑

```ruby
# app/controllers/events_controller.rb
def map_data
  # 限制返回的数据量
  events = build_optimized_events_query.limit(500)

  # 不返回敏感信息
  render json: {
    events: events.map { |event| sanitize_event_data(event) }
  }
end

private

def sanitize_event_data(event)
  {
    id: event.id,
    title: event.title,
    latitude: event.event_location.latitude.round(4), # 限制精度
    longitude: event.event_location.longitude.round(4),
    # ... 其他安全的字段
  }
end
```

## 总结

Phase 3的地图集成为Events功能提供了强大的地理位置可视化能力。通过采用Leaflet + OpenStreetMap的技术方案，我们实现了：

1. **轻量级地图集成** - 无API限制，快速加载
2. **响应式设计** - 完美适配各种设备
3. **用户友好的交互** - 直观的视图切换和位置搜索
4. **性能优化** - 懒加载、缓存、聚类等优化策略
5. **可扩展架构** - 支持未来功能扩展

这个实施方案提供了完整的技术指导，可以根据具体需求进行调整和优化。
