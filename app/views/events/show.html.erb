<% content_for :title, @event.title %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { 
          items: [
            [t('events.index.title'), events_path], 
            [truncate(@event.title, length: 50), nil]
          ] 
        } %>

        <div class="mb-6">
          <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white transition-colors duration-200">
            <%= @event.title %>
          </h1>
          <div class="mt-2 flex items-center space-x-4">
            <!-- 状态标签 -->
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors duration-200
                         <%= case @event.status
                             when 'published' then 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                             when 'draft' then 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                             when 'cancelled' then 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                             when 'completed' then 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                             else 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
                             end %>">
              <%= t("events.status.#{@event.status}") %>
            </span>
            
            <!-- 分类标签 -->
            <% if @event.event_category %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200 transition-colors duration-200">
                <%= @event.event_category.name %>
              </span>
            <% end %>
            
            <!-- 难度标签 -->
            <% if @event.difficulty_level.present? %>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 transition-colors duration-200">
                <%= t("events.difficulty_levels.#{@event.difficulty_level}") %>
              </span>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <!-- 主要内容 -->
      <div class="col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-8 border border-gray-200 dark:border-gray-700 transition-all duration-200 mb-6">
          <!-- 活动封面图 -->
          <% if @event.cover_image.attached? %>
            <div class="mb-8">
              <%= image_tag @event.cover_image, 
                  class: "w-full h-64 md:h-96 object-cover rounded-lg shadow-sm" %>
            </div>
          <% end %>

          <!-- 活动标题和状态 -->
          <div class="mb-6">
            <div class="flex items-center justify-between mb-4">
              <h1 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
                <%= @event.title %>
              </h1>
              
              <!-- 状态标签 -->
              <div class="flex space-x-2">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                  <%= case @event.status
                      when 'published' then 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      when 'cancelled' then 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                      when 'completed' then 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      else 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                      end %>">
                  <%= t("events.status.#{@event.status}") %>
                </span>
                
                <% if @event.event_category %>
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                    <%= @event.event_category.name %>
                  </span>
                <% end %>
              </div>
            </div>

            <!-- 活动基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
              <!-- 创建者 -->
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">
                    <%= t('events.show.organizer') %>
                  </div>
                  <div><%= @event.user.username || @event.user.email_address %></div>
                </div>
              </div>

              <!-- 时间 -->
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">
                    <%= l(@event.start_time, format: :long) %>
                  </div>
                  <div><%= t('events.show.to') %> <%= l(@event.end_time, format: :long) %></div>
                </div>
              </div>

              <!-- 地点 -->
              <div class="flex items-center">
                <% if @event.is_online? %>
                  <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 2a1 1 0 100 2h2a1 1 0 100-2h-2z" clip-rule="evenodd"></path>
                  </svg>
                  <div>
                    <div class="font-medium text-gray-900 dark:text-white">
                      <%= t('events.show.online_event') %>
                    </div>
                    <% if @event.meeting_link.present? && @registration %>
                      <div>
                        <%= link_to @event.meeting_link, target: "_blank", 
                            class: "text-primary-600 hover:text-primary-500" do %>
                          <%= t('events.show.join_meeting') %>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                <% else %>
                  <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                  </svg>
                  <div>
                    <% if @event.event_location %>
                      <div class="font-medium text-gray-900 dark:text-white">
                        <%= @event.event_location.name %>
                      </div>
                      <div><%= @event.event_location.address %></div>
                      <div><%= @event.event_location.city %>, <%= @event.event_location.country %></div>
                    <% else %>
                      <div class="font-medium text-gray-900 dark:text-white"><%= t('events.show.location_tbd') %></div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- 活动描述 -->
          <% if @event.description.present? %>
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                <%= t('events.show.details') %>
              </h2>
              <div class="prose max-w-none dark:prose-invert">
                <%= simple_format(@event.description) %>
              </div>
            </div>
          <% end %>

          <!-- 参与要求 -->
          <% if @event.prerequisites.present? %>
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                <%= t('events.prerequisites') %>
              </h2>
              <div class="prose max-w-none dark:prose-invert">
                <%= simple_format(@event.prerequisites) %>
              </div>
            </div>
          <% end %>

          <!-- 需要携带 -->
          <% if @event.what_to_bring.present? %>
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                <%= t('events.what_to_bring') %>
              </h2>
              <div class="prose max-w-none dark:prose-invert">
                <%= simple_format(@event.what_to_bring) %>
              </div>
            </div>
          <% end %>

          <!-- 包含内容 -->
          <% if @event.what_included.present? %>
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                <%= t('events.what_included') %>
              </h2>
              <div class="prose max-w-none dark:prose-invert">
                <%= simple_format(@event.what_included) %>
              </div>
            </div>
          <% end %>

          <!-- 讲师信息 -->
          <% if @event.instructors.any? %>
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                <%= t('events.instructors.title') %>
              </h2>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <% @event.event_instructors.each do |instructor| %>
                  <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <div class="flex items-start space-x-4">
                      <div class="flex-shrink-0">
                        <%= render 'shared/avatar', user: instructor.user, size: 'md' %>
                      </div>
                      <div class="flex-1">
                        <h3 class="font-medium text-gray-900 dark:text-white">
                          <%= instructor.user.username %>
                        </h3>
                        <% if instructor.title.present? %>
                          <p class="text-sm text-gray-600 dark:text-gray-400">
                            <%= instructor.title %>
                          </p>
                        <% end %>
                        <p class="text-xs text-primary-600 dark:text-primary-400 mb-2">
                          <%= t("events.instructors.#{instructor.role}") %>
                        </p>
                        <% if instructor.bio.present? %>
                          <p class="text-sm text-gray-600 dark:text-gray-400">
                            <%= truncate(instructor.bio, length: 150) %>
                          </p>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>

          <!-- 相关文档 -->
          <% if @event.documents.any? %>
            <div class="mb-8">
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                <%= t('events.documents') %>
              </h2>
              <div class="space-y-2">
                <% @event.documents.each do |document| %>
                  <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <svg class="w-5 h-5 mr-3 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                    </svg>
                    <%= link_to document.filename, rails_blob_path(document), 
                        class: "text-primary-600 hover:text-primary-500 font-medium" %>
                    <span class="ml-auto text-sm text-gray-500">
                      <%= number_to_human_size(document.byte_size) %>
                    </span>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="lg:col-span-1 space-y-6">
        <!-- 报名卡片 -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 sticky top-6">
          <!-- 价格信息 -->
          <div class="mb-6">
            <div class="text-3xl font-bold text-gray-900 dark:text-white">
              <% if @event.price > 0 %>
                ¥<%= @event.price %>
                <% if @event.member_price && @event.member_price < @event.price %>
                  <span class="text-lg text-gray-500 line-through ml-2">¥<%= @event.member_price %></span>
                  <div class="text-sm text-green-600 dark:text-green-400">
                    <%= t('events.show.member_discount_price', price: @event.member_price) %>
                  </div>
                <% end %>
              <% else %>
                <span class="text-green-600 dark:text-green-400"><%= t('events.show.free') %></span>
              <% end %>
            </div>
          </div>

          <!-- 参与人数信息 -->
          <% if @event.max_participants %>
            <div class="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div class="flex justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-400"><%= t('events.show.registered') %></span>
                <span class="font-medium text-gray-900 dark:text-white">
                  <%= @event.registered_users.count %> / <%= @event.max_participants %>
                </span>
              </div>
              <div class="mt-2 w-full bg-gray-200 rounded-full h-2 dark:bg-gray-600">
                <div class="bg-primary-600 h-2 rounded-full" 
                     style="width: <%= (@event.registered_users.count.to_f / @event.max_participants * 100).round(1) %>%"></div>
              </div>
              <% if @event.available_spots %>
                <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  <%= t('events.show.spots_remaining', count: @event.available_spots) %>
                </div>
              <% end %>
            </div>
          <% end %>

          <!-- 报名按钮 -->
          <% if authenticated? %>
            <% if @registration %>
              <!-- 已报名状态 -->
              <div class="text-center">
                <div class="inline-flex items-center px-4 py-3 bg-green-100 text-green-800 rounded-lg mb-4 dark:bg-green-900 dark:text-green-200">
                  <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                  </svg>
                  <%= t('events.show.already_registered') %>
                </div>
                <%= button_to t('events.registration.cancel_registration'),
                    event_event_registration_path(@event, @registration),
                    method: :delete,
                    confirm: t('events.confirmations.cancel_registration'),
                    class: "w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md font-medium text-sm transition-colors duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600" %>
              </div>
            <% elsif @can_register %>
              <!-- 可以报名 -->
              <%= form_with model: [@event, EventRegistration.new], local: true, 
                  class: "space-y-4" do |f| %>
                <%= f.text_area :notes, 
                    placeholder: t('events.registration.notes_placeholder'),
                    rows: 3,
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:placeholder-gray-400" %>
                
                <%= f.submit t('events.registration.register_button'),
                    class: "w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" %>
              <% end %>
            <% elsif @event.full? %>
              <!-- 活动已满 -->
              <div class="w-full bg-red-100 text-red-800 font-medium py-3 px-4 rounded-md text-center dark:bg-red-900 dark:text-red-200">
                <%= t('events.show.event_full') %>
              </div>
            <% elsif @event.cancelled? %>
              <!-- 活动已取消 -->
              <div class="w-full bg-red-100 text-red-800 font-medium py-3 px-4 rounded-md text-center dark:bg-red-900 dark:text-red-200">
                <%= t('events.show.event_cancelled') %>
              </div>
            <% else %>
              <!-- 无权限报名 -->
              <div class="w-full bg-gray-100 text-gray-600 font-medium py-3 px-4 rounded-md text-center dark:bg-gray-700 dark:text-gray-400">
                <%= t('events.show.no_permission') %>
              </div>
            <% end %>
          <% else %>
            <!-- 未登录用户 -->
            <div class="text-center">
              <p class="text-gray-600 dark:text-gray-400 mb-4"><%= t('common.auth.please_login') %></p>
              <%= link_to sign_in_path,
                  class: "w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200 inline-block text-center" do %>
                <%= t('common.auth.login_to_register') %>
              <% end %>
            </div>
          <% end %>

          <!-- 管理操作 -->
          <% if authenticated? && (allowed_to?(:update?, @event) || allowed_to?(:destroy?, @event)) %>
            <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3"><%= t('events.actions.management_actions') %></h4>
              <div class="space-y-2">
                <% if allowed_to?(:update?, @event) %>
                  <%= link_to edit_event_path(@event),
                      class: "w-full text-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600" do %>
                    <%= t('events.actions.edit_event') %>
                  <% end %>
                <% end %>
                
                <% if @event.draft? && allowed_to?(:publish?, @event) %>
                  <%= button_to publish_event_path(@event), method: :patch,
                      confirm: t('events.confirmations.publish'),
                      class: "w-full text-center px-3 py-2 text-sm font-medium text-white bg-green-600 border border-green-600 rounded-md hover:bg-green-700 hover:border-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2" do %>
                    <%= t('events.actions.publish_event') %>
                  <% end %>
                <% end %>
                
                <% if allowed_to?(:manage_registrations?, @event) %>
                  <%= link_to event_event_registrations_path(@event),
                      class: "w-full text-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600" do %>
                    <%= t('events.actions.manage_registrations') %>
                  <% end %>
                <% end %>
                
                <% if allowed_to?(:destroy?, @event) %>
                  <%= link_to event_path(@event), method: :delete,
                      confirm: t('events.confirmations.delete_event'),
                      class: "w-full text-center px-3 py-2 text-sm font-medium text-red-700 bg-white border border-red-300 rounded-md hover:bg-red-50 dark:bg-gray-700 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20" do %>
                    <%= t('events.actions.delete_event') %>
                  <% end %>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>

        <!-- 参与者列表卡片 - 移到侧边栏 -->
        <% if @event_registrations.any? %>
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                <%= t('events.participants.title') %>
                <span class="text-sm font-normal text-gray-500 dark:text-gray-400 ml-1">
                  (<%= @event_registrations.count %>)
                </span>
              </h3>
              
              <!-- Toggle 按钮对所有已登录用户可见 -->
              <% if authenticated? %>
                <button type="button" 
                        data-collapse-toggle="participants-sidebar-list"
                        aria-expanded="false" 
                        aria-controls="participants-sidebar-list"
                        class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg p-1.5 transition-colors">
                  <svg class="w-4 h-4 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="sr-only"><%= t('common.actions.toggle') %></span>
                </button>
              <% end %>
            </div>
            
            <!-- 对管理员始终显示，对普通用户默认隐藏，可通过 toggle 显示 -->
            <div id="participants-sidebar-list" 
                 class="space-y-2 <%= 'hidden' unless allowed_to?(:update?, @event) %>">
              <% @event_registrations.limit(5).each do |registration| %>
                <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div class="flex items-center space-x-2 min-w-0 flex-1">
                    <%= render 'shared/avatar', user: registration.user, size: 'h-6 w-6' %>
                    <div class="min-w-0 flex-1">
                      <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                        <%= registration.user.username || registration.user.email_address %>
                      </div>
                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        <%= l(registration.registered_at, format: :short) %>
                      </div>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-1 flex-shrink-0">
                    <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium 
                      <%= case registration.status
                          when 'confirmed' then 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-200'
                          when 'pending' then 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-200'
                          when 'cancelled' then 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-200'
                          else 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-200'
                          end %>">
                      <%= case registration.status
                          when 'confirmed' then '✓'
                          when 'pending' then '?'
                          when 'cancelled' then '✗'
                          when 'attended' then '✓'
                          else '•'
                          end %>
                    </span>
                    
                    <% if registration.amount_paid > 0 %>
                      <span class="text-xs font-medium text-gray-600 dark:text-gray-300">
                        ¥<%= registration.amount_paid.to_i %>
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
              
              <% if @event_registrations.count > 5 %>
                <div class="text-center pt-2 border-t border-gray-200 dark:border-gray-600">
                  <span class="text-xs text-gray-500 dark:text-gray-400">
                    <%= t('events.show.more_participants', count: @event_registrations.count - 5) %>
                  </span>
                </div>
              <% end %>
              
                              <% if allowed_to?(:manage_registrations?, @event) %>
                  <div class="pt-3 border-t border-gray-200 dark:border-gray-600">
                    <%= link_to event_event_registrations_path(@event), 
                        class: "inline-flex items-center text-sm font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300" do %>
                      <%= t('events.actions.manage_registrations') %>
                      <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    <% end %>
                  </div>
                <% end %>
            </div>
          </div>
        <% end %>

        <!-- 分享卡片 -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4"><%= t('events.actions.share_event') %></h3>
          <div class="flex space-x-2">
            <!-- 复制链接按钮 -->
            <button type="button"
                    onclick="navigator.clipboard.writeText(window.location.href)"
                    class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-3 rounded-md text-sm transition-colors duration-200 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300">
              <%= t('events.actions.copy_link') %>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 