# Event Feature Test Helpers

这个文件包含了用于事件功能测试的辅助方法。这些方法简化了事件相关的测试编写，提供了一致的接口来执行常见的测试操作。

## 使用方法

所有的辅助方法都会自动包含在 `type: :feature` 的测试中。

## 可用的辅助方法

### 事件创建和管理

#### `create_event(attributes = {})`
创建一个新事件。

```ruby
event_attributes = {
  title: 'Ruby Workshop',
  description: 'Learn Ruby programming',
  start_time: 1.week.from_now,
  end_time: 1.week.from_now + 2.hours,
  price: 50.0,
  max_participants: 20,
  event_category: category
}

create_event(event_attributes)
```

#### `fill_event_form(attributes = {})`
填写事件表单（用于更复杂的表单操作）。

#### `edit_event(event, attributes = {})`
编辑现有事件。

```ruby
edit_event(event, { title: 'Updated Title', price: 75.0 })
```

#### `publish_event(event)`
发布事件。

```ruby
publish_event(draft_event)
```

#### `cancel_event(event)`
取消事件。

```ruby
cancel_event(published_event)
```

### 搜索和筛选

#### `search_events(query)`
搜索事件。

```ruby
search_events('Ruby')
```

#### `filter_events_by_category(category_name)`
按分类筛选事件。

```ruby
filter_events_by_category('Technology')
```

#### `filter_events_by_price(price_range)`
按价格筛选事件。

```ruby
filter_events_by_price('Free')
```

#### `filter_events(filters = {})`
通用筛选方法。

```ruby
filter_events(category: 'Technology', location: 'Toronto')
```

### 断言和验证

#### `expect_event_in_list(event)`
验证事件在列表中显示。

```ruby
expect_event_in_list(published_event)
```

#### `expect_event_not_in_list(event)`
验证事件不在列表中显示。

```ruby
expect_event_not_in_list(draft_event)
```

#### `expect_success_notice(message)`
验证成功通知消息。

```ruby
expect_success_notice('Event created successfully')
```

#### `expect_error_notice(message)`
验证错误通知消息。

```ruby
expect_error_notice('Please fix the errors below')
```

#### `expect_event_details(event)`
验证事件详情页面显示正确信息。

```ruby
expect_event_details(event)
```

#### `expect_event_published`
验证事件发布成功。

#### `expect_event_cancelled`
验证事件取消成功。

### 事件报名相关

#### `register_for_event(event, notes: nil)`
报名参加事件。

```ruby
register_for_event(event, notes: 'Looking forward to this event')
```

#### `cancel_registration(event)`
取消事件报名。注意：这会将报名状态设置为 "cancelled"，而不是删除记录。

```ruby
cancel_registration(event)
```

#### `expect_registration_success`
验证报名成功。

#### `expect_cancellation_success`
验证取消报名成功。

### 事件状态和容量

#### `expect_event_full`
验证事件已满员。

#### `expect_capacity_info(registered_count, max_participants = nil)`
验证事件容量信息显示。

```ruby
expect_capacity_info(7, 10)  # 7/10 已报名
```

#### `expect_free_event_display`
验证免费事件显示。

#### `expect_paid_event_display(price)`
验证付费事件显示。

```ruby
expect_paid_event_display(99.99)
```

#### `expect_member_discount_display(regular_price, member_price)`
验证会员优惠价格显示。

```ruby
expect_member_discount_display(100, 80)
```

### 在线事件相关

#### `expect_online_event_display`
验证在线事件显示。

#### `expect_meeting_link_visible`
验证会议链接可见（已报名用户）。

#### `expect_meeting_link_hidden`
验证会议链接不可见（未报名用户）。

### 事件列表和导航

#### `switch_to_events_tab(tab)`
切换到指定的事件标签页。

```ruby
switch_to_events_tab('past')  # 切换到过往事件
```

#### `expect_tab_active(tab_name)`
验证标签页激活状态。

```ruby
expect_tab_active('upcoming')
```

#### `expect_pagination_present`
验证分页存在。

#### `click_next_page` / `click_previous_page`
点击下一页/上一页。

### 文件上传

#### `upload_event_cover_image(file_path = 'spec/fixtures/files/sample_image.jpg')`
上传事件封面图片。

#### `upload_event_documents(file_path = 'spec/fixtures/files/sample_document.pdf')`
上传事件文档。

#### `expect_file_uploaded(event, attachment_type)`
验证文件已上传。

```ruby
expect_file_uploaded(event, :cover_image)
```

### 权限和错误处理

#### `expect_management_actions_visible(event)`
验证管理操作可见。

#### `expect_management_actions_hidden(event)`
验证管理操作不可见。

#### `expect_authorization_error`
验证权限错误。

#### `expect_404_page`
验证404错误页面。

#### `expect_login_prompt`
验证登录提示。

#### `expect_form_validation_errors(field_errors)`
验证表单验证错误。

```ruby
expect_form_validation_errors(["Title can't be blank", "Start time can't be blank"])
```

### JavaScript 和等待

#### `wait_for_search_suggestions`
等待搜索建议出现。

#### `wait_for_modal`
等待模态框出现。

#### `wait_for_page_load`
等待页面加载完成。

## 示例测试

```ruby
require 'rails_helper'

RSpec.feature 'Event Management', type: :feature do
  let(:user) { create(:user) }
  let(:category) { create(:event_category) }

  background do
    sign_in(user)
  end

  scenario 'User creates and publishes an event' do
    event_attributes = {
      title: 'Ruby Workshop',
      description: 'Learn Ruby programming',
      start_time: 1.week.from_now,
      end_time: 1.week.from_now + 2.hours,
      price: 50.0,
      event_category: category
    }

    create_event(event_attributes)
    expect_success_notice('Event created successfully')

    publish_event(Event.last)
    expect_event_published
  end

  scenario 'User searches for events' do
    create(:event, :published, title: 'Ruby Workshop', event_category: category)
    create(:event, :published, title: 'Python Workshop', event_category: category)

    visit events_path
    search_events('Ruby')

    expect_event_in_list(Event.find_by(title: 'Ruby Workshop'))
    expect_event_not_in_list(Event.find_by(title: 'Python Workshop'))
  end

  scenario 'User registers and cancels registration' do
    event = create(:event, :published, :upcoming, event_category: category)

    # 报名事件
    register_for_event(event, notes: 'Looking forward to this event')
    expect_registration_success

    # 取消报名
    cancel_registration(event)
    expect_cancellation_success

    # 验证数据库状态
    registration = EventRegistration.find_by(user: user, event: event)
    expect(registration.status).to eq('cancelled')
  end
end
```

## 注意事项

1. 这些辅助方法假设你的应用使用了国际化（I18n）
2. 某些方法可能需要根据你的具体UI实现进行调整
3. 对于复杂的表单（如日期时间选择器），可能需要特殊处理
4. 建议在使用前先运行测试确保方法正常工作
