<div class="w-full sm:max-w-2xl mb-8">
  <ol class="flex items-center w-full text-sm font-medium text-center text-gray-500 dark:text-gray-400 sm:text-base whitespace-nowrap">
    <li class="flex md:w-full items-center <%= current_step == 'phone_verification' ? 'text-primary-600 dark:text-primary-500' : 'text-gray-500' %> after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700">
      <span class="flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
        <% if current_step != 'phone_verification'%>
          <svg class="w-3.5 h-3.5 sm:w-4 sm:h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
          </svg>
        <% else %>
           <span class="me-2">1</span>
         <% end %>
         <%= t('auth.registrations.steps.progress.verify_identity') %> <%# Changed text %>
         <%# <span class="hidden sm:inline-flex sm:ms-2"><%= t('auth.registrations.steps.progress.code') % ></span> %>
       </span>
     </li>
    <li class="flex md:w-full items-center <%= current_step == 'create_account' ? 'text-primary-600 dark:text-primary-500' : 'text-gray-500' %> after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-200 after:border-1 after:hidden sm:after:inline-block after:mx-6 xl:after:mx-10 dark:after:border-gray-700">
      <span class="flex items-center after:content-['/'] sm:after:hidden after:mx-2 after:text-gray-200 dark:after:text-gray-500">
        <% if current_step == 'subscribe' %>
          <svg class="w-3.5 h-3.5 sm:w-4 sm:h-4 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
          </svg>
        <% else %>
          <span class="me-2">2</span>
        <% end %>
        <%= t('auth.registrations.steps.progress.create') %>
        <span class="hidden sm:inline-flex sm:ms-2"><%= t('auth.registrations.steps.progress.account') %></span>
      </span>
    </li>
    <%# Removed the subscribe step %>
  </ol>
</div>
