<%#
  Events Calendar View Partial (内嵌版本)

  用法：
  render "events/calendar_view", events_by_date: @events_by_date, date: @date, start_date: @start_date, end_date: @end_date

  参数：
  - events_by_date: 按日期分组的事件哈希
  - date: 当前显示的月份日期
  - start_date: 日历开始日期
  - end_date: 日历结束日期

  注意：目前日历视图通过重定向到单独页面处理，此 partial 为未来内嵌显示预留
%>

<!-- 日历视图容器 -->
<div data-view-switcher-target="calendarContainer" class="<%= @current_view == 'calendar' ? '' : 'hidden' %>">
  <div data-controller="calendar"
       data-calendar-load-events-url-value="<%= day_events_events_path %>"
       data-calendar-all-events-text-value="<%= t('events.calendar.all_events_on', date: '') %>"
       data-view-switcher-default-view-value="calendar">

    <!-- 日历导航 -->
    <div class="flex items-center justify-between mb-4">
      <%= link_to events_path(date: date.prev_month, view: 'calendar', **params.except(:date, :page).permit!), class: "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-2" do %>
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      <% end %>

      <div class="flex items-center space-x-4">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
          <%= l(date, format: :month_year) %>
        </h2>
        <%= link_to t('events.calendar.today'), events_path(view: 'calendar', **params.except(:date, :page).permit!), class: "text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300" %>
      </div>

      <%= link_to events_path(date: date.next_month, view: 'calendar', **params.except(:date, :page).permit!), class: "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-2" do %>
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      <% end %>
    </div>

    <!-- 日历网格 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
      <!-- 星期标题 -->
      <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700">
        <% I18n.t('date.abbr_day_names').each do |day| %>
          <div class="bg-gray-50 dark:bg-gray-800 p-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            <%= day %>
          </div>
        <% end %>
      </div>

      <!-- 日期网格 -->
      <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-700">
        <% (start_date..end_date).each do |calendar_date| %>
          <div class="bg-white dark:bg-gray-800 min-h-[120px] relative <%= 'opacity-50' if calendar_date.month != date.month %>">
            <div class="p-2">
              <div class="text-sm <%= calendar_date == Date.current ? 'font-bold text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-gray-100' %>">
                <%= calendar_date.day %>
              </div>

              <!-- 当天的事件 -->
              <% if events_by_date[calendar_date] %>
                <div class="mt-1 space-y-1">
                  <% events_by_date[calendar_date].first(3).each do |event| %>
                    <%= link_to event_path(event),
                        class: "block w-full text-left px-2 py-1 text-xs rounded #{event_calendar_color_class(event)} truncate hover:opacity-80 transition-opacity" do %>
                      <span class="inline-block w-2 h-2 rounded-full <%= event_category_color_class(event.event_category) %> mr-1"></span>
                      <%= truncate(event.title, length: 20) %>
                    <% end %>
                  <% end %>

                  <% if events_by_date[calendar_date].size > 3 %>
                    <button type="button"
                            class="w-full text-left px-2 py-1 text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            data-modal-toggle="day-events-modal"
                            data-action="click->calendar#showDayEvents"
                            data-date="<%= calendar_date %>"
                            data-date-formatted="<%= l(calendar_date, format: :long) %>">
                      +<%= events_by_date[calendar_date].size - 3 %> <%= t('events.calendar.more') %>
                    </button>
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

  </div>
</div>

<!-- 某天所有事件模态框 - 移到日历视图容器外面，确保始终在 DOM 中可见 -->
<div id="day-events-modal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
  <div class="relative p-4 w-full max-w-2xl max-h-full">
    <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
      <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white"
            data-calendar-target="dayModalTitle">
          <%= t('events.calendar.all_events_on', date: '') %>
        </h3>
        <button type="button"
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white"
                data-modal-hide="day-events-modal"
                data-action="click->calendar#resetModal">
          <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
          </svg>
        </button>
      </div>
      <div class="p-4 md:p-5">
        <div class="space-y-3" data-calendar-target="dayEventsList">
          <!-- 动态加载事件列表 -->
        </div>
      </div>
    </div>
  </div>
</div>
