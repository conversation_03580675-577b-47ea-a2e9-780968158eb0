# API 文档

本文档描述了 AI Pro CGC 项目的 API 接口规范。

## 目录

- [认证](#认证)
- [用户管理 API](#用户管理-api)
- [AI 助手 API](#ai-助手-api)
- [对话管理 API](#对话管理-api)
- [知识库 API](#知识库-api)
- [微信集成 API](#微信集成-api)
- [错误处理](#错误处理)

## 认证

### 登录方式

1. **邮箱/用户名登录**
   ```http
   POST /session
   Content-Type: application/json

   {
     "email_address": "<EMAIL>",
     "password": "password123"
   }
   ```

2. **第三方登录 (OAuth)**
   ```http
   GET /auth/wechat
   GET /auth/github
   GET /auth/google_oauth2
   ```

3. **登录响应**
   ```json
   {
     "user": {
       "id": 1,
       "username": "john_doe",
       "email_address": "<EMAIL>",
       "avatar_url": "https://example.com/avatar.jpg"
     },
     "session_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
     "redirect_to": "/dashboard"
   }
   ```

## 用户管理 API

### 获取用户信息

```http
GET /users/:id
Authorization: Bearer {session_token}
```

**响应示例：**
```json
{
  "user": {
    "id": 1,
    "username": "john_doe",
    "email_address": "<EMAIL>",
    "phone_number": "+8613812345678",
    "location": "Beijing, China",
    "gender": "male",
    "preferences": {
      "language": "zh",
      "theme": "light",
      "timezone": "Asia/Shanghai"
    },
    "subscription": {
      "status": "active",
      "plan": "pro",
      "expires_at": "2024-12-31T23:59:59Z"
    },
    "storage": {
      "used": 52428800,
      "limit": 10737418240
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

### 更新用户偏好

```http
PATCH /users/update_preferences
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "preferences": {
    "language": "en",
    "theme": "dark",
    "timezone": "UTC",
    "notifications": {
      "email": true,
      "web": false
    }
  }
}
```

### 更新用户资料

```http
PATCH /users/update_profile
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "username": "new_username",
  "phone_number": "+8613987654321",
  "location": "Shanghai, China",
  "gender": "female"
}
```

## AI 助手 API

### 获取助手列表

```http
GET /ai/assistants
Authorization: Bearer {session_token}
```

**响应示例：**
```json
{
  "assistants": [
    {
      "id": 1,
      "name": "编程助手",
      "instructions": "你是一个专业的编程助手...",
      "tool_choice": "auto",
      "tools": ["Langchain::Tool::Wikipedia", "Langchain::Tool::Calculator"],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 3,
    "total_count": 25
  }
}
```

### 创建助手

```http
POST /ai/assistants
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "assistant": {
    "name": "新助手",
    "instructions": "你是一个专业的助手...",
    "tool_choice": "auto",
    "tools": ["Langchain::Tool::Wikipedia"]
  }
}
```

### 更新助手

```http
PATCH /ai/assistants/:id
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "assistant": {
    "name": "更新后的助手名称",
    "instructions": "更新后的指令..."
  }
}
```

### 删除助手

```http
DELETE /ai/assistants/:id
Authorization: Bearer {session_token}
```

## 对话管理 API

### 获取对话列表

```http
GET /ai/conversations
Authorization: Bearer {session_token}
```

**响应示例：**
```json
{
  "conversations": [
    {
      "id": 1,
      "title": "关于 Rails 开发的问题",
      "assistant": {
        "id": 1,
        "name": "编程助手"
      },
      "last_message": {
        "content": "这是最后一条消息...",
        "created_at": "2024-01-15T10:30:00Z"
      },
      "message_count": 15,
      "created_at": "2024-01-10T09:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 创建对话

```http
POST /ai/conversations
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "conversation": {
    "assistant_id": 1,
    "title": "新对话标题"
  }
}
```

### 获取对话详情

```http
GET /ai/conversations/:id
Authorization: Bearer {session_token}
```

**响应示例：**
```json
{
  "conversation": {
    "id": 1,
    "title": "关于 Rails 开发的问题",
    "assistant": {
      "id": 1,
      "name": "编程助手"
    },
    "messages": [
      {
        "id": 1,
        "role": "user",
        "content": "如何在 Rails 中实现认证？",
        "created_at": "2024-01-10T09:00:00Z"
      },
      {
        "id": 2,
        "role": "assistant",
        "content": "在 Rails 中实现认证有多种方式...",
        "tool_calls": [],
        "status": "completed",
        "created_at": "2024-01-10T09:01:00Z"
      }
    ]
  }
}
```

### 发送消息

```http
POST /ai/messages
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "message": {
    "conversation_id": 1,
    "content": "请解释一下 Rails 的 MVC 架构"
  }
}
```

**响应示例（流式）：**
```json
{
  "message": {
    "id": 3,
    "role": "user",
    "content": "请解释一下 Rails 的 MVC 架构",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### 切换助手

```http
POST /ai/conversations/:id/switch_assistant
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "assistant_id": 2
}
```

### 清空对话

```http
POST /ai/conversations/:id/clear_conversation
Authorization: Bearer {session_token}
```

## 知识库 API

### 获取知识库列表

```http
GET /knowledge_bases
Authorization: Bearer {session_token}
```

**响应示例：**
```json
{
  "knowledge_bases": [
    {
      "id": 1,
      "name": "Rails 开发文档",
      "description": "包含 Rails 开发的各种文档和教程",
      "visibility": "private",
      "posts_count": 25,
      "owner": {
        "id": 1,
        "username": "john_doe"
      },
      "teams": [],
      "archived": false,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### 创建知识库

```http
POST /knowledge_bases
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "knowledge_base": {
    "name": "新知识库",
    "description": "知识库描述",
    "visibility": "private"
  }
}
```

### 获取知识库详情

```http
GET /knowledge_bases/:id
Authorization: Bearer {session_token}
```

### 添加文档到知识库

```http
POST /knowledge_bases/:id/create_post
Authorization: Bearer {session_token}
Content-Type: application/json

{
  "post": {
    "title": "文档标题",
    "content": "文档内容（Markdown 格式）",
    "published": true
  }
}
```

### 上传文件到知识库

```http
POST /knowledge_bases/:id/create_base_unit
Authorization: Bearer {session_token}
Content-Type: multipart/form-data

{
  "base_unit": {
    "name": "文件名称",
    "file": file_upload,
    "description": "文件描述"
  }
}
```

### 文件转换为 Markdown

```http
GET /knowledge_bases/:knowledge_base_id/base_units/:id/convert_to_md
Authorization: Bearer {session_token}
```

## 微信集成 API

### 微信公众号回调

```http
GET /wechat?signature={signature}&timestamp={timestamp}&nonce={nonce}&echostr={echostr}
```

```http
POST /wechat
Content-Type: application/xml

<xml>
  <ToUserName><![CDATA[toUser]]></ToUserName>
  <FromUserName><![CDATA[fromUser]]></FromUserName>
  <CreateTime>1234567890</CreateTime>
  <MsgType><![CDATA[text]]></MsgType>
  <Content><![CDATA[用户发送的消息]]></Content>
  <MsgId>1234567890123456</MsgId>
</xml>
```

### 微信服务号回调

```http
GET /service_wechat?signature={signature}&timestamp={timestamp}&nonce={nonce}&echostr={echostr}
```

```http
POST /service_wechat
Content-Type: application/xml

<xml>
  <ToUserName><![CDATA[toUser]]></ToUserName>
  <FromUserName><![CDATA[fromUser]]></FromUserName>
  <CreateTime>1234567890</CreateTime>
  <MsgType><![CDATA[event]]></MsgType>
  <Event><![CDATA[subscribe]]></Event>
</xml>
```

## 错误处理

### 错误响应格式

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "email_address": ["不能为空", "格式不正确"],
      "password": ["密码长度不能少于8位"]
    }
  },
  "request_id": "req_1234567890"
}
```

### 常见错误代码

| 错误代码                  | HTTP 状态码 | 说明             |
| ------------------------- | ----------- | ---------------- |
| `AUTHENTICATION_REQUIRED` | 401         | 需要身份认证     |
| `AUTHORIZATION_FAILED`    | 403         | 权限不足         |
| `RESOURCE_NOT_FOUND`      | 404         | 资源不存在       |
| `VALIDATION_ERROR`        | 422         | 请求参数验证失败 |
| `RATE_LIMIT_EXCEEDED`     | 429         | 请求频率超限     |
| `INTERNAL_ERROR`          | 500         | 服务器内部错误   |

### 状态码说明

- `200 OK` - 请求成功
- `201 Created` - 资源创建成功
- `204 No Content` - 请求成功但无返回内容
- `400 Bad Request` - 请求格式错误
- `401 Unauthorized` - 未认证
- `403 Forbidden` - 权限不足
- `404 Not Found` - 资源不存在
- `422 Unprocessable Entity` - 验证失败
- `429 Too Many Requests` - 请求频率超限
- `500 Internal Server Error` - 服务器错误

## 分页规范

所有列表类 API 都支持分页：

### 请求参数

```http
GET /ai/assistants?page=1&per_page=20
```

### 响应格式

```json
{
  "data": [...],
  "pagination": {
    "current_page": 1,
    "per_page": 20,
    "total_pages": 5,
    "total_count": 100,
    "has_next_page": true,
    "has_prev_page": false
  }
}
```

## 版本控制

API 支持版本控制，在请求头中指定版本：

```http
Accept: application/vnd.api+json; version=1
```

或在 URL 中指定：

```http
GET /api/v1/assistants
```

## 速率限制

- 已认证用户：1000 请求/小时
- 未认证用户：100 请求/小时
- AI 对话：50 请求/小时

响应头中包含限制信息：

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

## WebSocket API

### 实时对话

```javascript
// 连接到对话频道
const cable = ActionCable.createConsumer("/cable");
const subscription = cable.subscriptions.create(
  { channel: "ConversationChannel", conversation_id: 1 },
  {
    received: function(data) {
      // 处理接收到的消息
      console.log(data);
    }
  }
);

// 发送消息
subscription.send({
  action: "send_message",
  content: "Hello, AI!"
});
```

### 事件类型

- `message_received` - 接收到新消息
- `message_updated` - 消息状态更新
- `typing_start` - 开始输入
- `typing_stop` - 停止输入
- `conversation_updated` - 对话信息更新

## SDK 示例

### JavaScript

```javascript
class AiProCgcClient {
  constructor(baseUrl, token) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  async request(method, path, data = null) {
    const response = await fetch(`${this.baseUrl}${path}`, {
      method,
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : null,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取助手列表
  async getAssistants() {
    return this.request('GET', '/ai/assistants');
  }

  // 发送消息
  async sendMessage(conversationId, content) {
    return this.request('POST', '/ai/messages', {
      message: { conversation_id: conversationId, content }
    });
  }
}
```

### Ruby

```ruby
class AiProCgcClient
  def initialize(base_url, token)
    @base_url = base_url
    @token = token
  end

  def get_assistants
    request(:get, '/ai/assistants')
  end

  def send_message(conversation_id, content)
    request(:post, '/ai/messages', {
      message: { conversation_id: conversation_id, content: content }
    })
  end

  private

  def request(method, path, data = nil)
    uri = URI("#{@base_url}#{path}")
    
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = uri.scheme == 'https'
    
    request = case method
    when :get then Net::HTTP::Get.new(uri)
    when :post then Net::HTTP::Post.new(uri)
    when :patch then Net::HTTP::Patch.new(uri)
    when :delete then Net::HTTP::Delete.new(uri)
    end
    
    request['Authorization'] = "Bearer #{@token}"
    request['Content-Type'] = 'application/json'
    request.body = data.to_json if data
    
    response = http.request(request)
    JSON.parse(response.body)
  end
end
``` 