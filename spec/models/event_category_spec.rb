require 'rails_helper'

RSpec.describe EventCategory, type: :model do
  let(:category) { build(:event_category) }

  describe 'associations' do
    it { should have_many(:events).dependent(:destroy) }
  end

  describe 'validations' do
    subject {
      category = build(:event_category)
      category.skip_slug_generation!
      category
    }

    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_most(255) }
    it { should validate_presence_of(:slug) }
    it { should validate_length_of(:slug).is_at_most(255) }
    it { should validate_uniqueness_of(:slug) }

    describe 'name validation' do
      it 'is valid with a proper name' do
        category.name = 'Technology'
        expect(category).to be_valid
      end

      it 'is invalid without a name' do
        category.name = nil
        expect(category).not_to be_valid
        expect(category.errors[:name]).to include("can't be blank")
      end

      it 'is invalid with a name longer than 255 characters' do
        category.name = 'a' * 256
        expect(category).not_to be_valid
        expect(category.errors[:name]).to include('is too long (maximum is 255 characters)')
      end
    end

    describe 'slug validation' do
      it 'is valid with a unique slug' do
        category.slug = 'unique-slug'
        expect(category).to be_valid
      end

      it 'is invalid with a duplicate slug' do
        create(:event_category, slug: 'duplicate-slug')
        category.slug = 'duplicate-slug'
        expect(category).not_to be_valid
        expect(category.errors[:slug]).to include('has already been taken')
      end

      it 'is invalid without a slug' do
        category.slug = nil
        category.skip_slug_generation!
        expect(category).not_to be_valid
        expect(category.errors[:slug]).to include("can't be blank")
      end

      it 'is invalid with a slug longer than 255 characters' do
        category.slug = 'a' * 256
        expect(category).not_to be_valid
        expect(category.errors[:slug]).to include('is too long (maximum is 255 characters)')
      end
    end
  end

  describe 'callbacks' do
    describe 'before_validation :generate_slug' do
      it 'generates slug from name when slug is blank' do
        category.name = 'Technology & Programming'
        category.slug = nil
        category.valid?
        expect(category.slug).to eq('technology-programming')
      end

      it 'generates slug with special characters removed' do
        category.name = 'Art & Design (Creative)'
        category.slug = nil
        category.valid?
        expect(category.slug).to eq('art-design-creative')
      end

      it 'does not override existing slug' do
        category.name = 'Technology'
        category.slug = 'custom-tech-slug'
        category.valid?
        expect(category.slug).to eq('custom-tech-slug')
      end

      it 'does not generate slug when name is blank' do
        category.name = nil
        category.slug = nil
        category.valid?
        expect(category.slug).to be_nil
      end

      it 'generates slug when name is present and slug is blank' do
        category.name = 'Business Events'
        category.slug = ''
        category.valid?
        expect(category.slug).to eq('business-events')
      end
    end
  end

  describe 'scopes' do
    let!(:tech_category) { create(:event_category, :technology) }
    let!(:business_category) { create(:event_category, :business) }
    let!(:empty_category) { create(:event_category, name: 'Empty Category') }

    before do
      # Create events for tech_category (not archived)
      create_list(:event, 2, event_category: tech_category, archived: false)
      # Create archived event for tech_category
      create(:event, event_category: tech_category, archived: true)
      # Create events for business_category (not archived)
      create(:event, event_category: business_category, archived: false)
      # empty_category has no events
    end

    describe '.active' do
      it 'returns categories that have non-archived events' do
        active_categories = EventCategory.active
        expect(active_categories).to include(tech_category)
        expect(active_categories).to include(business_category)
        expect(active_categories).not_to include(empty_category)
      end

      it 'does not return duplicate categories' do
        active_categories = EventCategory.active
        expect(active_categories.count).to eq(2)
        expect(active_categories.where(id: tech_category.id).count).to eq(1)
      end
    end
  end

  describe 'instance methods' do
    describe '#to_s' do
      it 'returns the category name' do
        category.name = 'Technology'
        expect(category.to_s).to eq('Technology')
      end
    end
  end

  describe 'factory validations' do
    it 'creates valid category with factory' do
      category = build(:event_category)
      expect(category).to be_valid
    end

    it 'creates valid category with traits' do
      tech_category = build(:event_category, :technology)
      expect(tech_category).to be_valid
      expect(tech_category.name).to eq('Technology')

      business_category = build(:event_category, :business)
      expect(business_category).to be_valid
      expect(business_category.name).to eq('Business')

      art_category = build(:event_category, :art)
      expect(art_category).to be_valid
      expect(art_category.name).to eq('Art & Design')
    end
  end

  describe 'edge cases' do
    it 'handles unicode characters in name' do
      category.name = '技术与编程'
      category.slug = nil
      category.valid?
      expect(category.slug).to be_present
    end

    it 'handles very long names for slug generation' do
      long_name = 'This is a very long category name that should be properly parameterized into a slug'
      category.name = long_name
      category.slug = nil
      category.valid?
      expect(category.slug).to eq(long_name.parameterize)
    end

    it 'handles names with numbers and special characters' do
      category.name = 'Web 3.0 & Blockchain (2024)'
      category.slug = nil
      category.valid?
      expect(category.slug).to eq('web-3-0-blockchain-2024')
    end
  end

  describe 'dependent destroy' do
    it 'destroys associated events when category is destroyed' do
      category = create(:event_category, :with_events)
      expect { category.destroy }.to change(Event, :count).by(-3)
    end
  end
end
