require "administrate/base_dashboard"

class EventInstructorDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    event: Field::BelongsTo,
    user: Field::BelongsTo,
    role: Field::Select.with_options(
      collection: EventInstructor.roles.keys.map { |role| [role.humanize, role] }
    ),
    title: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    event
    user
    role
    title
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    event
    user
    role
    title
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    event
    user
    role
    title
  ].freeze

  COLLECTION_FILTERS = {
    by_role: ->(resources) { resources.by_role(params[:role]) if params[:role].present? },
    primary_instructors: ->(resources) { resources.primary_instructors }
  }.freeze

  def display_resource(event_instructor)
    event_instructor.display_name
  end
end
