<%# 定价方案对比表格 partial，可根据需要调整表头和内容 %>
<div class="text-center mb-8">
  <h2 class="text-3xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-4xl">
    对比方案
  </h2>
  <p class="mt-3 max-w-2xl mx-auto text-xl text-gray-500 dark:text-gray-400 sm:mt-4">
    找到最适合您需求的方案
  </p>
</div>

<div class="max-w-4xl mx-auto overflow-x-auto relative shadow-md sm:rounded-lg border border-gray-200 dark:border-gray-700">
  <table class="w-full text-sm text-gray-500 dark:text-gray-400 table-fixed">
    <thead class="text-xs font-medium text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-300">
      <tr>
        <th scope="col" class="py-4 px-6 w-2/5 text-left border-b border-gray-200 dark:border-gray-600">
          功能
        </th>
        <% PRICING_PLANS.each do |plan| %>
          <th scope="col" class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600 <%= plan[:plan_type] == 'monthly' ? 'bg-primary-50 dark:bg-primary-900/10' : '' %>">
            <%= plan[:name] %>
          </th>
        <% end %>
      </tr>
    </thead>
    <tbody>
      <!-- 功能对比行 -->
      <tr class="bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700">
        <th scope="row" class="py-4 px-6 font-medium text-gray-900 dark:text-white text-left border-b border-gray-200 dark:border-gray-600">
          个人/企业账户分离
        </th>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">
          <svg class="w-5 h-5 mx-auto text-primary-500 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
        </td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">
          <svg class="w-5 h-5 mx-auto text-primary-500 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
        </td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600 bg-primary-50 dark:bg-primary-900/10">
          <svg class="w-5 h-5 mx-auto text-primary-500 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
        </td>
      </tr>
      
      <tr class="bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700">
        <th scope="row" class="py-4 px-6 font-medium text-gray-900 dark:text-white text-left border-b border-gray-200 dark:border-gray-600">
          团队成员数
        </th>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">1</td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">10</td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600 bg-primary-50 dark:bg-primary-900/10">
          <span class="font-medium">无限制</span>
        </td>
      </tr>
      
      <tr class="bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700">
        <th scope="row" class="py-4 px-6 font-medium text-gray-900 dark:text-white text-left border-b border-gray-200 dark:border-gray-600">
          存储空间
        </th>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">100MB</td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">10GB</td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600 bg-primary-50 dark:bg-primary-900/10">
          <span class="font-medium">100GB</span>
        </td>
      </tr>
      
      <tr class="bg-white hover:bg-gray-50 dark:bg-gray-800 dark:hover:bg-gray-700">
        <th scope="row" class="py-4 px-6 font-medium text-gray-900 dark:text-white text-left border-b border-gray-200 dark:border-gray-600">
          高级支持
        </th>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">
          <svg class="w-5 h-5 mx-auto text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
        </td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600">
          <svg class="w-5 h-5 mx-auto text-primary-500 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
        </td>
        <td class="py-4 px-6 text-center border-b border-gray-200 dark:border-gray-600 bg-primary-50 dark:bg-primary-900/10">
          <svg class="w-5 h-5 mx-auto text-primary-500 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>
        </td>
      </tr>
    </tbody>
  </table>
</div>
