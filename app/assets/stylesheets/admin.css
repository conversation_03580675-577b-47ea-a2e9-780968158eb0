/* Administrate Admin Interface Styles */
/* 使用 Flowbite + Tailwind CSS 设计系统 */

/* 修复搜索框样式 - 使用 Flowbite 输入框样式 */
.main-content__header .search {
  @apply flex items-center gap-2;
}

.main-content__header .search__input,
.main-content__header input[name="search"] {
  @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-80 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500;
}

/* 修复搜索按钮样式 - 使用 Flowbite 按钮样式 */
.main-content__header .search__submit,
.main-content__header button[type="submit"] {
  @apply text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800;
}

/* 修复清除按钮样式 - 使用 Flowbite 辅助按钮样式 */
.main-content__header .search__clear-link {
  @apply text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600;
}

/* 修复表格样式 - 使用 Flowbite 表格样式 */
.table {
  @apply w-full text-sm text-left text-gray-500 dark:text-gray-400;
}

.table th {
  @apply px-6 py-3 bg-gray-50 dark:bg-gray-700 text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white;
}

.table tr {
  @apply bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600;
}

/* 修复按钮样式 - 使用 Flowbite 按钮样式 */
.button {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg focus:ring-4 focus:outline-none transition-colors duration-200;
}

.button--primary {
  @apply text-white bg-blue-700 hover:bg-blue-800 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800;
}

.button--secondary {
  @apply text-gray-500 bg-white hover:bg-gray-100 focus:ring-gray-200 border border-gray-200 hover:text-gray-900 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600;
}

.button--danger {
  @apply text-white bg-red-700 hover:bg-red-800 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900;
}

/* 修复导航样式 - 使用 Flowbite 侧边栏样式 */
.navigation {
  @apply bg-gray-50 dark:bg-gray-800;
}

.navigation__link {
  @apply flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700;
}

.navigation__link--active {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white;
}

/* 修复表单样式 - 使用 Flowbite 表单样式 */
.field-unit__label {
  @apply block mb-2 text-sm font-medium text-gray-900 dark:text-white;
}

.field-unit__field input,
.field-unit__field select,
.field-unit__field textarea {
  @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500;
}

/* 修复分页样式 - 使用 Flowbite 分页样式 */
.pagination {
  @apply flex items-center justify-center mt-8;
}

.pagination__link {
  @apply flex items-center justify-center px-3 h-8 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white;
}

.pagination__link--active {
  @apply px-3 h-8 text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white;
}

/* 修复过滤器样式 - 使用 Flowbite 徽章样式 */
.filters {
  @apply bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4 dark:bg-gray-800 dark:border-gray-700;
}

.filters__list {
  @apply flex flex-wrap gap-2;
}

.filters__item {
  @apply bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800;
}

.filters__item--active {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

/* 修复头部样式 - 使用 Flowbite 头部样式 */
.main-content__header {
  @apply flex items-center justify-between mb-8 pb-4 border-b border-gray-200 dark:border-gray-700;
}

.main-content__page-title {
  @apply text-3xl font-bold text-gray-900 dark:text-white;
}

/* 修复 "New Resource" 按钮样式 */
.main-content__header .button,
.main-content__header a[href*="new"] {
  @apply text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800;
}

/* 响应式设计 - 使用 Tailwind 响应式类 */
@media (max-width: 768px) {
  .main-content__header {
    @apply flex-col items-stretch gap-4;
  }

  .main-content__header .search {
    @apply w-full;
  }

  .main-content__header .search__input {
    @apply w-full;
  }

  .table {
    @apply text-xs;
  }

  .table th,
  .table td {
    @apply px-3 py-2;
  }
}
