# 个人公众号 codingirlsclub 配置
default_setting: &default_setting
  appid: <%= Rails.application.credentials.dig(:wechat, :default, :appid) %>
  secret: <%= Rails.application.credentials.dig(:wechat, :default, :secret) %>
  token: <%= Rails.application.credentials.dig(:wechat, :default, :token) %>
  encrypt_mode: true # if true must fill encoding_aes_key
  encoding_aes_key: <%= Rails.application.credentials.dig(:wechat, :default, :encoding_aes_key) %>
  trusted_domain_fullname: "https://codingirlsclub.com/"

production:
  <<: *default_setting
  timeout: 30
  skip_verify_ssl: true

development:
  <<: *default_setting

test:
  <<: *default_setting
  access_token: "/var/tmp/wechat_access_token"
  jsapi_ticket: "/var/tmp/wechat_jsapi_ticket"

# 服务号 HackerStart 配置
service_settings: &service_settings
  appid: <%= Rails.application.credentials.dig(:wechat, :service, :appid) %>
  secret: <%= Rails.application.credentials.dig(:wechat, :service, :secret) %>
  token: <%= Rails.application.credentials.dig(:wechat, :service, :token) %>
  encrypt_mode: true # if true must fill encoding_aes_key
  encoding_aes_key: <%= Rails.application.credentials.dig(:wechat, :service, :encoding_aes_key) %>
  trusted_domain_fullname: "https://codingirlsclub.com/"

service_production:
  <<: *service_settings
  timeout: 30
  skip_verify_ssl: true

service_development:
  <<: *service_settings

service_test:
  <<: *service_settings
  access_token: "/var/tmp/service_wechat_access_token"
  jsapi_ticket: "/var/tmp/service_wechat_jsapi_ticket"

# Mini program codingirlsclub 配置
miniprogram_settings: &miniprogram_settings
  appid: <%= Rails.application.credentials.dig(:wechat, :miniprogram, :app_id) %>
  secret: <%= Rails.application.credentials.dig(:wechat, :miniprogram, :secret) %>
  type: "mp" # 指定为小程序类型

miniprogram_production:
  <<: *miniprogram_settings
  timeout: 30
  skip_verify_ssl: true

miniprogram_development:
  <<: *miniprogram_settings

miniprogram_test:
  <<: *miniprogram_settings
  access_token: "/var/tmp/miniprogram_wechat_access_token"
  jsapi_ticket: "/var/tmp/miniprogram_wechat_jsapi_ticket"
