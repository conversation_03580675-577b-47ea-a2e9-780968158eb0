# Pin npm packages by running ./bin/importmap

pin "application", preload: true
pin "@hotwired/turbo-rails", to: "turbo.min.js", preload: true
pin "@hotwired/stimulus", to: "stimulus.min.js", preload: true
pin "@hotwired/stimulus-loading", to: "stimulus-loading.js", preload: true
pin "@rails/actioncable", to: "actioncable.esm.js"
pin_all_from "app/javascript/controllers", under: "controllers"
pin_all_from "app/javascript/command_providers", under: "command_providers"
pin "flowbite", to: "flowbite.esm.js", preload: true
pin "tailwindcss-animate" # @1.0.7
pin "tailwindcss/plugin", to: "tailwindcss--plugin.js" # @3.4.17
pin "trix"
pin "@rails/actiontext", to: "actiontext.esm.js"
pin "prismjs" # @1.30.0
pin "intl-tel-input" # @25.3.1
pin "intl-tel-input-utils" # @25.3.1
pin "leaflet" # @1.9.4
pin "leaflet.markercluster" # @1.5.3