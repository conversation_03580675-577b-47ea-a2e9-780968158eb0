<section class="bg-white dark:bg-gray-900">
  <div class="px-4 py-8 mx-auto max-w-4xl lg:py-16">
    <h1 class="mb-4 text-3xl font-bold text-gray-900 dark:text-white"><%= t('knowledge_bases.manage_teams.title') %> <%= @knowledge_base.name %></h1>
    <p class="mb-8 text-gray-500 dark:text-gray-400"><%= t('knowledge_bases.manage_teams.subtitle') %></p>

    <%= form_with url: add_team_knowledge_base_path(@knowledge_base), class: "space-y-6" do |f| %>
      <div>
        <%= f.label :team_id, t('knowledge_bases.manage_teams.add_team'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
        <%= f.collection_select :team_id,
            @teams,
            :id,
            :name,
            { prompt: "Select a team" },
            class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" %>
      </div>

      <div class="flex items-center space-x-4">
        <%= f.submit t('knowledge_bases.manage_teams.add_button'),
            class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>
      </div>
    <% end %>

    <div class="mt-8 space-y-6">
      <h2 class="text-xl font-bold text-gray-900 dark:text-white"><%= t('knowledge_bases.manage_teams.associated_teams') %></h2>
      <div class="space-y-3">
        <% @associated_teams.each do |team| %>
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg dark:bg-gray-800">
            <span class="text-gray-900 dark:text-white font-medium"><%= team.name %></span>
            <%= button_to t('knowledge_bases.manage_teams.remove_team'),
                remove_team_knowledge_base_path(@knowledge_base, team_id: team.id),
                method: :delete,
                class: "text-red-500 hover:text-red-600 border border-red-500 hover:border-red-600 focus:ring-4 focus:outline-hidden focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-red-600 dark:hover:border-red-600 dark:focus:ring-red-800",
                form: {
                  data: { turbo_confirm: t('knowledge_bases.manage_teams.remove_confirm') },
                  class: "inline-block"
                } %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</section>
