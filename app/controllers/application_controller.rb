class ApplicationController < ActionController::Base
  # 语言切换
  around_action :switch_locale

  # 错误处理必须在最前面
  rescue_from ActionPolicy::Unauthorized do |exception|
    flash[:error] = "You are not authorized"
    redirect_to root_path
  end

  include Authentication
  include ActionPolicy::Controller

  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern



  # 确保每个动作都执行了授权检查
  verify_authorized

  # 跳过特定控制器的授权检查
  skip_verify_authorized do
    case controller_name
    when "sessions"
      [ "new", "create" ].include?(action_name)
    when "static_pages"
      true
    else
      false
    end
  end

  layout :set_layout

  private

  # 语言切换逻辑
  def switch_locale(&action)
    locale = extract_locale || I18n.default_locale
    response.headers["Cache-Control"] = "no-cache, no-store"
    response.headers["Turbo-Visit-Control"] = "reload"
    I18n.with_locale(locale, &action)
  end

  # 从请求中提取语言参数 (优先级: session > 用户偏好 > 浏览器 > 默认)
  def extract_locale
    session[:locale] ||                             # 1. Session 中存储的语言 (最高优先级)
    current_user&.preferred_language ||              # 2. 用户偏好设置 (数据库)
    extract_locale_from_accept_language_header ||    # 3. 浏览器语言
    I18n.default_locale                             # 4. 默认语言
  end

  # 从浏览器 Accept-Language 头解析语言
  def extract_locale_from_accept_language_header
    request.env["HTTP_ACCEPT_LANGUAGE"]&.scan(/^[a-z]{2}/)&.first
  end

  def set_layout
    if authenticated?
      "user_dashboard"
    else
      "application"
    end
  end

  def current_user
    Current.session&.user
  end

  helper_method :current_user

  # Provide authorization context for ActionPolicy
  def authorization_context
    { user: current_user }
  end
end
