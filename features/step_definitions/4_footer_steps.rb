# 重用已有的步骤
Given('I visit any page') do
  visit root_path
end

# 查看页脚内容的步骤
Then('I should see {string} in the footer') do |content|
  within('footer') do
    expect(page).to have_content(content)
  end
end

# 点击页脚链接的步骤
When('I click {string} in the footer') do |link_text|
  within('footer') do
    click_link link_text
  end
end

# 重用已有的重定向步骤
Then('I should be redirected to the about page') do
  expect(page).to have_current_path(about_path)
end

Then('I should be redirected to the privacy page') do
  expect(page).to have_current_path(privacy_policy_path)
end

Then('I should be redirected to the terms page') do
  expect(page).to have_current_path(terms_path)
end
