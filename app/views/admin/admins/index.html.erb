<%#
# Index

This view is the template for the index page.
It is responsible for rendering the search bar, header and pagination.
It renders the `_table` partial to display details about the resources.

## Local variables:

- `page`:
  An instance of [Administrate::Page::Collection][1].
  Contains helper methods to help display a table,
  and knows which attributes should be displayed in the resource's table.
- `resources`:
  An instance of `ActiveRecord::Relation` containing the resources
  that match the user's search criteria.
  By default, these resources are passed to the table partial to be displayed.
- `search_term`:
  A string containing the term the user has searched for, if any.
- `show_search_bar`:
  A boolean that determines if the search bar should be shown.

[1]: http://www.rubydoc.info/gems/administrate/Administrate/Page/Collection
%>

<%=
  render("index_header",
    resources: resources,
    search_term: search_term,
    page: page,
    show_search_bar: show_search_bar,
  )
%>

<%= content_for(:before_main) %>

<% if content_for?(:main) %>
  <%= content_for(:main) %>
<% else %>
  <section class="main-content__body main-content__body--flush">
    <%= render(
      "collection",
      collection_presenter: page,
      collection_field_name: resource_name,
      page: page,
      resources: resources,
      table_title: "page-title"
    ) %>

    <%= render("pagination", resources: resources) %>
  </section>
<% end %>

<%= content_for(:after_main) %>
