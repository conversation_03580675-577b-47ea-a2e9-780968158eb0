require 'rails_helper'

RSpec.describe KnowledgeBase, type: :model do
  let(:owner) { create(:user) }
  let(:team) { create(:team) }
  subject { build(:knowledge_base, owner: owner) }

  # 基本验证测试
  describe "validations" do
    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_least(2).is_at_most(100) }
    it { should validate_presence_of(:visibility) }
    it { should validate_length_of(:description).is_at_most(5000) }

    context "uniqueness validation" do
      before { create(:knowledge_base, owner: owner, name: "Unique Name") }

      it "validates uniqueness scoped to owner and deleted_at" do
        kb = build(:knowledge_base, owner: owner, name: "Unique Name")
        expect(kb).not_to be_valid
        expect(kb.errors[:name]).to include(I18n.t('errors.messages.taken'))
      end
    end
  end

  # 关联关系测试
  describe "associations" do
    it { should belong_to(:owner).class_name("User") }
    it { should have_many(:knowledge_base_posts).dependent(:destroy) }
    it { should have_many(:posts).through(:knowledge_base_posts) }
    it { should have_many(:team_resource_accesses).dependent(:destroy) }
    it { should have_many(:teams).through(:team_members) }
  end

  # 作用域测试
  describe "scopes" do
    let!(:kb1) { create(:knowledge_base, name: 'Rails Best Practices', created_at: 1.day.ago) }
    let!(:kb2) { create(:knowledge_base, name: 'Testing Strategies', created_at: Time.current) }

    describe ".recent" do
      it "orders by created_at desc" do
        expect(described_class.recent).to eq [ kb2, kb1 ]
      end
    end

    describe ".search_by_name_or_description" do
      context "when searching by name" do
        it "finds matching records" do
          result = described_class.search_by_name_or_description("Best Practice")
          expect(result).to contain_exactly(kb1)
        end
      end

      context "when searching by description" do
        let!(:kb3) { create(:knowledge_base, description: 'This is about testing best practices') }

        it "finds matching records" do
          result = described_class.search_by_name_or_description("testing best")
          expect(result).to contain_exactly(kb3)
        end
      end
    end
  end

  # 自定义方法测试
  describe "#transfer_ownership" do
    let(:new_owner) { create(:user) }

    before { subject.save! }  # 确保对象已持久化

    context "with valid parameters" do
      it "updates owner within transaction" do
        expect {
          subject.transfer_ownership(new_owner)
        }.to change { subject.reload.owner }.to(new_owner)
      end
    end

    context "when update fails" do
      it "rolls back transaction" do
        # 使用更真实的错误模拟
        original_owner = subject.owner
        allow(subject).to receive(:update!).and_raise(ActiveRecord::RecordInvalid)

        expect {
          subject.transfer_ownership(new_owner) rescue nil
        }.not_to change { subject.reload.owner }

        expect(subject.owner).to eq(original_owner)
      end
    end
  end

  # 状态管理测试
  describe "visibility states" do
    context "when changing to team visibility" do
      it "requires team associations" do
        subject.save!  # 先保存对象使其持久化
        subject.visibility = :team_visibility  # 再设置 visibility
        subject.valid?  # 进行验证
        # 验证模型添加的实际错误消息
        expect(subject.errors[:team_resource_accesses]).to include("must have at least one team access when changing visibility to team")
      end
    end
  end

  # 业务规则测试
  describe "post limits" do
    before do
      subject.save!
      create_list(:knowledge_base_post, KnowledgeBase::MAX_POSTS_LIMIT, knowledge_base: subject)
    end

    it "prevents exceeding post limit" do
      new_post = build(:knowledge_base_post, knowledge_base: subject)
      expect(new_post).not_to be_valid
      expect(new_post.errors[:base]).to include("Maximum number of posts reached")
    end
  end

  # 缓存测试
  describe "caching behavior" do
    before { subject.save! }

    context "when visibility changes" do
      it "clears associated caches" do
        base_key = [ subject.cache_key_with_version ]
        expect(Rails.cache).to receive(:delete).with(base_key + [ "associations" ])
        subject.update!(visibility: :private_visibility)
      end
    end
  end

  # 软删除测试
  describe "soft deletion" do
    before { subject.save! }

    it "sets deleted_at timestamp" do
      expect {
        subject.destroy
      }.to change { subject.reload.deleted_at }.from(nil)
    end

    it "restores correctly" do
      subject.destroy
      expect {
        subject.restore
        subject.reload
      }.to change { subject.deleted_at }.from(be_present).to(nil)
    end
  end

  # 事务安全测试
  describe "bulk operations" do
    let!(:kb_list) { create_list(:knowledge_base, 3) }

    describe ".bulk_archive" do
      it "updates all records in transaction" do
        expect {
          described_class.bulk_archive(kb_list.map(&:id))
        }.to change { described_class.archived.count }.by(3)
      end
    end
  end

  # 序列化测试
  describe "metadata serialization" do
    it "stores hash structure correctly" do
      data = { "version" => 1.2, "category" => "tech" }
      subject.metadata = data
      subject.save!
      expect(subject.reload.metadata).to eq data
    end
  end

  # 回调测试（示例，需根据实际实现）
  describe "callbacks" do
    context "after create" do
      it "triggers cache clearing" do
        expect(Rails.cache).to receive(:delete).at_least(:once)
        subject.save!
      end
    end
  end
end
