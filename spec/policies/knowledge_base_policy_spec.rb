require 'rails_helper'

RSpec.describe KnowledgeBasePolicy, type: :policy do
  let(:user) { create(:user) }
  let(:owner) { create(:user) }
  let(:team) { create(:team) }
  let(:knowledge_base) { create(:knowledge_base, owner: owner) }

  let(:policy) { described_class.new(knowledge_base, user: user) }

  # 角色上下文定义
  shared_context 'viewer role' do
    let!(:access) do
      create(:team_resource_access,
             resource: knowledge_base,
             team_member: create(:team_member, user: user),
             role: :viewer)
    end
  end

  shared_context 'editor role' do
    let!(:access) do
      create(:team_resource_access,
             resource: knowledge_base,
             team_member: create(:team_member, user: user),
             role: :editor)
    end
  end

  shared_context 'maintainer role' do
    let!(:access) do
      create(:team_resource_access,
             resource: knowledge_base,
             team_member: create(:team_member, user: user),
             role: :maintainer)
    end
  end

  # 权限验证共享示例
  shared_examples_for 'grants access' do |actions|
    Array(actions).each do |action|
      it "allows #{action}" do
        expect(policy).to be_allowed_to(action)
      end
    end
  end

  shared_examples_for 'denies access' do |actions|
    Array(actions).each do |action|
      it "denies #{action}" do
        expect(policy).not_to be_allowed_to(action)
      end
    end
  end

  describe '基础权限' do
    let(:policy) { described_class.new(nil, user: user) }

    describe '#index?' do
      context '已登录用户' do
        it { expect(policy).to be_allowed_to(:index) }
      end
    end

    describe '#create?' do
      context '已登录用户' do
        it { expect(policy).to be_allowed_to(:create) }
      end
    end
  end

  describe '可见性控制' do
    let(:policy) { described_class.new(knowledge_base, user: user) }

    context '公开知识库' do
      let(:knowledge_base) { create(:knowledge_base, :public_visibility) }
      it_behaves_like 'grants access', :show
    end

    context '私有知识库' do
      let(:knowledge_base) { create(:knowledge_base, :private_visibility, owner: owner) }

      context '非所有者' do
        it_behaves_like 'denies access', :show
      end
    end

    context '团队知识库' do
      let(:knowledge_base) { create(:knowledge_base, :team_visibility) }

      context '团队成员' do
        include_context 'viewer role'
        it_behaves_like 'grants access', :show
      end

      context '非团队成员' do
        it_behaves_like 'denies access', :show
      end
    end
  end

  describe '操作权限' do
    let(:policy) { described_class.new(knowledge_base, user: user) }

    context '维护者' do
      include_context 'maintainer role'
      it_behaves_like 'grants access', %i[update configure_team]
      it_behaves_like 'denies access', :destroy
    end

    context '编辑者' do
      include_context 'editor role'
      it_behaves_like 'grants access', :update
      it_behaves_like 'denies access', :destroy
    end

    context '查看者' do
      include_context 'viewer role'
      it_behaves_like 'denies access', %i[update destroy]
    end
  end

  describe '作用域过滤' do
    let(:policy) { described_class.new(nil, user: user) }
    let!(:public_kb) { create(:knowledge_base, :public_visibility) }
    let!(:private_kb) { create(:knowledge_base, :private_visibility, owner: owner) }
    let!(:team_kb) do
      create(:knowledge_base, :team_visibility).tap do |kb|
        team_member = create(:team_member, user: user) if user.present?
        create(:team_resource_access, resource: kb, team_member: team_member) if user.present?
      end
    end

    subject { policy.apply_scope(KnowledgeBase.all, type: :active_record_relation) }

    context '已登录用户' do
      it '可以看到公开的知识库' do
        expect(subject).to include(public_kb)
      end

      context '作为拥有者' do
        let(:owner) { user }
        it '可以看到自己的私有知识库' do
          expect(subject).to include(private_kb)
        end
      end

      context '作为团队成员' do
        it '可以看到有权限的团队知识库' do
          expect(subject).to include(team_kb)
        end
      end

      context '不是拥有者也不是团队成员' do
        let(:owner) { create(:user) }
        it '看不到私有知识库' do
          expect(subject).not_to include(private_kb)
        end
      end
    end
  end

  describe '删除限制' do
    let(:policy) { described_class.new(knowledge_base, user: owner) }

    context '存在团队关联' do
      before do
        team_member = create(:team_member, user: owner)
        create(:team_resource_access,
               resource: knowledge_base,
               team_member: team_member,
               role: :maintainer)
        knowledge_base.team_visibility!
      end
      it { expect(policy).not_to be_allowed_to(:destroy) }
    end

    context '已归档' do
      before do
        knowledge_base.update!(archived: true)
      end
      it { expect(policy).not_to be_allowed_to(:destroy) }
    end
  end
end
