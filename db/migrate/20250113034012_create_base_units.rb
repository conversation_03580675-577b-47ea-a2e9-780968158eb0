class CreateBaseUnits < ActiveRecord::Migration[8.0]
  def change
    create_table :base_units do |t|
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false
      t.string :content_type
      t.integer :file_size, null: false, limit: 8  # 使用 limit: 8 来模拟 bigint
      t.string :checksum
      t.string :status, default: 'processing'
      t.datetime :processed_at
      t.json :metadata, default: {}

      t.timestamps
    end

    add_index :base_units, :status
    add_index :base_units, :content_type
    add_index :base_units, [ :user_id, :name ], unique: true
  end
end
