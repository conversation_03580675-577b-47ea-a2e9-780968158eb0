require "rails_helper"

RSpec.describe BaseUnitPolicy, type: :policy do
  let(:owner) { create(:user) }
  let(:other_user) { create(:user) }
  let(:base_unit) { create(:base_unit, user: owner) }

  describe "owner? private method" do
    context "when user is the owner" do
      it "returns true" do
        policy = described_class.new(base_unit, user: owner)
        expect(policy.send(:owner?)).to be true
      end
    end

    context "when user is not the owner" do
      it "returns false" do
        policy = described_class.new(base_unit, user: other_user)
        expect(policy.send(:owner?)).to be false
      end
    end

    context "when user is nil" do
      it "returns false" do
        policy = described_class.new(base_unit, user: nil)
        expect(policy.send(:owner?)).to be false
      end
    end
  end
end
