<div class="flex flex-col h-full p-6 mx-auto w-full text-center text-gray-900 bg-white rounded-lg border border-gray-100 shadow-md hover:shadow-lg transition-shadow duration-300 dark:border-gray-600 xl:p-8 dark:bg-gray-800 dark:text-white">
  <!-- 标题和描述 -->
  <h3 class="mb-2 text-2xl font-bold"><%= plan[:name] %></h3>
  <p class="mb-8 font-light text-gray-500 sm:text-lg dark:text-gray-400"><%= plan[:description] %></p>

  <!-- 价格区域 -->
  <div class="flex justify-center items-baseline my-8">
    <span class="mr-2 text-5xl font-extrabold text-primary-600 dark:text-primary-500">$<%= plan[:price].to_i %></span>
    <span class="text-gray-500 dark:text-gray-400">/<%= plan[:plan_type] == "free" ? "" : plan[:plan_type] %></span>
  </div>

  <!-- 功能列表 -->
  <ul role="list" class="mb-8 space-y-4 text-left">
    <% plan[:features].each do |feature| %>
      <li class="flex items-center space-x-3">
        <!-- Icon: Check -->
        <svg class="shrink-0 w-5 h-5 text-primary-500 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span>
          <% if feature.is_a?(Array) %>
            <%= feature[0] %> <span class="font-semibold"><%= feature[1] %></span>
          <% else %>
            <%= feature %>
          <% end %>
        </span>
      </li>
    <% end %>
  </ul>

  <!-- 订阅按钮容器 -->
  <div class="mt-auto pt-4">
    <%= render partial: "registrations/subscribe_button", locals: { plan: plan } %>
  </div>
</div>
