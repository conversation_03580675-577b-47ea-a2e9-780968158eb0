class UsersController < ApplicationController
  # 用户偏好设置和语言切换统一入口
  # TODO 修复 theme 更新逻辑：
  # - navbar 中的 theme 切换需要更新数据库中的 preferences theme
  # - settings 中的 theme 切换需要更新 localStorage 中的 theme 或者 session 中的 theme
  def update_preferences
    authorize! current_user
    # 合并新参数到原 preferences，防止未提交字段被覆盖
    # 使用 deep_merge! 来安全地合并嵌套的 JSONB 字段，避免覆盖整个哈希
    new_prefs = (current_user.preferences || {}).deep_merge(preferences_params.to_h)
    if current_user.update(preferences: new_prefs)
      session[:locale] = new_prefs["language"] if new_prefs["language"].present?
      # PATCH 请求（如侧边栏语言切换）和设置页表单都统一重定向回来源页
      redirect_to request.referer.presence || settings_users_path, allow_other_host: false
    else
      # 仅设置页需要提示，侧边栏语言切换无需 flash
      flash.now[:alert] = t("users.settings.preferences_update_failed")
      render :settings, status: :unprocessable_entity
    end
  end

  def settings
    authorize! current_user
    render :settings
  end

  def update_profile
    authorize! current_user
    # Split hobbies string into an array, handling potential nil or empty string
    profile_params_with_hobbies = profile_params.merge(
      hobbies: profile_params[:hobbies]&.split(",")&.map(&:strip)&.reject(&:blank?) || []
    )

    if current_user.update(profile_params_with_hobbies)
      flash[:notice] = t("users.settings.profile_updated_successfully")
      redirect_to settings_users_path
    else
      # Re-render settings page with errors
      flash.now[:alert] = t("users.settings.profile_update_failed")
      render :settings, status: :unprocessable_entity
    end
  end

  def update_password
    authorize! current_user
    # Check if current password is correct
    unless current_user.authenticate(password_params[:current_password])
      flash.now[:alert] = t("users.settings.password.invalid_current_password")
      render :settings, status: :unprocessable_entity
      return
    end

    # Check if new password and confirmation match
    if password_params[:password] != password_params[:password_confirmation]
      flash.now[:alert] = t("users.settings.password.password_mismatch")
      # Manually add error to the object for display in form if using form builders that show errors
      current_user.errors.add(:password_confirmation, :confirmation, message: t("errors.messages.confirmation", attribute: User.human_attribute_name(:password)))
      render :settings, status: :unprocessable_entity
      return
    end

    # Attempt to update the password
    if current_user.update(password: password_params[:password], password_confirmation: password_params[:password_confirmation])
      flash[:notice] = t("users.settings.password.password_updated_successfully")
      # Optionally sign out other sessions
      # current_user.sessions.where.not(id: current_session.id).destroy_all
      redirect_to settings_users_path
    else
      # Re-render settings page with validation errors from the model (e.g., password too short)
      flash.now[:alert] = t("users.settings.password.password_update_failed")
      render :settings, status: :unprocessable_entity
    end
  end

  private

  def preferences_params
    # 定义所有可配置的通知类型和渠道
    notification_types = [
      :ai_message,
      :event_registration,
      :event_reminder,
      :system,
      :knowledge_base
    ]
    notification_channels = [ :web, :email, :service_wechat ]

    # 构建允许的通知参数的嵌套哈希结构
    # 这允许全局开关 (例如, notification[web])
    # 和按类型的开关 (例如, notification[ai_message][web])
    permitted_notification_params = notification_channels + notification_types.map { |type| { type => notification_channels } }

    params.require(:preferences).permit(
      :language, :theme, :timezone, :currency,
      notification: permitted_notification_params
    )
  end

  def profile_params
    # Permit only the attributes that should be updatable via this form
    # Exclude username and email_address as they are marked readonly in the form
    params.require(:user).permit(:phone_number, :gender, :location, :hobbies)
  end

  def password_params
    params.permit(:current_password, :password, :password_confirmation)
  end
end
