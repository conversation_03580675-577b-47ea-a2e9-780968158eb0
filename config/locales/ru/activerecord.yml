# Перевод на русский язык моделей и атрибутов ActiveRecord
ru:
  activerecord:
    models:
      user: "Пользователь"
      team: "Команда"
      team_member: "Участник команды"
      knowledge_base: "База знаний"
      post: "Пост"
      assistant: "Ассистент"
      conversation: "Разговор"
      message: "Сообщение"
      base_unit: "Базовая единица"
      subscription: "Подписка"
    attributes:
      user:
        name: "Имя"
        email: "Электронная почта"
        password: "Пароль"
        bio: "О себе"
        preferences: "Предпочтения"
      team:
        name: "Название"
        description: "Описание"
    errors:
      messages:
        blank: "не может быть пустым"
        taken: "уже существует"
        invalid: "имеет неверное значение"
        too_short:
          one: "слишком короткое (минимум %{count} символ)"
          few: "слишком короткое (минимум %{count} символа)"
          many: "слишком короткое (минимум %{count} символов)"
          other: "слишком короткое (минимум %{count} символа)"
        too_long:
          one: "слишком длинное (максимум %{count} символ)"
          few: "слишком длинное (максимум %{count} символа)"
          many: "слишком длинное (максимум %{count} символов)"
          other: "слишком длинное (максимум %{count} символа)"
        confirmation: "не совпадает с подтверждением"
