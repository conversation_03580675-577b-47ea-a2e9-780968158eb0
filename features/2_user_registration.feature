Feature: User Registration
  As a potential user
  I want to register a new account
  So that I can use the system's features

  Background:
    Given I visit the registration page
  # Step 1: 手机号验证

  Scenario: Step 1 - Mobile verification
    When I enter the mobile number "***********"
    And I click the "Send verification code" button
    Then I should see the prompt "Verification code has been sent"
    When I enter the verification code "123456"
    And I click the "Verify" button
    Then I should see the prompt "Verification successful"
    And I should proceed to step 2
  # Step 2: 基本信息

  Scenario: Step 2 - Basic information
    Given I have completed mobile verification
    When I enter the following registration information:
      | Username         | testuser         |
      | Email            | <EMAIL> |
      | Password         | password123      |
      | Confirm Password | password123      |
    And I check "I agree to the Terms of Service"
    And I click the "Create Account and Next" button
    Then I should proceed to step 3
  # Step 3: 订阅选择

  Scenario: Step 3 - Select subscription plan and complete
    Given I have completed basic information
    When I see the subscription plans:
      | Plan Name            | Price | Features                       |
      | Weekly Subscription  | Free  | Basic features, Weekly updates |
      | Monthly Subscription | Free  | All features, Priority support |
    And I select "Weekly Subscription" plan
    And I click the "Complete Registration" button
    Then I should see the prompt "Registration successful"
    And I should be redirected to the homepage
  # 跳过订阅

  Scenario: Complete registration without subscription
    Given I have completed basic information
    When I select "Skip subscription"
    And I click the "Complete Registration" button
    Then I should see the prompt "Registration successful"
    And I should be redirected to the homepage
  # 错误场景

  Scenario: Mobile verification failed
    When I enter the mobile number "***********"
    And I click the "Send verification code" button
    And I enter the wrong verification code "000000"
    And I click the "Verify" button
    Then I should see the prompt "Verification code error"
    And I should stay on step 1

  Scenario: Invalid basic information
    Given I have completed mobile verification
    When I enter the following registration information:
      | Username         | t             |
      | Email            | invalid-email |
      | Password         | short         |
      | Confirm Password | different     |
    And I click the "Next" button
    Then I should see the following error messages:
      | Username must be at least 3 characters |
      | Incorrect email format                 |
      | Password too short                     |
      | Password confirmation does not match   |
    And I should stay on step 2
