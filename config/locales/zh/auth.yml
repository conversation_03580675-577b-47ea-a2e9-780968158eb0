# 认证和用户管理中文翻译
zh:
  auth:
    sign_in: "登录"
    sign_up: "注册"
    sign_out: "退出登录"
    sessions:
      title: "登录"
      email: "邮箱"
      password: "密码"
      remember_me: "记住我"
      forgot_password: "忘记密码?"
      new_account: "还没有账号?"
    registrations:
      title: "创建账号"
      username: "用户名"
      email: "邮箱"
      password: "密码"
      password_confirmation: "确认密码"
      account_exists: "已有账号?"
      terms_agreement: "注册即表示您同意我们的服务条款和隐私政策"
      steps:
        verification:
          title: "验证身份"
          phone_label: "手机号码"
          phone_placeholder: "请输入手机号"
          code_label: "验证码"
          code_placeholder: "请输入验证码"
          send_code: "获取验证码"
          next: "下一步"
          have_account: "是否已有账号?"
        progress:
          verify_identity: "验证身份"
          create: "创建"
          account: "账号"
          send_code: "发送验证码"
          create_account: "创建账号"
          subscribe: "订阅"
          step: "第%{number}步"
      pricing:
        get_started: "立即开始"
      placeholders:
        username: "输入用户名(仅限字母、数字和下划线)"
        email: "<EMAIL>"
        password: "••••••••"
    passwords:
      forgot:
        title: "忘记密码?"
        description: "别担心!输入您的邮箱，我们将发送密码重置链接!"
        email_label: "您的邮箱"
        email_placeholder: "<EMAIL>"
        submit: "重置密码"
      reset:
        title: "重置密码"
        new_password_label: "新密码"
        confirm_password_label: "确认新密码"
        password_placeholder: "••••••••"
        submit: "重置密码"
      terms:
        accept: "我接受"
        link: "条款和条件"
      mailer:
        reset:
          html:
            message: "您可以在15分钟内通过"
            link_text: "此密码重置页面"
          text:
            message: "您可以在15分钟内通过此密码重置页面重置密码:"
    verification:
      code_sent: "验证码已发送"
      enter_code: "输入验证码"
      resend_code: "重新发送验证码"
      verify_button: "验证"
    omniauth:
      mismatch: "这个 %{provider} 账号已被其他用户关联，请先退出登录！"
      signed_in: "欢迎回来！已通过 %{provider} 成功登录。"
      signed_up_and_in: "欢迎加入！您的账号已创建并关联了 %{provider}。"
      linked: "您的 %{provider} 账号已成功关联！"
      already_linked: "这个 %{provider} 账号已经关联到您的账户了。"
      creation_failed: "暂时无法使用 %{provider} 创建账号，请重试或手动注册。"
      failure: "%{provider} 认证失败，请重试。"
      or_continue_with: "或通过以下方式继续"
