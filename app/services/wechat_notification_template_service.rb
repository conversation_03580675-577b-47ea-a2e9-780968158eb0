# frozen_string_literal: true

# 微信通知模板服务类
# 用于管理微信通知模板的配置和数据构建
class WechatNotificationTemplateService
  def self.get_template_config(template_key)
    templates = Rails.application.config_for(:wechat_notification_templates)
    templates[template_key.to_s]
  end

  def self.build_event_registration_data(event)
    {
      thing30: { value: event.title, color: "#173177" }, # 讲座名称
      time33: { value: event.start_time.strftime("%Y年%m月%d日"), color: "#173177" }, # 讲座时间
      thing14: { value: event.location&.name || event.address || "线上活动", color: "#173177" }, # 上课地址
      amount49: { value: format_price(event.price), color: "#173177" }, # 金额
      character_string11: { value: event_url(event), color: "#173177" } # 课程链接
    }
  end

  def self.build_event_reminder_data(event, reminder_type)
    {
      first: { value: "活动提醒", color: "#173177" },
      keyword1: { value: event.title, color: "#173177" },
      keyword2: { value: event.start_time.strftime("%Y-%m-%d %H:%M"), color: "#173177" },
      keyword3: { value: reminder_type_text(reminder_type), color: "#173177" },
      remark: { value: "点击查看详情", color: "#173177" }
    }
  end

  def self.build_system_notification_data(message)
    {
      first: { value: message, color: "#173177" },
      keyword1: { value: "系统通知", color: "#173177" },
      keyword2: { value: Time.current.strftime("%Y-%m-%d %H:%M"), color: "#173177" },
      remark: { value: "点击查看详情", color: "#173177" }
    }
  end

  private

  def self.format_price(price)
    if price.present? && price > 0
      "#{price}元"
    else
      "免费"
    end
  end

  def self.event_url(event)
    Rails.application.routes.url_helpers.event_url(
      event,
      host: Rails.application.routes.default_url_options[:host] || "localhost"
    )
  end

  def self.reminder_type_text(reminder_type)
    case reminder_type
    when "day_before"
      "明天开始"
    when "hour_before"
      "1小时后开始"
    when "starting_soon"
      "即将开始"
    else
      "活动提醒"
    end
  end
end
