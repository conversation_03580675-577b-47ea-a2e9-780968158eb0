import { Controller } from "@hotwired/stimulus"

/**
 * 通用文件上传控制器
 * @class UploadController
 * @extends Controller
 */
export default class extends Controller {
  static targets = ["fileInput", "progress"]
  static values = { 
    url: String,
    formData: { type: Object, default: {} }
  }

  /**
   * 触发文件选择
   */
  triggerFileInput() {
    this.fileInputTarget.click()
  }

  /**
   * 处理文件选择
   * @param {Event} event - 文件选择事件
   */
  handleFileSelect(event) {
    const files = event.target.files
    if (!files.length) return

    Array.from(files).forEach(file => {
      this.uploadFile(file)
    })
  }

  /**
   * 上传单个文件
   * @param {File} file - 要上传的文件
   */
  async uploadFile(file) {
    const formData = new FormData()
    // 添加基础文件字段
    formData.append('file', file)
    // 添加额外表单数据
    Object.entries(this.formDataValue).forEach(([key, value]) => {
      formData.append(key, value)
    })

    try {
      const response = await fetch(this.urlValue, {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })

      const result = await response.json()

      if (result.success) {
        this.dispatch('success', { detail: { file, response: result } })
        window.location.reload()
        //TODO: 页面flash
        //this.showFlash('success', 'Upload successful')
      } else {
        this.dispatch('error', { detail: { file, errors: result.errors } })
        console.error('Upload failed:', result.errors)
      }
    } catch (error) {
      this.dispatch('error', { detail: { file, error } })
      console.error('Upload error:', error)
    }
  }

  showFlash(type, message) {
    const event = new CustomEvent('show-flash', {
      bubbles: true,
      detail: { type, message }
    })
    document.dispatchEvent(event)
  }
} 