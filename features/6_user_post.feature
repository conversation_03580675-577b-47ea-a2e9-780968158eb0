Feature: User Post Management
  As a registered user
  I want to create and manage my posts
  So that I can share my content with others

  Background:
    Given I am a registered user
    And I am logged in

  Scenario: Create a new draft post
    When I visit the new post page
    And I fill in the post title with "My Posts Title"
    And I fill in the post content with "This is a draft post"
    # And I select "draft" from "Status"
    And I click the "Submit" button in posts
    Then I should see "Post created"
    And I should see "My Posts Title"
    And I should see "Status: Draft"

  Scenario: Publish a draft post
    Given I have a draft post titled "Draft Post"
    When I visit the post page
    And I click the "Publish" link in posts
    Then I should see "Post published"
    And I should see "Status: Have released"
    And I should see "Publish in:"

  Scenario: Edit an existing post
    Given I have a post titled "Original Title"
    When I visit the post page
    And I click the "Edit" link in posts
    And I fill in the post title with "Updated Title"
    And I click the "Submit" button in posts
    Then I should see "Post has been updated"
    And I should see "Updated Title"

  Scenario: Delete a post
    Given I have a post titled "Post to Delete"
    When I visit the posts page
    And I click the "Delete" link in posts
    Then I should see "Post deleted"
    And I should not see "Post to Delete"

  Scenario: View my posts list
    Given I have the following posts:
      | title       | status    |
      | First Post  | published |
      | Second Post | draft     |
      | Third Post  | published |
    When I visit the posts page
    Then I should see "First Post"
    And I should see "Second Post"
    And I should see "Third Post"
