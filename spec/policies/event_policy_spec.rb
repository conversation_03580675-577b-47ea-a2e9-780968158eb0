require 'rails_helper'

RSpec.describe EventPolicy, type: :policy do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:admin_user) { create(:user, admin: true) }
  let(:event) { create(:event, user: user) }
  let(:other_event) { create(:event, user: other_user) }

  describe "permissions" do
    context "when user is the event owner" do
      let(:policy) { described_class.new(event, user: user) }

      it "allows update" do
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows edit" do
        expect(policy).to be_allowed_to(:edit?)
      end

      it "allows destroy" do
        expect(policy).to be_allowed_to(:destroy?)
      end
    end

    context "when user is not the event owner" do
      let(:policy) { described_class.new(event, user: other_user) }

      it "denies update" do
        expect(policy).not_to be_allowed_to(:update?)
      end

      it "denies edit" do
        expect(policy).not_to be_allowed_to(:edit?)
      end

      it "denies destroy" do
        expect(policy).not_to be_allowed_to(:destroy?)
      end
    end

    context "when user is nil" do
      let(:policy) { described_class.new(event, user: nil) }

      it "denies update" do
        expect(policy).not_to be_allowed_to(:update?)
      end

      it "denies edit" do
        expect(policy).not_to be_allowed_to(:edit?)
      end
    end
  end

  describe "show permissions" do
    context "for public events" do
      let(:public_event) { create(:event, visibility: :public_visibility) }

      it "allows anyone to view" do
        expect(described_class.new(public_event, user: nil)).to be_allowed_to(:show?)
        expect(described_class.new(public_event, user: user)).to be_allowed_to(:show?)
        expect(described_class.new(public_event, user: other_user)).to be_allowed_to(:show?)
      end
    end

    context "for private events" do
      let(:private_event) { create(:event, user: user, visibility: :private_visibility) }

      it "allows owner to view" do
        expect(described_class.new(private_event, user: user)).to be_allowed_to(:show?)
      end

      it "denies non-owner to view" do
        expect(described_class.new(private_event, user: other_user)).not_to be_allowed_to(:show?)
        expect(described_class.new(private_event, user: nil)).not_to be_allowed_to(:show?)
      end
    end
  end

  describe "register permissions" do
    let(:public_event) { create(:event, :published, :upcoming, visibility: :public_visibility, max_participants: 10) }
    let(:test_user) { create(:user) }

    context "valid registration conditions" do
      it "allows user to register for public upcoming events" do
        expect(described_class.new(public_event, user: test_user)).to be_allowed_to(:register?)
      end
    end

    context "invalid registration conditions" do
      it "denies unauthenticated users" do
        expect(described_class.new(public_event, user: nil)).not_to be_allowed_to(:register?)
      end

      it "denies registration for full events" do
        create_list(:event_registration, 10, event: public_event)
        expect(described_class.new(public_event, user: test_user)).not_to be_allowed_to(:register?)
      end

      it "denies registration for cancelled events" do
        cancelled_event = create(:event, :cancelled, visibility: :public_visibility)
        expect(described_class.new(cancelled_event, user: test_user)).not_to be_allowed_to(:register?)
      end

      it "denies registration for past events" do
        past_event = create(:event, :past, visibility: :public_visibility)
        expect(described_class.new(past_event, user: test_user)).not_to be_allowed_to(:register?)
      end

      it "denies registration when user is already registered" do
        create(:event_registration, event: public_event, user: test_user)
        expect(described_class.new(public_event, user: test_user)).not_to be_allowed_to(:register?)
      end
    end
  end

  describe "accessible permissions" do
    context "for public events" do
      let(:public_event) { create(:event, visibility: :public_visibility) }

      it "allows anyone to access public events" do
        expect(described_class.new(public_event, user: nil)).to be_allowed_to(:accessible?)
        expect(described_class.new(public_event, user: user)).to be_allowed_to(:accessible?)
        expect(described_class.new(public_event, user: other_user)).to be_allowed_to(:accessible?)
      end
    end

    context "for private events" do
      let(:private_event) { create(:event, visibility: :private_visibility, user: user) }

      it "allows owner to access private events" do
        expect(described_class.new(private_event, user: user)).to be_allowed_to(:accessible?)
      end

      it "denies non-owner to access private events" do
        expect(described_class.new(private_event, user: other_user)).not_to be_allowed_to(:accessible?)
        expect(described_class.new(private_event, user: nil)).not_to be_allowed_to(:accessible?)
      end
    end
  end

  describe "create permissions" do
    context "when user is admin" do
      it "allows admin to create events" do
        policy = described_class.new(Event.new, user: admin_user)
        expect(policy).to be_allowed_to(:create?)
      end
    end

    context "when user is team admin" do
      let(:team) { create(:team) }
      let(:team_admin) { create(:user) }
      
      before do
        create(:team_member, user: team_admin, team: team, role: :admin)
      end

      it "allows team admin to create events" do
        policy = described_class.new(Event.new, user: team_admin)
        expect(policy).to be_allowed_to(:create?)
      end
    end

    context "when user is event moderator in any team" do
      let(:team) { create(:team) }
      let(:event_moderator) { create(:user) }
      let(:team_member) { create(:team_member, user: event_moderator, team: team) }
      
      before do
        # 创建一个 event 用于 TeamResourceAccess
        existing_event = create(:event)
        create(:team_resource_access, 
               team_member: team_member, 
               resource: existing_event, 
               resource_kind: :event, 
               role: :moderator)
      end

      it "allows event moderator to create events" do
        policy = described_class.new(Event.new, user: event_moderator)
        expect(policy).to be_allowed_to(:create?)
      end
    end

    context "when user is regular user" do
      it "denies regular user from creating events" do
        policy = described_class.new(Event.new, user: user)
        expect(policy).not_to be_allowed_to(:create?)
      end
    end

    context "when user is nil" do
      it "denies unauthenticated user from creating events" do
        policy = described_class.new(Event.new, user: nil)
        expect(policy).not_to be_allowed_to(:create?)
      end
    end
  end

  describe "team event permissions" do
    let(:team) { create(:team) }
    let(:team_admin) { create(:user) }
    let(:team_member) { create(:user) }
    let(:non_team_user) { create(:user) }
    let(:team_event) { create(:event, visibility: :team_visibility, user: user) }
    
    before do
      # 设置团队管理员
      create(:team_member, user: team_admin, team: team, role: :admin)
      # 设置普通团队成员
      create(:team_member, user: team_member, team: team, role: :member)
      # 将事件关联到团队
      admin_team_member = TeamMember.find_by(user: team_admin, team: team)
      create(:team_resource_access, 
             team_member: admin_team_member, 
             resource: team_event, 
             resource_kind: :event, 
             role: :moderator)
    end

    context "when user is team admin for this event" do
      it "allows team admin to destroy team events" do
        policy = described_class.new(team_event, user: team_admin)
        expect(policy).to be_allowed_to(:destroy?)
      end

      it "allows team admin to update team events" do
        policy = described_class.new(team_event, user: team_admin)
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows team admin to manage registrations" do
        policy = described_class.new(team_event, user: team_admin)
        expect(policy).to be_allowed_to(:manage_registrations?)
      end
    end

    context "when user is team member but not admin" do
      it "allows team member to view team events" do
        policy = described_class.new(team_event, user: team_member)
        expect(policy).to be_allowed_to(:show?)
      end

      it "denies team member from updating team events" do
        policy = described_class.new(team_event, user: team_member)
        expect(policy).not_to be_allowed_to(:update?)
      end

      it "denies team member from destroying team events" do
        policy = described_class.new(team_event, user: team_member)
        expect(policy).not_to be_allowed_to(:destroy?)
      end
    end

    context "when user is not in the team" do
      it "denies non-team user from viewing team events" do
        policy = described_class.new(team_event, user: non_team_user)
        expect(policy).not_to be_allowed_to(:show?)
      end

      it "denies non-team user from updating team events" do
        policy = described_class.new(team_event, user: non_team_user)
        expect(policy).not_to be_allowed_to(:update?)
      end
    end
  end
end
