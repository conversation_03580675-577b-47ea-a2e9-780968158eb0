class Wechat::SubscriptionController < ApplicationController
  include WechatInteractions

  # 处理关注事件
  on :event, with: "subscribe" do |request|
    openid = request[:FromUserName]
    user_info, identity = find_or_create_user_with_identity(openid)
    store_wechat_session(request, user_info, identity)

    # 发送欢迎消息
    request.reply.text "Welcome! 「架起女性与编程的桥梁」\n\n Send 'help' for commands.\n\n Send 'account' command or click menu '帐号' to query or create an account. \n\n You can also send pictures or voice messages. Enjoy your journey!"

    # nickname = user_info&.[]("nickname") || "朋友"
    # request.reply.textcard(
    #   "欢迎关注 Coding Girls Club!",
    #   "你好, #{nickname}！「架起女性与编程的桥梁」\n\n" +
    #   "发送 'help' 查看可用命令。\n" +
    #   "发送 'account' 或点击菜单【帐号】管理账户。\n\n" +
    #   "开启你的编程之旅吧！",
    #   "访问官网 https://codingirlsclub.com/",
    #   )
  end

  # 处理菜单点击事件 (示例)
  # 需要先通过微信后台或 API 创建带 key 的菜单
  on :click, with: "EXAMPLE_MENU_KEY" do |request, key|
    # 使用 I18n 获取回复文本，并传入变量
    request.reply.text I18n.t("wechats.menu_clicked", key: key)
  end

  # 处理图片消息
  on :image do |request|
    # 使用 I18n 获取回复文本
    request.reply.text I18n.t("wechats.image_received")
  end

  # 处理语音消息
  on :voice do |request|
    # 如果需要识别语音内容，可以使用 request[:Recognition]
    recognition = request[:Recognition]
    # 根据是否有识别结果选择不同的 I18n key
    reply_key = recognition.present? ? ".voice_recognized" : ".voice_received"
    # 使用 I18n 获取回复文本，并传入变量（如果需要）
    # 注意：这里的 reply_key 已经是 ".voice_recognized" 或 ".voice_received"
    # 需要拼接成绝对路径
    absolute_reply_key = "wechats.#{reply_key.sub(/^\./, '')}"
    request.reply.text I18n.t(absolute_reply_key, recognition: recognition)
  end

  # 添加 rescue_from 来处理控制器级别的异常
  rescue_from StandardError do |exception|
    Rails.logger.error "SubscriptionController Error: #{exception.message}" # Updated class name in log
    render xml: { return_code: "FAIL", return_msg: "Internal Server Error" }.to_xml(root: "xml", dasherize: false)
  end
end
