FactoryBot.define do
  factory :message do
    role { "user" }
    content { "Hello, Assistant!" }
    messageable { association :conversation }

    # 系统消息特性
    trait :system do
      role { "system" }
    end

    # 工具消息特性
    trait :tool do
      role { "tool" }
    end

    # 助手消息特性
    trait :assistant do
      role { "assistant" }
      content { "Test assistant response" }
    end

    # 思考中状态的助手消息
    trait :thinking do
      role { "assistant" }
      content { "I'm thinking..." }
      status { :processing }
    end

    # 带工具调用的助手消息
    trait :with_tool_calls do
      role { "assistant" }
      content { nil }
      tool_calls { [ { "id" => "call_123", "type" => "function", "function" => { "name" => "test", "arguments" => "{}" } } ] }
    end

    # 处理中状态
    trait :processing do
      status { :processing }
    end

    # 已完成状态
    trait :completed do
      status { :completed }
    end

    # 失败状态
    trait :failed do
      status { :failed }
    end
  end
end
