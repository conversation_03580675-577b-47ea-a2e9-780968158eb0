// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails
import "@hotwired/turbo-rails";
import "controllers";
import "trix";
import "@rails/actiontext";
import { Application } from "@hotwired/stimulus";
import { initFlowbite } from "flowbite";
import "prismjs"; // Import for side effects only

// Initialize Flowbite on initial load
initFlowbite();

// Stimulus 初始化
const application = Application.start();

// Configure Stimulus development experience
application.debug = false;
window.Stimulus = application;

export { application };

// Flowbite 初始化
document.addEventListener("turbo:load", () => {
  initFlowbite();
});

document.addEventListener("turbo:render", () => {
  initFlowbite();
});

// 清理示例（根据实际需求添加）
// document.addEventListener("turbo:before-cache", () => {
//   // 示例：Modal.instances.forEach(modal => modal.hide())
// });

// Initial highlight for page load (after DOM is ready)
document.addEventListener("DOMContentLoaded", (event) => {
  // Try accessing Prism globally
  if (typeof Prism !== 'undefined') {
    Prism.highlightAll();
  }
});
