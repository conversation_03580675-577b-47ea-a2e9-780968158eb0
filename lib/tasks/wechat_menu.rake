namespace :wechat do
  desc "Create WeChat service account menu"
  task create_service_menu: :environment do
    # Load API with service account
    api = Wechat::ApiLoader.with(account: :service)

    # Load menu YAML file
    menu_yml_path = Rails.root.join("config/wechat_menus/menu_service.yml")
    if File.exist?(menu_yml_path)
      menu = Wechat::ApiLoader.load_yaml(File.read(menu_yml_path))

      # Create menu
      if api.menu_create(menu)
        puts "Service account menu created successfully!"
      else
        puts "Service account menu creation failed!"
      end
    else
      puts "Menu configuration file not found: #{menu_yml_path}"
      puts "Please create the menu configuration file first"
    end
  end

  desc "Create WeChat subscription account menu"
  task create_subscription_menu: :environment do
    # Load API with default (subscription) account
    api = Wechat::ApiLoader.with(account: :default)

    # Load menu YAML file
    menu_yml_path = Rails.root.join("config/wechat_menus/menu_subscription.yml")
    if File.exist?(menu_yml_path)
      menu = Wechat::ApiLoader.load_yaml(File.read(menu_yml_path))

      # Create menu
      if api.menu_create(menu)
        puts "Subscription account menu created successfully!"
      else
        puts "Subscription account menu creation failed!"
      end
    else
      puts "Menu configuration file not found: #{menu_yml_path}"
      puts "Please create the menu configuration file first"
    end
  end
end
