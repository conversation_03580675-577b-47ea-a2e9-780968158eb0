class ProcessBaseUnitJob < ApplicationJob
  queue_as :default

  def perform(base_unit)
    base_unit.file.open do |file|
      # TODO: 根据文件类型处理预览
      # case base_unit.content_type
      # when 'application/pdf'
      #   process_pdf(base_unit, file)
      # when 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      #   process_docx(base_unit, file)
      # end

      # 更新元数据
      metadata = {
        etag: base_unit.file.blob.checksum,
        last_modified: Time.current.httpdate,
        content_length: base_unit.file_size,
        content_type: base_unit.content_type,
        cache_control: "max-age=31536000",
        content_disposition: "inline; filename=\"#{base_unit.name}\""
      }

      base_unit.update!(
        status: "ready",
        processed_at: Time.current,
        metadata: metadata
      )
    end
  rescue StandardError => e
    base_unit.update!(
      status: "failed",
      metadata: { error: e.message }
    )
  end
end
