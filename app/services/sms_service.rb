# require "digest"
# require "faraday"

class SmsService
  SENDCLOUD_API_URL = "https://api.sendcloud.net"
  SENDCLOUD_SMS_USER = Rails.application.credentials.dig(:sendcloud, :sms_user) || "development_user"
  SENDCLOUD_SMS_KEY = Rails.application.credentials.dig(:sendcloud, :sms_key) || "development_key"

  def self.send_sms_code(phone)
    # 发送验证码，返回生成的验证码
    code = generate_sms_code(phone)
    send_sms(phone, code)
    code
  end

  def self.generate_sms_code(phone)
    # 生成并缓存验证码
    code = SecureRandom.random_number(100000..999999).to_s
    Rails.cache.write(sms_cache_key(phone), code, expires_in: 5.minutes)
    code
  end

  def self.verify_sms_code(phone, code)
    # 校验验证码
    cached_code = Rails.cache.read(sms_cache_key(phone))
    cached_code.present? && cached_code == code
  end

  def self.sms_code_remaining_time(phone)
    # 获取验证码剩余有效时间（秒）
    time = Rails.cache.instance_variable_get(:@data)[sms_cache_key(phone)]&.expires_at
    time ? (time - Time.current).to_i : 0
  end

  private

  def self.sms_cache_key(phone)
    # 缓存 key
    "sms_verification:#{phone}"
  end

  def self.send_sms(phone, code)
    params = build_sendcloud_params(phone, code)
    signature = generate_signature(params)
    request_params = params.merge("signature" => signature)
    response = post_to_sendcloud(request_params)
    handle_response(response, phone)
  end

  # 组装 SendCloud 参数
  def self.build_sendcloud_params(phone, code)
    zh_template_id = Rails.application.credentials.dig(:sendcloud, :templates, :zh_id) || "development_zh_template"
    i18n_template_id = Rails.application.credentials.dig(:sendcloud, :templates, :i18n_id) || "development_i18n_template"
    if phone.start_with?("+86")
      template_id = zh_template_id
      api_phone = phone[3..-1]
      msg_type = "0"  # 国内短信类型
    else
      template_id = i18n_template_id
      api_phone = phone
      msg_type = "2"  # 国际短信类型
    end
    {
      "smsUser" => SENDCLOUD_SMS_USER.to_s,
      "templateId" => template_id.to_s,
      "msgType" => msg_type,
      "phone" => api_phone.to_s,
      "vars" => { "%code%" => code }.to_json
    }
  end

  # 生成 SendCloud 签名（排除 signature 字段和 nil 值）
  def self.generate_signature(params)
    api_key = SENDCLOUD_SMS_KEY.to_s
    filtered_params = params.reject { |k, v| k == "signature" || v.nil? }
    sorted_params = filtered_params.sort.to_h # 按参数名字典序排序
    param_str = sorted_params.map { |k, v| "#{k}=#{v}" }.join("&") # 拼接字符串
    sign_str = "#{api_key}&#{param_str}&#{api_key}" # 按文档拼接
    Digest::MD5.hexdigest(sign_str) # 计算 MD5 签名
  end

  # 发送请求到 SendCloud
  def self.post_to_sendcloud(params)
    conn = Faraday.new(url: SENDCLOUD_API_URL)
    conn.post("/smsapi/send") do |req|
      req.headers["Content-Type"] = "application/x-www-form-urlencoded"
      req.body = URI.encode_www_form(params)
    end
  end

  # 处理 SendCloud 响应
  def self.handle_response(response, phone)
    if response.status == 200
      Rails.logger.info "SMS sent successfully to #{phone}"
      Rails.logger.info "Response: #{response.body}"
    else
      Rails.logger.error "Failed to send SMS to #{phone}"
      Rails.logger.error "Response code: #{response.status}, body: #{response.body}"
    end
  end
end
