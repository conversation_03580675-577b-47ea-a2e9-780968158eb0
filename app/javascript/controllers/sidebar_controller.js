import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="sidebar"
export default class extends Controller {
  static targets = ["sidebar", "backdrop", "hamburgerIcon", "closeIcon"]

  connect() {
    // Optional: Add initial setup or checks if needed
    // console.log("Sidebar controller connected");
  }

  toggle() {
    // console.log("Toggling sidebar");
    this.sidebarTarget.classList.toggle('hidden');
    this.backdropTarget.classList.toggle('hidden');
    this.hamburgerIconTarget.classList.toggle('hidden');
    this.closeIconTarget.classList.toggle('hidden');
  }

  // Optional: Add a hide method if needed elsewhere, e.g., on navigation
  // hide() {
  //   this.sidebarTarget.classList.add('hidden');
  //   this.backdropTarget.classList.add('hidden');
  //   this.hamburgerIconTarget.classList.remove('hidden');
  //   this.closeIconTarget.classList.add('hidden');
  // }
}
