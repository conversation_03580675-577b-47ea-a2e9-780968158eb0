# RAG

### RAG 应用的 5 个步骤

为提升 RAG 系统的性能，推荐采用以下迭代步骤：

1. 从基线开始：搭建简单的初始系统，快速验证核心功能。
2. 使用合适的指标评估：明确性能指标，如相关性、真实性和生成质量等。
3. 分析数据并识别问题：通过评估结果，发现系统表现不足之处。
4. 提出假设并开展实验：设计实验验证优化策略。
5. 持续迭代：根据实验结果，不断调整并改进系统。

### RAG Issues

1. RAG 系统的典型组成有哪些？

   1.1 Data Ingestion

   > 收集、处理并存储结构化或非结构化数据（如文本、表格），并将其转换为向量格式存入数据库。

   - [x] 非结构化数据收集
   - [ ] 结构化数据收集（其实实现了部分结构化数据的收集，但是通过 api(http,websocket)、数据库、文件等方式收集结构化数据处理）

   - 数据处理
     - [ ] determin Doc Chunk Parameters
     - [ ] choosing Embedding model
     - [ ] [提升 RAG 性能：分块后，Chunk Enrichment 的 2 个关键优化步骤不可错过](https://mp.weixin.qq.com/s/uly0irmPsgo6PKRB6ec2Sg)
   - 数据存储

     > [ ] 向量化
     > [ ] 数据存储
     > [ ] 向量化
     > 1.2 Data Querying

   - 检索 Retrieval
     [ ] 是否对用户的 query 优化？
   - 综合 Synthesis

2. 基础 RAG 实现面临哪些挑战？
   开发者通常会遇到：

- 响应质量差：上下文不相关或不足。
- 低精度/召回率（Low Precision/Recall）：未能覆盖关键信息或包含无关内容。
- 幻觉现象（Hallucination）：大模型生成脱离事实的回答。
- 信息过时：知识库未及时更新。

3. 如何提升 RAG 应用的性能？
   性能优化可以从以下几方面入手：

- 数据优化（Data Optimization）：调整数据块（Chunk）大小，添加元数据（Metadata），使用高质量的向量嵌入（Embedding）。
- 检索优化（Retrieval Optimization）：采用混合检索（Hybrid Search, 语义+关键词）、重排序（Reranking）、递归检索（Recursive Retrieval）。
- 综合优化（Synthesis Optimization）：引入代理系统（Agent）分解复杂问题，充分利用模型的推理能力。
- 微调（Fine-tuning）：优化嵌入模型或直接微调语言模型。

4. 评估 Evalution
   评估是理解系统性能并发现改进空间的关键：

   > [揭秘 RAG 系统优化的 5 大挑战与 4 大关键指标：打造高性能生成应用](<https://mp.weixin.qq.com/s?__biz=MzAwNDEyNTg0MA==&mid=2649952624&idx=1&sn=f066d76b3d2b23c942deeb47f057b249&chksm=8337148fb4409d991c96025f31b1ff5ab9061efb852370867651c02cf9d294bdf7654dda52e6&cur_album_id=3691173598770331658&scene=190&key=daf9bdc5abc4e8d09ab817a4348ad3e68bf7a378f9963f1abf85d2b412c453a9ed2b52843f2eafdb58f5ac6c7628985b8303a19f295cb5fd083117208184380b3846f45d92fa8b6bcc5ba1e42702e3810e74015826782c1d0798ccd2c5bb71bae3246c10d5466f3bfb0346f3f4c63736a26ecd5608b1d67a4f701b5719e841b1&ascene=14&uin=MTcwMTY4MTA4MA%3D%3D&devicetype=iMac+Mac15%2C9+OSX+OSX+15.2+build(24C101)&version=13080911&nettype=WIFI&lang=en&countrycode=CN&fontScale=100&exportkey=n_ChQIAhIQJEQABXM04OvA54d9S8IejRLxAQIE97dBBAEAAAAAAMNeFOq1WeAAAAAOpnltbLcz9gKNyK89dVj0IH9CeEnVifz1lZPQgHxZ9v0vHUpPlbQvV7VijJylYFUdgW%2FgM0PPr7vXW7zW9JhA%2BeOZ%2F%2BCd8Ks5QV3NTXhf%2Bgt%2FfBTv4uJhl4rU%2FXUDiNH0MJoS6K6C5%2F1PC%2FyPv6WTTQm7OntzhZKPUEZmr1GwMe0fWpXXUChpTOTCC8MYzUqYuvP7R9fbESMV%2FEZi7GLohcv%2Fp220DWPRBvvSm61khf4SBxU5Qr%2FserAX4aWSdLo54MUb5yuIHNpc0j6sdHu5dSNZ4cgU7dIhaJU%3D&acctmode=0&pass_ticket=wUU5t%2BjIO8JA%2BtqoiPXsP%2BqYo3CR6LSK1ED8LPkM%2FAA3drYKq%2FjGv2WXeOp9rDCR&wx_header=0>)

   > [揭开 RAG 系统的评估面纱：4 大指标助你提升输出质量](https://mp.weixin.qq.com/s/xrvyeUpEVu97WohRB2ztYw)

   > **优化 RAG 应用的度量指标及优化技巧**
   > 以下是常见的 RAG 系统优化指标及相应技巧：

   - 上下文相关性（Context Relevance）定义：检索的文档是否包含回答问题所需的信息。优化技巧：扩大检索窗口，增加相关信息的覆盖范围。使用重新排序技术过滤低质量文档。混合检索结合稀疏和密集向量，提升特定领域术语的检索效果。
   - 切块相关性（Chunk Relevance）定义：检索信息中有用内容的比例。优化技巧：调整切块策略，找到平衡相关性和上下文的最佳切块大小。采用语义切块，确保分块信息的连贯性和实用性。
   - 真实性（Faithfulness）定义：生成内容是否忠实于检索信息，避免出现虚构内容。优化技巧：优化检索过程，减少幻觉的可能性。实现文档重新排序以优先考虑高可信度内容。
   - 文本质量（Text Quality）定义：生成内容的流畅性、一致性和语法正确性。优化技巧：使用高质量的 LLM 并进行任务微调。通过提示词设计（如链式推理）改善生成逻辑。

   > **案例分析：基于 Quadrant 和 Quo 的迭代优化过程**
   > 以下是一个使用 Quadrant 向量数据库和 Quo 评估平台构建 RAG 问答系统的案例时间轴。

   - Phase 1: 初始实现系统搭建：基于 Quadrant 实现简单的 RAG 系统，使用其文档作为知识库。初始实验：使用 Mistral Instruct LLM 和固定检索窗口。进行切块策略测试（小块与大块对比）。
   - Phase 2: 迭代优化结果评估：通过 Quo 分析实验结果，发现增加块大小虽然提供更多上下文，但会降低生成真实性。调整方案：采用较小的块大小，同时扩大检索窗口。测试新的嵌入模型和 LLM（如 GPT-3.5）。改进效果：检索相关性和真实性大幅提高，生成质量也明显改善。
   - Phase 3: 针对问题的深度优化问题分析：通过具体数据点，发现检索对领域术语支持不足。优化方案：使用混合检索（稀疏与密集向量结合）。测试多种重新排序模型（如 Cohere 和 ColBERT）。最终成果：混合检索结合 Cohere 的重新排序模型后，系统真实性从 76% 提升至 85%。

- Benchmark（如成功率、MRR（Mean Reciprocal Rank）、NDCG（Normalized Discounted Cumulative Gain）等。）
- 组件与整体性评估 （单独评估检索和综合模块，同时分析系统的最终生成效果。）

1. 哪些技术是"必备"优化？

[ ] 块大小调整（Chunk Size Tuning）：在提供足够上下文和避免信息冗余之间找到平衡。

- [从零开始优化 RAG：7 种 Chunking 方法让你的系统更智能](https://mp.weixin.qq.com/s/gsvwS4qlxPiP1yn38SFMIQ)
  [ ] 元数据过滤（Metadata Filtering）：利用附加信息提升检索结果的相关性。

6. 如何利用代理（Agent）提升 RAG 性能？
   通过引入代理，RAG 系统可以：进行多文档推理（Multi-Document Reasoning）。将复杂查询分解为简单步骤，并逐步获取所需信息。

7. 微调如何优化 RAG 管道？

- 嵌入微调（Embedding Fine-tuning）：提升检索内容的相关性。
- 模型微调（LLM Fine-tuning）：在生成专用数据集上训练，提高生成准确性。

### LLM Tools

#### QA Assistant

- [ ] 挂载不同的知识库
- [ ]

#### Curriculum Development Assistant（课程开发工具）

- [ ] 添加知识库生成 course catalog
- [ ] 根据知识库生成 学习任务 task card
- [ ] 根据知识库生成 学习任务 task 的 quiz card
- [ ] 根据知识库生成 quiz 中的编程题，并且提供代码编辑器，eval 代码

#### 产出工件 artifacts

- [ ] 流程图
- [ ] blue3 的动画 3Blue1Brown.com
  - [Manim](https://github.com/ManimCommunity/manim/)

### User Journey and ERD

- [User Journey](docs/user_journey.mmd)
- [ERD](docs/erd.mmd)
