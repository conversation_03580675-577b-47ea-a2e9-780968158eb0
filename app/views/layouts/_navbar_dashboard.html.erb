<nav class="fixed z-30 w-full bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
  <div class="px-3 py-3 lg:px-5 lg:pl-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center justify-start">
        <%# Removed Flowbite attributes, added Stimulus action and targets %>
        <button id="toggleSidebarMobile"
                type="button"
                data-action="click->sidebar#toggle"
                aria-controls="sidebar"
                class="p-2 text-gray-600 rounded-xs cursor-pointer lg:hidden hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
          <span class="sr-only"><%= t('layouts.dashboard.navbar.toggle_sidebar') %></span>
          <svg data-sidebar-target="hamburgerIcon" id="toggleSidebarMobileHamburger" class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h6a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path></svg>
          <svg data-sidebar-target="closeIcon" id="toggleSidebarMobileClose" class="hidden w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
        </button>
        <%= link_to root_path, class: "flex ml-2 md:mr-24" do %>
          <%= image_tag "https://codingirls.club/cgc_logo_orange_white.png", class: "h-8 mr-3", alt: t('brand.logo_alt') %>
          <span class="self-center text-xl font-semibold sm:text-2xl whitespace-nowrap dark:text-white"><%= t('brand.name', locale: (I18n.locale == :zh ? :zh : :en)) %></span>
        <% end %>

        <%= form_tag "#", method: :get, class: "hidden lg:block lg:pl-32 lg:ml-8" do %>
          <label for="topbar-search" class="sr-only"><%= t('layouts.dashboard.navbar.search.sr_label') %></label>
          <div class="relative mt-1 lg:w-96">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg>
            </div>
            <%= text_field_tag :q, params[:q],
                class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
                placeholder: t('layouts.dashboard.navbar.search.placeholder'),
                id: "topbar-search" %>
          </div>
        <% end %>
      </div>

      <div class="flex items-center">
        <!-- Search mobile -->
        <button id="toggleSidebarMobileSearch" type="button" class="p-2 text-gray-500 rounded-lg lg:hidden hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
          <span class="sr-only"><%= t('layouts.dashboard.navbar.search.sr_label') %></span>
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg>
        </button>

        <!-- Notifications -->
        <div data-controller="notifications"
             data-notifications-user-id-value="<%= authenticated? ? current_user.id : '' %>"
             data-notifications-unread-count-value="<%= authenticated? ? current_user.unread_notifications_count : 0 %>"
             class="relative">
          <button type="button" 
                  data-notifications-target="bell"
                  data-action="click->notifications#toggle"
                  data-dropdown-toggle="notification-dropdown"
                  class="p-2 text-gray-500 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700">
            <span class="sr-only"><%= t('layouts.dashboard.navbar.notifications.sr_label') %></span>
            <div class="relative">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
              </svg>
              <span data-notifications-target="badge" 
                    class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full <%= current_user&.unread_notifications_count&.zero? ? 'hidden' : '' %>">
                <%= current_user&.unread_notifications_count || 0 %>
              </span>
            </div>
          </button>
          <%= render 'shared/notification_dropdown' %>
        </div>

        <!-- Apps -->
        <button type="button" data-dropdown-toggle="apps-dropdown" class="hidden p-2 text-gray-500 rounded-lg sm:flex hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700">
          <span class="sr-only"><%= t('layouts.dashboard.navbar.apps.sr_label') %></span>
          <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path></svg>
        </button>
        <%= render 'shared/apps_dropdown' %>

        <!-- Theme toggle -->
        <button id="theme-toggle"
                data-controller="theme"
                data-action="click->theme#toggle"
                data-tooltip-target="tooltip-toggle"
                type="button"
                class="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-hidden focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5">
          <svg id="theme-toggle-dark-icon"
               data-theme-target="darkIcon"
               class="w-5 h-5"
               fill="currentColor"
               viewBox="0 0 20 20"
               xmlns="http://www.w3.org/2000/svg">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
          </svg>
          <svg id="theme-toggle-light-icon"
               data-theme-target="lightIcon"
               class="hidden w-5 h-5"
               fill="currentColor"
               viewBox="0 0 20 20"
               xmlns="http://www.w3.org/2000/svg">
            <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
          </svg>
        </button>
         <div id="tooltip-toggle"
             role="tooltip"
             class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-xs opacity-0 tooltip">
          <%= t('layouts.dashboard.navbar.theme.toggle_tooltip') %>
          <div class="tooltip-arrow" data-popper-arrow></div>
        </div>

        <!-- Profile -->
        <div class="flex items-center ml-3">
          <div>
            <button type="button" class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" id="user-menu-button-2" aria-expanded="false" data-dropdown-toggle="dropdown-2">
              <span class="sr-only"><%= t('layouts.dashboard.navbar.user_menu.sr_label') %></span>
              <%= render 'shared/avatar', user: current_user, size: 8, alt_text: t('layouts.dashboard.navbar.user_menu.avatar_alt') %>
            </button>
          </div>
          <!-- 用户下拉菜单 -->
          <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded-xs shadow-xs dark:bg-gray-700 dark:divide-gray-600" id="dropdown-2">
            <div class="px-4 py-3" role="none">
              <p class="text-sm text-gray-900 dark:text-white" role="none">
                <%= current_user&.username || t('layouts.dashboard.navbar.user_menu.guest') %>
              </p>
              <p class="text-sm font-medium text-gray-900 truncate dark:text-gray-300" role="none">
                <%= current_user&.email_address || t('layouts.dashboard.navbar.user_menu.not_logged_in') %>
              </p>
            </div>
            <ul class="py-1" role="none">
              <li>
                <%= link_to t('layouts.dashboard.navbar.user_menu.dashboard'), "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" %>
              </li>
              <li>
                <%= link_to t('layouts.dashboard.navbar.user_menu.settings'), settings_users_path, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" %>
              </li>
              <li>
                <% if authenticated? %>
                  <%= button_to t('auth.sign_out'), sign_out_path, method: :delete, class: "block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" %>
                <% else %>
                  <%= link_to t('auth.sign_in'), sign_in_path, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" %>
                <% end %>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>
