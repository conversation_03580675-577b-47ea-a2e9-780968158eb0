// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails
import "@hotwired/turbo-rails";
import "controllers";
import "trix";
import "@rails/actiontext";
import { Application } from "@hotwired/stimulus";
import { initFlowbite } from "flowbite";
import "prismjs"; // Import for side effects only

// Stimulus 初始化
const application = Application.start();

// Configure Stimulus development experience
application.debug = false;
window.Stimulus = application;

export { application };

// 安全的 Flowbite 初始化函数
function safeInitFlowbite() {
  try {
    // 确保 DOM 已准备好
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        initFlowbiteWithValidation();
      });
    } else {
      initFlowbiteWithValidation();
    }
  } catch (error) {
    console.warn('Flowbite initialization failed:', error);
  }
}

// 带验证的 Flowbite 初始化
function initFlowbiteWithValidation() {
  try {
    // 检查必要的模态框元素是否存在
    const modalElements = document.querySelectorAll('[data-modal-toggle], [data-modal-hide], [data-modal-show]');
    const validModals = [];

    modalElements.forEach(element => {
      const modalId = element.getAttribute('data-modal-toggle') ||
                     element.getAttribute('data-modal-hide') ||
                     element.getAttribute('data-modal-show');

      if (modalId && document.getElementById(modalId)) {
        validModals.push(modalId);
      }
    });

    // 只有当所有引用的模态框都存在时才初始化 Flowbite
    if (modalElements.length === 0 || validModals.length > 0) {
      initFlowbite();
    } else {
      console.warn('Some modal elements are missing, skipping Flowbite initialization');
    }
  } catch (error) {
    console.warn('Flowbite validation failed, attempting basic initialization:', error);
    // 如果验证失败，尝试基本初始化
    try {
      initFlowbite();
    } catch (basicError) {
      console.error('Basic Flowbite initialization also failed:', basicError);
    }
  }
}

// 初始化 Flowbite
safeInitFlowbite();

// Turbo 事件处理
document.addEventListener("turbo:load", () => {
  safeInitFlowbite();
});

document.addEventListener("turbo:render", () => {
  safeInitFlowbite();
});

// 清理 Flowbite 组件以防止内存泄漏
document.addEventListener("turbo:before-cache", () => {
  try {
    // 隐藏所有打开的模态框
    const modals = document.querySelectorAll('[data-modal-toggle]');
    modals.forEach(modal => {
      if (!modal.classList.contains('hidden')) {
        modal.classList.add('hidden');
        modal.setAttribute('aria-hidden', 'true');
      }
    });

    // 清理 Flowbite 实例（如果可用）
    if (typeof window.FlowbiteInstances !== 'undefined') {
      // 这里可以添加更多清理逻辑
      console.log('Cleaning up Flowbite instances before cache');
    }
  } catch (error) {
    console.warn('Error during Flowbite cleanup:', error);
  }
});

// Initial highlight for page load (after DOM is ready)
document.addEventListener("DOMContentLoaded", (event) => {
  // Try accessing Prism globally
  if (typeof Prism !== 'undefined') {
    Prism.highlightAll();
  }
});
