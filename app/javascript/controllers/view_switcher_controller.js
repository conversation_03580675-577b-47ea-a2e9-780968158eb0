import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["gridContainer", "mapContainer", "calendarContainer", "gridButton", "mapButton", "calendarButton"]
  static values = { defaultView: String }

  connect() {
    console.log("View switcher controller connected")

    // 检查当前页面是否为 calendar 页面
    if (this.isCalendarPage()) {
      console.log("Currently on calendar page")
      return
    }

    // 首先检查 URL 参数中的 view 参数（优先级最高）
    const urlParams = new URLSearchParams(window.location.search)
    const urlView = urlParams.get('view')

    // 如果 URL 中有明确的 view 参数，使用它；否则使用默认的 grid 视图
    const currentView = urlView || this.defaultViewValue || 'grid'

    if (currentView === 'map') {
      this.showMapView()
    } else if (currentView === 'calendar') {
      this.showCalendarView()
    } else {
      this.showGridView()
    }
  }

  showGrid() {
    console.log("Switching to grid view")

    // 如果在 calendar 页面，跳转到 grid 视图
    if (this.isCalendarPage()) {
      this.navigateToGridView()
      return
    }

    // 如果当前 URL 中有 view=calendar 参数，使用 Turbo 导航以确保面包屑和标题正确更新
    const currentView = this.getCurrentViewFromUrl()
    if (currentView === 'calendar') {
      this.navigateToGridView()
      return
    }

    // 显示网格视图
    this.gridContainerTarget.classList.remove('hidden')
    this.mapContainerTarget.classList.add('hidden')
    if (this.hasCalendarContainerTarget) {
      this.calendarContainerTarget.classList.add('hidden')
    }

    // 更新按钮状态
    this.updateButtonStates('grid')

    // 触发自定义事件
    this.dispatch('viewChanged', { detail: { view: 'grid' } })

    // 更新 URL 参数
    this.updateUrlParams('grid')
  }

  showMap() {
    console.log("Switching to map view")

    // 如果在 calendar 页面，跳转到 map 视图
    if (this.isCalendarPage()) {
      this.navigateToMapView()
      return
    }

    // 如果当前 URL 中有 view=calendar 参数，使用 Turbo 导航以确保面包屑和标题正确更新
    const currentView = this.getCurrentViewFromUrl()
    if (currentView === 'calendar') {
      this.navigateToMapView()
      return
    }

    // 显示地图视图
    this.gridContainerTarget.classList.add('hidden')
    this.mapContainerTarget.classList.remove('hidden')
    if (this.hasCalendarContainerTarget) {
      this.calendarContainerTarget.classList.add('hidden')
    }

    // 更新按钮状态
    this.updateButtonStates('map')

    // 触发地图初始化（如果还未初始化）
    this.dispatch('viewChanged', { detail: { view: 'map' } })

    // 更新 URL 参数
    this.updateUrlParams('map')

    // 延迟触发地图重绘，确保容器已显示
    setTimeout(() => {
      const mapController = this.application.getControllerForElementAndIdentifier(
        this.mapContainerTarget.querySelector('[data-controller*="map"]'),
        'map'
      )
      if (mapController && mapController.map) {
        mapController.map.invalidateSize()
      }
    }, 100)
  }

  // 纯视图切换方法（不导航，只显示/隐藏）
  showGridView() {
    this.gridContainerTarget.classList.remove('hidden')
    this.mapContainerTarget.classList.add('hidden')
    if (this.hasCalendarContainerTarget) {
      this.calendarContainerTarget.classList.add('hidden')
    }
    this.updateButtonStates('grid')
  }

  showMapView() {
    this.gridContainerTarget.classList.add('hidden')
    this.mapContainerTarget.classList.remove('hidden')
    if (this.hasCalendarContainerTarget) {
      this.calendarContainerTarget.classList.add('hidden')
    }
    this.updateButtonStates('map')

    // 延迟触发地图重绘，确保容器已显示
    setTimeout(() => {
      const mapController = this.application.getControllerForElementAndIdentifier(
        this.mapContainerTarget.querySelector('[data-controller*="map"]'),
        'map'
      )
      if (mapController && mapController.map) {
        mapController.map.invalidateSize()
      }
    }, 100)
  }

  showCalendarView() {
    this.gridContainerTarget.classList.add('hidden')
    this.mapContainerTarget.classList.add('hidden')
    if (this.hasCalendarContainerTarget) {
      this.calendarContainerTarget.classList.remove('hidden')
    }
    this.updateButtonStates('calendar')
  }

  showCalendar() {
    console.log("Switching to calendar view")

    // 如果在 calendar 页面，不需要做任何事
    if (this.isCalendarPage()) {
      return
    }

    // 使用 Turbo 导航到日历视图以确保数据和UI正确更新
    this.navigateToCalendarView()
  }

  // 检查是否在 calendar 页面
  isCalendarPage() {
    return window.location.pathname.includes('/events/calendar')
  }

  // 从 URL 获取当前视图
  getCurrentViewFromUrl() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('view') || 'grid'
  }

  // 跳转到 grid 视图
  navigateToGridView() {
    const currentUrl = new URL(window.location)

    // 如果在 calendar 页面，回到主 events 页面
    const newPath = currentUrl.pathname.includes('/events/calendar') ? '/events' : currentUrl.pathname

    // 保持其他查询参数，但移除 date 和 view 参数
    const searchParams = new URLSearchParams(currentUrl.search)
    searchParams.delete('date')
    searchParams.delete('view')

    const queryString = searchParams.toString()
    const newUrl = queryString ? `${newPath}?${queryString}` : newPath

    // 跳转到新页面
    window.location.href = newUrl
  }

  // 跳转到 map 视图
  navigateToMapView() {
    const currentUrl = new URL(window.location)

    // 移除 calendar 路径部分，回到主 events 页面
    const newPath = '/events'

    // 保持其他查询参数，但移除 date 参数，添加 view=map
    const searchParams = new URLSearchParams(currentUrl.search)
    searchParams.delete('date')
    searchParams.set('view', 'map')

    const queryString = searchParams.toString()
    const newUrl = queryString ? `${newPath}?${queryString}` : `${newPath}?view=map`

    // 跳转到新页面
    window.location.href = newUrl
  }

  // 跳转到 calendar 视图
  navigateToCalendarView() {
    const currentUrl = new URL(window.location)

    // 保持当前路径，添加 view=calendar 参数
    const searchParams = new URLSearchParams(currentUrl.search)
    searchParams.set('view', 'calendar')

    const queryString = searchParams.toString()
    const newUrl = `${currentUrl.pathname}?${queryString}`

    // 跳转到新页面
    window.location.href = newUrl
  }

  updateButtonStates(activeView) {
    const baseClasses = 'inline-flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200'
    const activeClasses = `${baseClasses} bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300`
    const inactiveClasses = `${baseClasses} text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300`

    // 重置所有按钮为非活跃状态
    this.gridButtonTarget.className = inactiveClasses
    this.mapButtonTarget.className = inactiveClasses
    if (this.hasCalendarButtonTarget) {
      this.calendarButtonTarget.className = inactiveClasses
    }

    // 设置活跃按钮
    if (activeView === 'grid') {
      this.gridButtonTarget.className = activeClasses
    } else if (activeView === 'map') {
      this.mapButtonTarget.className = activeClasses
    } else if (activeView === 'calendar' && this.hasCalendarButtonTarget) {
      this.calendarButtonTarget.className = activeClasses
    }
  }

  // 更新 URL 参数
  updateUrlParams(view) {
    const url = new URL(window.location)
    if (view === 'grid') {
      url.searchParams.delete('view')
    } else {
      url.searchParams.set('view', view)
    }
    window.history.replaceState({}, '', url)
  }

  // 响应外部视图切换请求
  switchToView(event) {
    const view = event.detail.view
    if (view === 'map') {
      this.showMap()
    } else if (view === 'calendar') {
      this.showCalendar()
    } else {
      this.showGrid()
    }
  }
}
