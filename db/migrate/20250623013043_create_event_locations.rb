class CreateEventLocations < ActiveRecord::Migration[8.0]
  def change
    create_table :event_locations do |t|
      t.string :name, null: false
      t.string :address
      t.string :city
      t.string :province
      t.string :country
      t.decimal :latitude, precision: 10, scale: 6
      t.decimal :longitude, precision: 10, scale: 6
      t.text :directions

      t.timestamps
    end

    add_index :event_locations, :city
    add_index :event_locations, [ :latitude, :longitude ]
  end
end
