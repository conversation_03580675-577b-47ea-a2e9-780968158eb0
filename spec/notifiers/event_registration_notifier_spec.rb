require 'rails_helper'

RSpec.describe EventRegistrationNotifier, type: :notifier do
  let(:organizer) { create(:user, username: "organizer_user") }
  let(:registrant) { create(:user, username: "participant_user") }
  let(:event) { create(:event, title: "Rails Conference 2024", user: organizer) }
  let(:registration) { create(:event_registration, event: event, user: registrant) }

  describe 'notification methods' do
    before do
      EventRegistrationNotifier.with(event: event).deliver(registrant)
    end
    
    let(:notification) { registrant.notifications.last }

    describe '#message' do
      it 'returns formatted message with event and user information' do
        message = notification.message
        expected_message = "You have successfully registered for the event '#{event.title}'."
        expect(message).to eq(expected_message)
      end
    end

    describe '#url' do
      it 'returns the event path' do
        expected_url = Rails.application.routes.url_helpers.event_path(event)
        expect(notification.url).to eq(expected_url)
      end
    end
  end

  describe 'delivery' do
    let(:recipient) { create(:user) }

    context 'when delivering notification' do
      it 'creates a notification record' do
        expect {
          EventRegistrationNotifier.with(event: event).deliver(recipient)
        }.to change { recipient.notifications.count }.by(1)
      end

      it 'stores the correct event information' do
        EventRegistrationNotifier.with(event: event).deliver(recipient)
        
        notification = recipient.notifications.last
        expect(notification.params[:event]).to eq(event)
      end
    end

    context 'when delivering to event organizer' do
      it 'notifies organizer about new registration' do
        expect {
          EventRegistrationNotifier.with(event: event).deliver(organizer)
        }.to change { organizer.notifications.count }.by(1)
      end
    end

    context 'email delivery conditions' do
      let(:user_with_email_enabled) { create(:user) }
      let(:user_with_email_disabled) { create(:user) }

      before do
        # 设置用户的邮件通知偏好 - 正确的结构
        user_with_email_enabled.update_preferences({
          "notification" => {
            "email" => true,
            "event_registration" => { "email" => true }
          }
        })
        
        user_with_email_disabled.update_preferences({
          "notification" => {
            "email" => true,
            "event_registration" => { "email" => false }
          }
        })
      end

      it 'checks email notification preference correctly for enabled user' do
        expect(user_with_email_enabled.notification_enabled?(:event_registration, :email)).to be true
      end

      it 'checks email notification preference correctly for disabled user' do
        expect(user_with_email_disabled.notification_enabled?(:event_registration, :email)).to be false
      end
    end
  end

  describe 'validation' do
    it 'requires event parameter' do
      expect {
        EventRegistrationNotifier.with({}).deliver(create(:user))
      }.to raise_error(Noticed::ValidationError)
    end

    it 'allows valid event parameter' do
      expect {
        EventRegistrationNotifier.with(event: event).deliver(create(:user))
      }.not_to raise_error
    end
  end

  describe 'ActionCable delivery' do
    let(:recipient) { create(:user) }

    it 'configures ActionCable delivery properly' do
      notifier = EventRegistrationNotifier.with(event: event)
      
      # 测试 ActionCable 配置是否正确设置
      delivery_methods = notifier.class.delivery_methods
      action_cable_method = delivery_methods.find { |method| method.last.name == :action_cable }
      
      expect(action_cable_method).to be_present
      expect(action_cable_method.last.config[:channel]).to eq("NotificationChannel")
    end
  end

  describe 'integration with Event model' do
    it 'works with different event types' do
      events = [
        create(:event, :upcoming, title: "Workshop"),
        create(:event, :published, title: "Conference"),
      ]

      events.each do |test_event|
        expect {
          EventRegistrationNotifier.with(event: test_event).deliver(create(:user))
        }.not_to raise_error
      end
    end
  end
end
