<%#
# Show

This view is the template for the show page.
It renders the attributes of a resource,
as well as a link to its edit page.

## Local variables:

- `page`:
  An instance of [Administrate::Page::Show][1].
  Contains methods for accessing the resource to be displayed on the page,
  as well as helpers for describing how each attribute of the resource
  should be displayed.

[1]: http://www.rubydoc.info/gems/administrate/Administrate/Page/Show
%>

<% content_for(:title) { t("administrate.actions.show_resource", name: page.page_title) } %>

<header class="main-content__header">
  <h1 class="main-content__page-title">
    <%= content_for(:title) %>
  </h1>

  <%= content_for(:header_middle) %>

  <div>
    <%= link_to(
      t("administrate.actions.edit_resource", name: page.page_title),
      [:edit, namespace, page.resource],
      class: "button",
    ) if accessible_action?(page.resource, :edit) %>

    <%= button_to(
      t("administrate.actions.destroy"),
      [namespace, page.resource],
      class: "button button--danger",
      method: :delete,
      data: { turbo_confirm: t("administrate.actions.confirm") }
    ) if accessible_action?(page.resource, :destroy) %>
  </div>

  <%= content_for(:header_last) %>
</header>

<%= content_for(:before_main) %>

<% if content_for?(:main) %>
  <%= content_for(:main) %>
<% else %>
  <section class="main-content__body">
    <dl>
      <% page.attributes.each do |title, attributes| %>
        <fieldset class="<%= "field-unit--nested" if title.present? %>">
          <% if title.present? %>
            <legend><%= t "helpers.label.#{page.resource_name}.#{title}", default: title %></legend>
          <% end %>

          <% attributes.each do |attribute| %>
            <dt class="attribute-label" id="<%= attribute.name %>">
            <%= t(
              "helpers.label.#{resource_name}.#{attribute.name}",
              default: page.resource.class.human_attribute_name(attribute.name),
            ) %>
            </dt>

            <dd class="attribute-data attribute-data--<%=attribute.html_class%>"
                ><%= render_field attribute, page: page %></dd>
          <% end %>
        </fieldset>
      <% end %>
    </dl>
  </section>
<% end %>

<%= content_for(:after_main) %>
