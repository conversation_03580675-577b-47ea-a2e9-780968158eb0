<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-4xl lg:py-16">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= @base_unit.name %></h1>
        <p class="mt-2 text-lg text-gray-500 dark:text-gray-400"><%= t('base_units.show.subtitle') %></p>
      </div>

      <%= link_to base_units_path, class: "text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 inline-flex items-center" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        <%= t('base_units.show.actions.return_to_list') %>
      <% end %>
    </div>

    <div class="space-y-8">
      <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-xs dark:bg-gray-800 dark:border-gray-700">
        <h3 class="mb-4 text-xl font-bold text-gray-900 dark:text-white"><%= t('base_units.show.sections.file_info') %></h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('base_units.show.fields.filename') %></h4>
            <p class="text-base text-gray-900 dark:text-white"><%= @base_unit.name %></p>
          </div>
          <div>
            <h4 class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('base_units.show.fields.file_type') %></h4>
            <p class="text-base text-gray-900 dark:text-white"><%= BaseUnit::ALLOWED_TYPES[@base_unit.content_type] %></p>
          </div>
          <div>
            <h4 class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('base_units.show.fields.file_size') %></h4>
            <p class="text-base text-gray-900 dark:text-white"><%= number_to_human_size(@base_unit.file_size) %></p>
          </div>
          <div>
            <h4 class="mb-2 text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('base_units.show.fields.upload_time') %></h4>
            <p class="text-base text-gray-900 dark:text-white"><%= l @base_unit.created_at, format: :long %></p>
          </div>
        </div>
      </div>

      <% if @base_unit.status == 'ready' %>
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-xs dark:bg-gray-800 dark:border-gray-700">
          <h3 class="mb-4 text-xl font-bold text-gray-900 dark:text-white"><%= t('base_units.show.sections.file_preview') %></h3>

          <% if @base_unit.content_type.start_with?('image/') %>
            <div class="rounded-lg overflow-hidden">
              <%= image_tag url_for(@base_unit.file), class: "w-full h-auto" %>
            </div>
          <% elsif @base_unit.content_type == 'text/markdown' %>
            <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700 prose prose-sm max-w-none dark:prose-invert">
              <% begin %>
                <% content = @base_unit.file.blob.download %>
                <% if content.force_encoding("UTF-8").valid_encoding? %>
                  <%= markdown(content) %>
                <% else %>
                  <%= markdown(content.force_encoding("GBK").encode("UTF-8")) %>
                <% end %>
              <% rescue => e %>
                <p class="text-red-500 dark:text-red-400"><%= t('base_units.show.messages.encoding_error', error: e.message) %></p>
              <% end %>
            </div>
          <% elsif @base_unit.content_type == 'text/plain' %>
            <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700 font-mono text-sm whitespace-pre-wrap text-gray-900 dark:text-gray-100">
              <%= simple_format(@base_unit.file.blob.download) %>
            </div>
          <% elsif @base_unit.content_type == 'application/pdf' %>
            <div class="aspect-16/9 rounded-lg overflow-hidden">
              <iframe src="<%= rails_blob_path(@base_unit.file, disposition: 'inline') %>"
                      class="w-full h-full"
                      type="application/pdf">
              </iframe>
            </div>
          <% else %>
            <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
              <p class="text-gray-500 dark:text-gray-400">
                <%= t('base_units.show.messages.preview_not_supported') %>
                <%= link_to t('base_units.show.messages.download_to_view'), rails_blob_path(@base_unit.file, disposition: 'attachment'),
                    class: "font-medium text-primary-600 hover:underline dark:text-primary-500" %>
                to view its contents.
              </p>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-xs dark:bg-gray-800 dark:border-gray-700">
          <div class="flex items-center justify-center p-8">
            <div class="text-center">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-primary-500 border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                <span class="absolute! -m-px! h-px! w-px! overflow-hidden! whitespace-nowrap! border-0! p-0! [clip:rect(0,0,0,0)]!"><%= t('base_units.show.messages.loading') %></span>
              </div>
              <p class="mt-4 text-gray-500 dark:text-gray-400"><%= t('base_units.show.messages.processing') %></p>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>
