require 'rails_helper'

RSpec.describe EventReminderNotifier, type: :notifier do
  let(:organizer) { create(:user, username: "event_organizer") }
  let(:event) { create(:event, title: "Ruby Meetup", user: organizer, start_time: 1.day.from_now.change(hour: 14, min: 30)) }

  describe 'notification methods' do
    let(:participant) { create(:user) }
    
    shared_examples 'notification methods' do |reminder_type|
      before do
        EventReminderNotifier.with(event: event, reminder_type: reminder_type).deliver(participant)
      end
      
      let(:notification) { participant.notifications.last }

      describe '#message' do
        it "returns #{reminder_type} reminder message" do
          case reminder_type
          when 'day_before'
            expected_message = "活动提醒：您报名的活动「#{event.title}」将在明天 14:30 开始"
          when 'hour_before'
            expected_message = "活动即将开始：您报名的活动「#{event.title}」将在1小时后开始"
          when 'starting_soon'
            expected_message = "活动开始了：您报名的活动「#{event.title}」现在开始了"
          else
            expected_message = "活动提醒：您有一个活动「#{event.title}」"
          end
          
          expect(notification.message).to eq(expected_message)
        end
      end

      describe '#url' do
        it 'returns the event path' do
          expected_url = Rails.application.routes.url_helpers.event_path(event)
          expect(notification.url).to eq(expected_url)
        end
      end

      describe '#title' do
        it 'returns the correct title' do
          expect(notification.title).to eq("活动提醒")
        end
      end

      describe '#avatar_user' do
        it 'returns the event creator user' do
          expect(notification.avatar_user).to eq(event.user)
        end
      end
    end

    context 'when reminder_type is day_before' do
      include_examples 'notification methods', 'day_before'
    end

    context 'when reminder_type is hour_before' do
      include_examples 'notification methods', 'hour_before'
    end

    context 'when reminder_type is starting_soon' do
      include_examples 'notification methods', 'starting_soon'
    end

    context 'when reminder_type is unknown' do
      include_examples 'notification methods', 'unknown_type'
    end
  end


  describe 'delivery' do
    let(:participant) { create(:user) }
    let(:registration) { create(:event_registration, event: event, user: participant) }

    context 'when delivering notification' do
      it 'creates a notification record' do
        expect {
          EventReminderNotifier.with(event: event, reminder_type: 'day_before').deliver(participant)
        }.to change { participant.notifications.count }.by(1)
      end

      it 'stores the correct event and reminder type information' do
        EventReminderNotifier.with(event: event, reminder_type: 'hour_before').deliver(participant)
        
        notification = participant.notifications.last
        expect(notification.params[:event]).to eq(event)
        expect(notification.params[:reminder_type]).to eq('hour_before')
      end
    end

    context 'when delivering to multiple participants' do
      let(:participants) { create_list(:user, 3) }
      
      before do
        participants.each { |user| create(:event_registration, event: event, user: user) }
      end

      it 'creates notifications for all participants' do
        expect {
          EventReminderNotifier.with(event: event, reminder_type: 'day_before').deliver(participants)
        }.to change { Noticed::Notification.count }.by(3)
      end
    end

    context 'reminder scheduling scenarios' do
      let(:tomorrow_event) { create(:event, start_time: 1.day.from_now) }
      let(:soon_event) { create(:event, start_time: 1.hour.from_now) }
      let(:starting_event) { create(:event, start_time: Time.current) }

      it 'handles day before reminders' do
        EventReminderNotifier.with(event: tomorrow_event, reminder_type: 'day_before').deliver(participant)
        notification = participant.notifications.last
        
        expect(notification.message).to include("将在明天")
      end

      it 'handles hour before reminders' do
        EventReminderNotifier.with(event: soon_event, reminder_type: 'hour_before').deliver(participant)
        notification = participant.notifications.last
        
        expect(notification.message).to include("将在1小时后开始")
      end

      it 'handles starting soon reminders' do
        EventReminderNotifier.with(event: starting_event, reminder_type: 'starting_soon').deliver(participant)
        notification = participant.notifications.last
        
        expect(notification.message).to include("现在开始了")
      end
    end
  end

  describe 'validation' do
    it 'requires event parameter' do
      expect {
        EventReminderNotifier.with(reminder_type: 'day_before').deliver(create(:user))
      }.to raise_error(Noticed::ValidationError)
    end

    it 'requires reminder_type parameter' do
      expect {
        EventReminderNotifier.with(event: event).deliver(create(:user))
      }.to raise_error(Noticed::ValidationError)
    end

    it 'allows valid parameters' do
      expect {
        EventReminderNotifier.with(event: event, reminder_type: 'day_before').deliver(create(:user))
      }.not_to raise_error
    end
  end

  describe 'message formatting with different time formats' do
    let(:morning_event) { create(:event, start_time: 1.day.from_now.change(hour: 9, min: 0)) }
    let(:evening_event) { create(:event, start_time: 1.day.from_now.change(hour: 18, min: 45)) }
    let(:test_participant) { create(:user) }

    it 'formats morning time correctly' do
      EventReminderNotifier.with(event: morning_event, reminder_type: 'day_before').deliver(test_participant)
      notification = test_participant.notifications.last
      
      expect(notification.message).to include("09:00")
    end

    it 'formats evening time correctly' do
      EventReminderNotifier.with(event: evening_event, reminder_type: 'day_before').deliver(test_participant)
      notification = test_participant.notifications.last
      
      expect(notification.message).to include("18:45")
    end
  end

  describe 'integration with Event states' do
    let(:published_event) { create(:event, :published) }
    let(:cancelled_event) { create(:event, :cancelled) }

    it 'works with published events' do
      expect {
        EventReminderNotifier.with(event: published_event, reminder_type: 'day_before').deliver(create(:user))
      }.not_to raise_error
    end

    it 'works with cancelled events' do
      expect {
        EventReminderNotifier.with(event: cancelled_event, reminder_type: 'day_before').deliver(create(:user))
      }.not_to raise_error
    end
  end
end
