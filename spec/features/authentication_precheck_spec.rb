require 'rails_helper'

RSpec.describe "Authentication Pre-check", type: :feature do
  let(:guest_user) { nil }
  let(:authenticated_user) { create(:user) }
  let(:notification) { create(:notification, recipient: authenticated_user) }

  describe "index? action for guest user" do
    it "denies access to notification index for guest user" do
      # Test the policy directly for guest user
      policy = NotificationPolicy.new(notification, user: guest_user)
      expect(policy).not_to be_allowed_to(:index?)
    end

    it "returns false for authenticated? check with guest user" do
      policy = NotificationPolicy.new(notification, user: guest_user)
      expect(policy.send(:authenticated?)).to be false
    end
  end

  describe "index? action for authenticated user" do
    it "allows access to notification index for authenticated user" do
      # Test the policy directly for authenticated user
      policy = NotificationPolicy.new(notification, user: authenticated_user)
      expect(policy).to be_allowed_to(:index?)
    end

    it "returns true for authenticated? check with authenticated user" do
      policy = NotificationPolicy.new(notification, user: authenticated_user)
      expect(policy.send(:authenticated?)).to be true
    end
  end

  describe "Controller-level authentication check" do
    context "when accessing notifications index as guest" do
      it "redirects to login page" do
        # Clear any existing sessions/cookies
        Capybara.reset_sessions!
        
        visit notifications_path
        
        # Should be redirected to login
        expect(current_path).to eq(new_session_path)
      end
    end

    context "when accessing notifications index as authenticated user" do
      it "allows access to notifications index" do
        # Sign in as authenticated user
        sign_in(authenticated_user)
        
        visit notifications_path
        
        # Should be able to access the page
        expect(current_path).to eq(notifications_path)
        expect(page).to have_content("Notifications")
      end
    end
  end

  describe "API endpoint authentication check" do
    context "when making API request as guest" do
      it "returns 302 redirect for API requests" do
        # Use Capybara's page.driver for API calls
        page.driver.get notifications_path, {}, { 'HTTP_ACCEPT' => 'application/json' }
        
        # Should be redirected (302) since we're using session-based auth
        expect(page.driver.status_code).to eq(302)
      end
    end

    context "when making API request as authenticated user" do
      it "allows access for authenticated users" do
        # Sign in first
        sign_in(authenticated_user)
        
        # Make API request with proper format
        visit notifications_path(format: :json)
        
        # Should be successful (either 200 or 406 depending on view template availability)
        expect(page.status_code).to be_in([200, 406])
        
        # If 406, it means there's no JSON template but auth succeeded
        # This is expected since we're testing authentication, not template rendering
      end
    end
  end

  describe "Pre-check authentication mechanism" do
    it "successfully validates pre-check configuration" do
      # Since ActionPolicy's pre_check implementation details are internal,
      # let's verify the behavior instead of the configuration
      
      # Test that guest user is denied access to index?
      policy = NotificationPolicy.new(notification, user: guest_user)
      expect(policy).not_to be_allowed_to(:index?)
      
      # Test that guest user is denied access to mark_all_read?
      expect(policy).not_to be_allowed_to(:mark_all_read?)
      
      # Test that authenticated user is allowed access to index?
      auth_policy = NotificationPolicy.new(notification, user: authenticated_user)
      expect(auth_policy).to be_allowed_to(:index?)
      
      # Test that authenticated user is allowed access to mark_all_read?
      expect(auth_policy).to be_allowed_to(:mark_all_read?)
    end
  end

end
