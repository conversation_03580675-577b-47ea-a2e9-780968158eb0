require "administrate/base_dashboard"

class EventRegistrationDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    user: Field::BelongsTo,
    event: Field::BelongsTo,
    status: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    amount_paid: Field::String.with_options(searchable: false),
    registered_at: Field::DateTime,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    user
    event
    status
    amount_paid
    registered_at
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    user
    event
    status
    amount_paid
    registered_at
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    user
    event
    status
    amount_paid
    registered_at
  ].freeze

  COLLECTION_FILTERS = {
    confirmed: ->(resources) { resources.confirmed },
    pending: ->(resources) { resources.where(status: "pending") },
    attended: ->(resources) { resources.attended },
    cancelled: ->(resources) { resources.where(status: "cancelled") }
  }.freeze

  def display_resource(event_registration)
    "#{event_registration.user&.username} - #{event_registration.event&.title}"
  end
end