module Ai
  # LLM 服务类，处理语言模型相关配置
  class LLMService
    class << self
      # 创建 LLM 实例
      # @return [Langchain::LLM::Base] LLM 实例
      def create_llm
        Rails.env.production? ? create_doubao_llm : create_doubao_llm
      end

      # 获取 Qdrant 向量数据库 URL
      # @return [String] Qdrant 服务地址
      def qdrant_url
        Rails.env.production? ? Rails.application.credentials.dig(:qdrant, :url) : "http://localhost:6333"
      end

      # 获取 Qdrant API 密钥
      # @return [String, nil] Qdrant API 密钥
      def qdrant_api_key
        Rails.env.production? ? Rails.application.credentials.dig(:qdrant, :api_key) : nil
      end

      private

      # 创建豆包 LLM 实例
      # @return [Langchain::LLM::Doubao] 豆包 LLM 实例
      def create_doubao_llm
        Langchain::LLM::Doubao.new(
          api_key: Rails.application.credentials.dig(:doubao, :ARK_API_KEY),
          default_options: doubao_options
        )
      end

      # 创建 Ollama LLM 实例
      # @return [Langchain::LLM::Ollama] Ollama LLM 实例
      def create_ollama_llm
        Langchain::LLM::Ollama.new
      end

      # 豆包 LLM 配置选项
      # @return [Hash] 配置选项哈希
      def doubao_options
        {
          chat_model: Rails.application.credentials.dig(:doubao, :ARK_DOUBAO_1_5_PRO_32K_MODEL),
          embedding_model: Rails.application.credentials.dig(:doubao, :ARK_DOUBAO_EMBEDDING_LARGE_MODEL),
          dimensions: 2560
        }
      end
    end
  end
end
