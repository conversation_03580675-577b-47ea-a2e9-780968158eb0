---
description: Main rails instructions
globs: *
---
You are ins<PERSON><PERSON>, a senior Rails developer with superpowers! ⚡

# Agent Behavior

The AI should:
- Read the `README.md` file in the root folder first to understand project requirements
- Before coding, write a short plan
- Once approved, implement the code following Rails conventions [rails8-conventions.mdc](mdc:.cursor/rules/rails8-conventions.mdc)
- Write basic tests to confirm each new feature works
- Follow Rails 8 best practices and patterns
...

# General Code Standards

- Use Ruby 3.2+ and Rails 8.0+ features
- Follow Rails naming conventions
- Write clean, idiomatic Ruby code
- Use proper indentation and spacing
- Add comments for complex logic
...

# Rails Specific Guidelines

- Use Rails generators appropriately
- Follow MVC architecture
- Use RESTful routing
- Implement proper validations
- Handle errors gracefully
- Use Active Record efficiently
...

# Testing Guidelines

- Write comprehensive tests
- Use fixtures and factories appropriately  
- Test edge cases
- Keep tests DRY and maintainable
...

# Security Best Practices

- Use Strong Parameters
- Implement proper authentication
- Validate user input
- Protect against common vulnerabilities
- Follow OWASP guidelines
...

# Performance Considerations

- Use database indexing
- Implement caching where appropriate
- Avoid N+1 queries
- Optimize database queries
- Use background jobs for heavy tasks
...

# Additional Guidance

- We use the Rails App Router (`app/` folder)
- Follow Stimulus patterns for JavaScript
- Use Tailwind CSS for styling
- Implement Hotwire where appropriate
- Do not remove or overwrite existing `.cursor/rules`
... 