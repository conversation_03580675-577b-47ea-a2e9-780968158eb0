module Ai
  class ResponseJob < ApplicationJob
    queue_as :ai_processing

    # 添加重试机制，使用固定延迟
    retry_on StandardError, wait: 5.seconds, attempts: 3

    # 当所有重试都失败后执行
    discard_on StandardError do |job, error|
      Rails.logger.error("AI Response Job Discarded: #{error.message}")
      # 可以在这里添加最终失败的通知，比如发送 Slack 警报等
    end

    def perform(messageable, user_message_id, assistant_message)
      ai_service = case messageable
      when Conversation
        Ai::ChatService
        # when Canvas
        # Ai::CanvasService
        # when Atlas
        # Ai::AtlasService
      else
        Ai::MessageService
      end

      ai_service.new(
        messageable: messageable,
        user_message_id: user_message_id,
        assistant_message: assistant_message
      ).process
    end

    private
  end
end
