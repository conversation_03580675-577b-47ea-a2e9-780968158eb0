require "administrate/base_dashboard"

class KnowledgeBaseDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    archived: Field::<PERSON>olean,
    base_units: Field::<PERSON><PERSON>any,
    deleted_at: Field::DateTime,
    description: Field::Text,
    knowledge_base_posts: Field::HasMany,
    metadata: Field::Text,
    name: Field::String,
    owner: Field::BelongsTo,
    posts: Field::Has<PERSON>any,
    team_members: Field::<PERSON><PERSON><PERSON>,
    team_resource_accesses: Field::HasMany,
    teams: Field::HasMany,
    visibility: Field::Select.with_options(searchable: false, collection: ->(field) { field.resource.class.send(field.attribute.to_s.pluralize).keys }),
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    archived
    base_units
    deleted_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    archived
    base_units
    deleted_at
    description
    knowledge_base_posts
    metadata
    name
    owner
    posts
    team_members
    team_resource_accesses
    teams
    visibility
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    archived
    base_units
    deleted_at
    description
    knowledge_base_posts
    metadata
    name
    owner
    posts
    team_members
    team_resource_accesses
    teams
    visibility
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how knowledge bases are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(knowledge_base)
  #   "KnowledgeBase ##{knowledge_base.id}"
  # end
end
