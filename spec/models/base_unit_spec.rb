require 'rails_helper'

RSpec.describe BaseUnit, type: :model do
  let(:user) { create(:user) }
  let(:base_unit) { build(:base_unit, user: user) }
  let(:valid_pdf) { fixture_file_upload('spec/fixtures/files/sample.pdf', 'application/pdf') }
  let(:valid_docx) { fixture_file_upload('spec/fixtures/files/sample.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') }
  let(:invalid_image) { fixture_file_upload('spec/fixtures/files/sample.jpg', 'image/jpeg') }
  # let(:valid_image) { fixture_file_upload('spec/fixtures/files/sample.png', 'image/png') }

  after { ActiveStorage::Blob.all.each(&:purge) }

  describe 'File type validation' do
    context 'allowed types' do
      it 'accepts PDF files' do
        base_unit.file.attach(valid_pdf)
        expect(base_unit).to be_valid
      end

      it 'accepts DOCX files' do
        base_unit.file.attach(valid_docx)
        expect(base_unit).to be_valid
      end
    end

    # TODO: Image file support
    # context 'rejected types' do
    #   it 'rejects image files' do
    #     base_unit.file.attach(invalid_image)
    #     expect(base_unit).not_to be_valid
    #     expect(base_unit.errors[:file]).to include('has invalid content type')
    #   end
    # end
  end

  describe '#convert_to_md' do
    let(:base_unit) { create(:base_unit, user: user) }

    context 'PDF file conversion' do
      before { base_unit.file.attach(valid_pdf) }

      it 'returns converted HTML' do
        allow(PDF::Reader).to receive(:new).and_return(
          instance_double(PDF::Reader, pages: [ double(text: 'PDF Content') ])
        )
        expect(base_unit.convert_to_md).to include('<p>PDF Content</p>')
      end
    end

    context 'Error handling' do
      it 'logs and raises conversion error' do
        base_unit.file.attach(invalid_image)
        expect(Rails.logger).to receive(:error).with(/File conversion failed: Unsupported file type: image\/jpeg/)
        expect { base_unit.convert_to_md }.to raise_error(BaseUnit::ConversionError, /Unsupported file type: image\/jpeg/)
      end
    end
  end
end
