namespace :wechat do
  namespace :notification do
    desc "测试微信通知功能"
    task test: :environment do
      puts "开始测试微信通知功能..."

      # 检查微信投递方法是否加载
      begin
        delivery_method = Noticed::DeliveryMethods::Wechat.new
        puts "✓ 微信投递方法加载成功"
      rescue => e
        puts "✗ 微信投递方法加载失败: #{e.message}"
        exit 1
      end

      # 检查用户模型扩展
      user = User.first
      if user
        puts "✓ 用户模型扩展检查:"
        puts "  - 是否绑定微信: #{user.has_wechat_bound?}"
        puts "  - 微信OpenID: #{user.wechat_openid || '未绑定'}"
        puts "  - 微信通知开关: #{user.wechat_notification_enabled?}"
      else
        puts "✗ 没有找到用户进行测试"
      end

      # 检查通知器配置
      notifiers = [
        EventRegistrationNotifier,
        EventReminderNotifier,
        SystemNotifier
      ]

      notifiers.each do |notifier|
        delivery_methods = notifier.delivery_methods.map { |dm| dm.respond_to?(:name) ? dm.name : dm.to_s }
        if delivery_methods.include?("wechat") || delivery_methods.any? { |dm| dm.to_s.include?("wechat") }
          puts "✓ #{notifier.name} 已配置微信投递"
        else
          puts "✗ #{notifier.name} 未配置微信投递"
        end
      end

      puts "测试完成!"
    end

    desc "发送测试微信通知"
    task send_test: :environment do
      # 找到一个绑定了微信的用户
      user = User.joins(:omni_auth_identities)
                 .where(omni_auth_identities: { provider: "wechat" })
                 .first

      unless user
        puts "没有找到绑定微信的用户，请先绑定微信账号"
        exit 1
      end

      # 创建或找到一个事件
      event = Event.first || Event.create!(
        title: "测试活动",
        description: "这是一个测试活动",
        start_time: 1.day.from_now,
        end_time: 1.day.from_now + 2.hours,
        user: user
      )

      begin
        # 发送事件注册通知
        notification = EventRegistrationNotifier.with(
          event: event,
          record: event
        ).deliver(user)

        puts "✓ 测试通知发送成功"
        puts "  - 通知ID: #{notification.id}"
        puts "  - 接收用户: #{user.username}"
        puts "  - 微信OpenID: #{user.wechat_openid}"

      rescue => e
        puts "✗ 测试通知发送失败: #{e.message}"
        puts e.backtrace.first(5)
      end
    end

    desc "显示微信通知配置"
    task config: :environment do
      puts "微信通知配置信息:"
      puts "=" * 50

      # 显示微信账号配置
      puts "微信账号配置:"
      wechat_config = Wechat.config(:service)
      puts "  - AppID: #{wechat_config[:appid]}"
      puts "  - 是否启用加密: #{wechat_config[:encrypt_mode]}"
      puts "  - 可信域名: #{wechat_config[:trusted_domain_fullname]}"

      # 显示通知模板配置
      puts "\n通知模板配置:"
      template_config = Rails.application.config_for(:wechat_notification_templates) rescue {}
      template_config.each do |notification_type, config|
        puts "  - #{notification_type}:"
        puts "    模板ID: #{config['template_id']}"
        puts "    数据字段: #{config['data_keys']&.join(', ')}"
      end

      # 显示用户通知偏好示例
      puts "\n用户通知偏好示例:"
      user = User.first
      if user
        puts "  - 用户: #{user.username}"
        puts "  - 通知设置: #{user.notification_settings}"
        puts "  - 是否绑定微信: #{user.has_wechat_bound?}"
        puts "  - 微信通知开关: #{user.wechat_notification_enabled?}"
      end
    end

    desc "显示微信通知使用说明"
    task usage: :environment do
      puts <<~USAGE
        微信服务号通知功能使用说明
        ================================

        1. 基本配置
        -----------
        - 确保 config/wechat.yml 中配置了服务号信息
        - 在微信公众平台申请模板消息ID
        - 更新 config/wechat_notification_templates.yml 中的模板ID

        2. 用户绑定微信
        --------------
        用户需要通过 OAuth 绑定微信账号：
        - 访问 /auth/wechat/callback
        - 系统会自动创建 OmniAuthIdentity 记录

        3. 发送通知
        -----------
        在代码中使用通知器：

        # 发送事件注册通知
        EventRegistrationNotifier.with(
          event: @event,
          record: @event_registration
        ).deliver(@user)

        # 发送事件提醒通知
        EventReminderNotifier.with(
          event: @event,
          reminder_type: 'day_before'
        ).deliver(@user)

        # 发送系统通知
        SystemNotifier.with(
          message: "系统维护通知"
        ).deliver(@user)

        4. 用户偏好设置
        --------------
        用户可以在设置页面控制通知偏好：
        - 总体微信通知开关
        - 特定类型的微信通知开关

        5. 测试命令
        -----------
        rails wechat:notification:test        # 测试功能
        rails wechat:notification:send_test   # 发送测试通知
        rails wechat:notification:config      # 显示配置

        6. 注意事项
        -----------
        - 模板消息需要在微信公众平台申请
        - 用户必须先关注服务号才能接收消息
        - 模板消息有发送频率限制
        - 建议在生产环境使用环境变量配置模板ID
      USAGE
    end
  end
end
