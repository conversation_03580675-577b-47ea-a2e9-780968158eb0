require 'rails_helper'

RSpec.describe OmniAuthIdentity, type: :model do
  describe 'validations' do
    subject { build(:omni_auth_identity) }

    it { should validate_presence_of(:uid) }
    it { should validate_presence_of(:provider) }
    it { should validate_uniqueness_of(:uid).scoped_to(:provider) }
  end

  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'factories' do
    it 'has a valid default factory' do
      expect(build(:omni_auth_identity)).to be_valid
    end

    it 'has a valid google factory' do
      expect(build(:omni_auth_identity, :google)).to be_valid
    end

    it 'has a valid github factory' do
      expect(build(:omni_auth_identity, :github)).to be_valid
    end

    it 'has a valid wechat factory' do
      expect(build(:omni_auth_identity, :wechat)).to be_valid
    end
  end

  describe 'provider specific validations' do
    let(:user) { create(:user) }

    it 'can have multiple identities for the same user with different providers' do
      create(:omni_auth_identity, provider: 'github', user: user)
      wechat_identity = build(:omni_auth_identity, provider: 'wechat', user: user)

      expect(wechat_identity).to be_valid
    end

    it 'cannot have duplicate provider and uid combinations' do
      create(:omni_auth_identity, provider: 'github', uid: 'same_uid', user: user)
      duplicate_identity = build(:omni_auth_identity, provider: 'github', uid: 'same_uid', user: user)

      expect(duplicate_identity).not_to be_valid
      expect(duplicate_identity.errors[:uid]).to include("has already been taken")
    end

    it 'allows same uid for different providers' do
      create(:omni_auth_identity, provider: 'github', uid: 'same_uid', user: user)
      different_provider = build(:omni_auth_identity, provider: 'google_oauth2', uid: 'same_uid', user: user)

      expect(different_provider).to be_valid
    end
  end

  describe '.find_by_auth and .create_from_auth' do
    let(:user) { create(:user) }

    it 'creates and finds identity for normal provider' do
      auth = { 'provider' => 'github', 'uid' => '123456' }
      identity = OmniAuthIdentity.create_from_auth(auth, user)
      expect(identity.uid).to eq('123456')
      expect(identity.provider).to eq('github')
      found = OmniAuthIdentity.find_by_auth(auth)
      expect(found).to eq(identity)
    end

    it 'creates and finds identity for wechat with unionid' do
      auth = { 'provider' => 'wechat', 'uid' => 'wxuid', 'unionid' => 'wxunionid' }
      identity = OmniAuthIdentity.create_from_auth(auth, user)
      expect(identity.uid).to eq('wxuid')
      expect(identity.provider).to eq('wechat')
      expect(identity.unionid).to eq('wxunionid')
      found = OmniAuthIdentity.find_by_auth(auth)
      expect(found).to eq(identity)
    end

    it 'falls back to uid lookup for wechat when unionid missing' do
      auth = { 'provider' => 'wechat', 'uid' => 'wxuid_no_union' }
      identity = OmniAuthIdentity.create_from_auth(auth, user)
      found = OmniAuthIdentity.find_by_auth(auth)
      expect(found).to eq(identity)
    end
  end

  describe 'user relationship' do
    let(:user) { create(:user) }
    let(:identity) { create(:omni_auth_identity, user: user) }

    it 'is destroyed when user is destroyed' do
      identity # create the identity
      expect { user.destroy }.to change { OmniAuthIdentity.count }.by(-1)
    end
  end
end
