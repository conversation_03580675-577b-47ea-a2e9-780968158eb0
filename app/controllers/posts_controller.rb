class PostsController < ApplicationController
  # before_action :authenticate_user!
  before_action :set_post, only: [ :show, :edit, :update, :destroy, :publish, :unpublish ]
  before_action :ensure_owner!, only: [ :edit, :update, :destroy, :publish, :unpublish ]

  def index
    @posts = current_user.posts.order(created_at: :desc)
  end

  def show
  end

  def new
    @post = current_user.posts.build
  end

  def create
    @post = current_user.posts.build(post_params)
    if @post.save
      redirect_to @post, notice: "Post created"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @post.update(post_params)
      redirect_to @post, notice: "Post has been updated"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @post.destroy
    redirect_to posts_path, notice: "Post deleted"
  end

  def publish
    if @post.publish
      redirect_to @post, notice: "Post published"
    else
      redirect_to @post, alert: "Publishing failure"
    end
  end

  def unpublish
    if @post.unpublish
      redirect_to @post, notice: "The post has been turned into a draft"
    else
      redirect_to @post, alert: "Operation failure"
    end
  end

  private

  def set_post
    @post = Post.find(params[:id])
  end

  def ensure_owner!
    unless @post.user_id == current_user.id
      redirect_to posts_path, alert: "You do not have permission to do this"
    end
  end

  def post_params
    params.require(:post).permit(:title, :content, :status)
  end
end
