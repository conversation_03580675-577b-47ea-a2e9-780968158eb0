class BaseUnitsController < ApplicationController
  before_action :authenticated?
  before_action :set_base_unit, only: [ :show, :update, :destroy, :download, :convert_to_md ]
  rescue_from ActiveRecord::RecordNotFound, with: :record_not_found

  def index
    @base_units = current_user.base_units
                             .order(created_at: :desc)
    # .page(params[:page])
  end

  def show
  end

  def create
    @base_unit = current_user.base_units.new(base_unit_params)

    # 将文件处理逻辑移到模型
    @base_unit.file.attach(params[:file]) if params[:file].present?

    if @base_unit.save
      respond_to do |format|
        format.json { render json: { success: true } }
        format.turbo_stream do
          render turbo_stream: turbo_stream.append("base_units_list", partial: "base_unit", locals: { base_unit: @base_unit })
        end
      end
    else
      render_error_response
    end
  end

  def update
    if @base_unit.update(base_unit_params)
      render json: { success: true }
    else
      render json: {
        success: false,
        errors: @base_unit.errors.full_messages
      }, status: :unprocessable_entity
    end
  end

  def destroy
    if @base_unit
      @base_unit.destroy
      redirect_to base_units_path, notice: "File deleted successfully"
    else
      render json: { success: false, error: "Record not found" }, status: :not_found
    end
  end

  # 添加 download 动作
  def download
    if @base_unit && @base_unit.file.attached?
      response.headers["Content-Type"] = @base_unit.content_type
      response.headers["Content-Disposition"] = "attachment; filename=#{@base_unit.name}"
      send_data @base_unit.file.download, filename: @base_unit.name, type: @base_unit.content_type, disposition: "attachment"
    else
      render json: { success: false, error: "File not found" }, status: :not_found
    end
  end

  def convert_to_md
    begin
      return redirect_to base_units_path, notice: "The file has been converted" if @base_unit.posts.exists?

      @base_unit.convert_to_markdown!
      redirect_to base_units_path, notice: "File converted to Markdown successfully"
    rescue BaseUnit::ConversionError => e
      render json: { success: false, error: e.message }, status: :unprocessable_entity
    rescue StandardError => e
      render json: { success: false, error: "转换时发生错误: #{e.message}" }, status: :internal_server_error
    end
  end

  private

  def set_base_unit
    @base_unit = current_user.base_units.find(params[:id])
  end

  def record_not_found
    render json: { success: false, error: "Record not found" }, status: :not_found
  end

  def render_error_response
    error_messages = @base_unit.errors.full_messages
    storage_info = {
      used: current_user.storage_used,
      limit: current_user.storage_limit,
      available: current_user.storage_available
    }

    render json: { errors: error_messages, storage_info: storage_info },
           status: :unprocessable_entity
  end

  def base_unit_params
    return {} unless params[:base_unit].present?
    params.require(:base_unit).permit(
      :name, :content_type, :file_size, :checksum, :status, :metadata,
      :file # 允许直接通过params传递文件
    )
  end
end
