class CreateKnowledgeBases < ActiveRecord::Migration[8.0]
  def change
    create_table :knowledge_bases do |t|
      t.string :name, null: false
      t.integer :visibility, null: false, default: 0
      t.references :owner, null: false, foreign_key: { to_table: :users }
      t.text :description
      t.text :metadata, default: '{}'
      t.datetime :deleted_at
      t.boolean :archived, default: false

      t.timestamps
    end

    # 常用查询索引
    add_index :knowledge_bases, :visibility # 优化可见性查询
    add_index :knowledge_bases, :deleted_at # 优化软删除查询
    add_index :knowledge_bases, :archived # 优化归档查询

    # 唯一性约束
    add_index :knowledge_bases, [ :name, :deleted_at ], unique: true
    add_index :knowledge_bases, [ :visibility, :owner_id ], name: 'index_knowledge_bases_on_visibility_and_owner_id'
  end
end
