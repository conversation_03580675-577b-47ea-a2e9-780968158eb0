# frozen_string_literal: true

# Used by wechat gems, do not rename WechatSession to other name,
# Feel free to inherit from other class like ActiveModel::Model
class WechatSession < ApplicationRecord
  validates :openid, presence: true, uniqueness: true
  # don't using serialize if possible, see README `on :fallback`
  # Allow common classes needed for session storage, including TimeWithZone and TimeZone
  serialize :hash_store, type: Hash, coder: YAML, yaml: { permitted_classes: [ String, Symbol, Time, ActiveSupport::TimeWithZone, ActiveSupport::TimeZone ] }

  # called by wechat gems when user request session
  def self.find_or_initialize_session(request_message)
    find_or_initialize_by(openid: request_message[:from_user_name])
  end

  # called by wechat gems after response Techent server at controller#create
  def save_session(_response_message)
    touch unless new_record? # Always refresh updated_at even no change
    save!
  end
end
