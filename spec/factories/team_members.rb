FactoryBot.define do
  factory :team_member do
    association :team
    association :user
    role { :member }

    trait :admin do
      role { :admin }
    end

    trait :with_view_access do
      after(:create) do |tm|
        create(:team_resource_access,
              team_member: tm,
              role: :viewer)
      end
    end

    trait :with_edit_access do
      after(:create) do |tm|
        create(:team_resource_access,
              team_member: tm,
              role: :editor)
      end
    end

    trait :with_maintain_access do
      after(:create) do |tm|
        create(:team_resource_access,
              team_member: tm,
              role: :maintainer)
      end
    end
  end
end
