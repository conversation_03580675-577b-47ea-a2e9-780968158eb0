<%# Sidebar %>
<div>
  <aside id="sidebar"
         data-sidebar-target="sidebar"
         class="fixed top-0 left-0 z-20 flex flex-col shrink-0 hidden w-64 h-full pt-16 font-normal duration-75 lg:flex transition-width"
         aria-label="Sidebar">
    <div class="relative flex flex-col flex-1 min-h-0 pt-0 bg-white border-r border-gray-200 dark:bg-gray-800 dark:border-gray-700">
      <%# Restored pt-5 %>
      <div class="flex flex-col flex-1 pt-5 pb-4 overflow-y-auto">
        <div class="flex-1 px-3 space-y-1 bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
          <ul class="pb-2 space-y-2">
            <li>
              <form action="#" method="GET" class="lg:hidden">
                <label for="mobile-search" class="sr-only"><%= t('layouts.dashboard.sidebar.search.mobile_label') %></label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg class="w-5 h-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg>
                  </div>
                  <input type="text" name="search" id="mobile-search" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-gray-200 dark:focus:ring-primary-500 dark:focus:border-primary-500" placeholder="<%= t('layouts.dashboard.sidebar.search.placeholder') %>">
                </div>
              </form>
            </li>
            <%# Dashboard %>
            <li>
              <%= link_to root_path, class: "flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700" do %>
                <svg class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                  <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                </svg>
                <span class="ml-3"><%= t('layouts.dashboard.sidebar.dashboard') %></span>
              <% end %>
            </li>

            <!-- AI 相关入口 -->
            <li>
              <button type="button" class="flex items-center w-full p-2 text-base text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700" aria-controls="dropdown-ai" data-collapse-toggle="dropdown-ai">
                <svg class="shrink-0 w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                </svg>
                <span class="flex-1 ml-3 text-left whitespace-nowrap"><%= t('layouts.dashboard.sidebar.ai.title') %></span>
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <ul id="dropdown-ai" class="hidden py-2 space-y-2">
                <li>
                  <%= link_to t('ai.assistants.title'), ai_assistants_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %> <%# Reusing functional key %>
                </li>
                <li>
                  <%= link_to t('ai.conversations.title'), latest_ai_conversations_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %> <%# Reusing functional key %>
                </li>
              </ul>
            </li>

            <!-- Events 管理 -->
            <li>
              <button type="button" class="flex items-center w-full p-2 text-base text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700" aria-controls="dropdown-events" data-collapse-toggle="dropdown-events">
                <svg class="shrink-0 w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 012-2v12a2 2 0 002 2z"/>
                </svg>
                <span class="flex-1 ml-3 text-left whitespace-nowrap"><%= t('events.index.title') %></span>
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <ul id="dropdown-events" class="hidden py-2 space-y-2">
                <li>
                  <%= link_to t('events.index.all'), events_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %>
                </li>
                <li>
                  <%= link_to t('events.calendar.title'), calendar_events_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %>
                </li>
                <li>
                  <%= link_to t('common.views.map'), events_path(view: 'map'), class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %>
                </li>
                <% if authenticated? %>
                  <li>
                    <%= link_to t('events.my_events'), events_path(created_by: 'me'), class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %>
                  </li>
                  <li>
                    <%= link_to t('events.my_registrations'), events_path(registered: 'me'), class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %>
                  </li>
                  <% if allowed_to?(:create?, Event) %>
                    <li>
                      <%= link_to t('events.new.title'), new_event_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %>
                    </li>
                  <% end %>
                <% end %>
              </ul>
            </li>

            <!-- 知识库管理 -->
            <li>
              <button type="button" class="flex items-center w-full p-2 text-base text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700" aria-controls="dropdown-knowledgebase" data-collapse-toggle="dropdown-knowledgebase">
                <svg class="shrink-0 w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10a2 2 0 012 2v9a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2m1 1H8a1 1 0 0 1-1 1v9a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1h2.586a1 1 0 0 1 .707.293l2.414 2.414A1 1 0 0 1 7 10.586V7z"/>
                </svg>
                <span class="flex-1 ml-3 text-left whitespace-nowrap"><%= t('layouts.dashboard.sidebar.knowledge_bases.title') %></span>
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <ul id="dropdown-knowledgebase" class="hidden py-2 space-y-2">
                <li>
                  <%= link_to t('knowledge_bases.index.title'), knowledge_bases_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %> <%# Reusing functional key %>
                </li>
                <li>
                  <%= link_to t('knowledge_bases.index.archived_title'), archived_knowledge_bases_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %> <%# Reusing functional key %>
                </li>
                <li>
                  <%= link_to t('knowledge_bases.actions.create'), new_knowledge_base_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %> <%# Reusing functional key %>
                </li>
              </ul>
            </li>


            <!-- 我的内容 -->
            <li>
              <%= link_to posts_path, class: "flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700" do %>
                <svg class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <span class="ml-3"><%= t('layouts.dashboard.sidebar.my_post') %></span>
              <% end %>
            </li>

            <!-- 文件管理 -->
            <li>
              <%= link_to base_units_path, class: "flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700" do %>
                <svg class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                <span class="ml-3"><%= t('layouts.dashboard.sidebar.base_units') %></span>
              <% end %>
            </li>

            <!-- 账户设置 -->
            <li>
              <button type="button" class="flex items-center w-full p-2 text-base text-gray-900 transition duration-75 rounded-lg group hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700" aria-controls="dropdown-settings" data-collapse-toggle="dropdown-settings">
                <svg class="shrink-0 w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                </svg>
                <span class="flex-1 ml-3 text-left whitespace-nowrap"><%= t('layouts.dashboard.sidebar.account_settings.title') %></span>
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <ul id="dropdown-settings" class="hidden py-2 space-y-2">
                <li>
                  <%= link_to t('users.profile.title'), settings_users_path, class: "text-base text-gray-900 rounded-lg flex items-center p-2 group hover:bg-gray-100 transition duration-75 pl-11 dark:text-gray-200 dark:hover:bg-gray-700" %> <%# Reusing functional key %>
                </li>
              </ul>
            </li>
          </ul>

          <!-- 帮助文档 -->
          <div class="pt-2 space-y-2">
            <%= link_to about_path, class: "flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700" do %>
              <svg class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12h-1M4 12H3m3.343-5.657l-.707-.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
              </svg>
              <span class="ml-3"><%= t('static_pages.about.title') %></span> <%# Reusing functional key %>
            <% end %>

            <%= link_to terms_path, class: "flex items-center p-2 text-base text-gray-900 rounded-lg hover:bg-gray-100 group dark:text-gray-200 dark:hover:bg-gray-700" do %>
              <svg class="w-6 h-6 text-gray-500 transition duration-75 group-hover:text-gray-900 dark:text-gray-400 dark:group-hover:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
              </svg>
              <span class="ml-3"><%= t('static_pages.terms.title') %></span> <%# Reusing functional key %>
            <% end %>
          </div>
        </div>
      </div>

      <%# Bottom Menu %>
      <div class="absolute bottom-0 left-0 justify-center hidden w-full p-4 space-x-4 bg-white lg:flex dark:bg-gray-800">
        <%# Add data-preferences-target for Stimulus controller to the button %>
        <button type="button" data-dropdown-toggle="language-dropdown" class="inline-flex justify-center p-2 text-gray-500 rounded-xs cursor-pointer hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">
          <%# Add a specific target for the SVG icon itself %>
          <span data-preferences-target="languageIconSvg">
            <%# Dynamically render flag based on current locale %>
            <% svg_attrs = { class: "h-5 w-5 rounded-full mt-0.5", xmlns: "http://www.w3.org/2000/svg", viewBox: "0 0 512 512" } %>
            <% case I18n.locale.to_sym %>
          <% when :zh %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-cn")) %>>
              <defs>
                <path id="a" fill="#ffde00" d="M1-.3L-.7.8 0-1 .6.8-1-.3z"/>
              </defs>
              <path fill="#de2910" d="M0 0h512v512H0z"/>
              <use width="30" height="20" transform="matrix(76.8 0 0 76.8 128 128)" xlink:href="#a"/>
              <use width="30" height="20" transform="rotate(-121 142.6 -47) scale(25.5827)" xlink:href="#a"/>
              <use width="30" height="20" transform="rotate(-98.1 198 -82) scale(25.6)" xlink:href="#a"/>
              <use width="30" height="20" transform="rotate(-74 272.4 -114) scale(25.6137)" xlink:href="#a"/>
              <use width="30" height="20" transform="matrix(16 -19.968 19.968 16 256 230.4)" xlink:href="#a"/>
            </svg>
          <% when :en %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-us")) %>>
              <g fill-rule="evenodd">
                <g stroke-width="1pt">
                  <path fill="#bd3d44" d="M0 0h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z" transform="scale(3.9385)"/>
                  <path fill="#fff" d="M0 10h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z" transform="scale(3.9385)"/>
                </g>
                <path fill="#192f5d" d="M0 0h98.8v70H0z" transform="scale(3.9385)"/>
              </g>
            </svg>
          <% when :fr %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-fr")) %>>
              <g fill-rule="evenodd" stroke-width="1pt">
                <path fill="#fff" d="M0 0h512v512H0z"/>
                <path fill="#00267f" d="M0 0h170.7v512H0z"/>
                <path fill="#f31830" d="M341.3 0H512v512H341.3z"/>
              </g>
            </svg>
          <% when :de %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-de")) %>>
              <path fill="#ffce00" d="M0 341.3h512V512H0z"/>
              <path d="M0 0h512v170.7H0z"/>
              <path fill="#d00" d="M0 170.7h512v170.6H0z"/>
            </svg>
          <% when :es %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-es")) %>>
              <path fill="#F1BF00" d="M0 128h512v256H0z"/>
              <path fill="#AD1519" d="M0 0h512v128H0zm0 384h512v128H0z"/>
            </svg>
          <% when :th %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-th")) %>>
              <g fill-rule="evenodd">
                <path fill="#f4f5f8" d="M0 0h512v512H0z"/>
                <path fill="#2d2a4a" d="M0 173.4h512v165.2H0z"/>
                <path fill="#a51931" d="M0 0h512v88H0zm0 426.7h512V512H0z"/>
              </g>
            </svg>
          <% when :ja %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-jp")) %>>
              <circle cx="256" cy="256" r="170.7" fill="#fff"/>
              <circle cx="256" cy="256" r="113.8" fill="#d30000"/>
            </svg>
          <% when :ko %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-kr")) %>>
              <path fill="#fff" d="M0 0h512v512H0z"/>
              <circle cx="256" cy="256" r="85.3" fill="#cd2e3a"/>
              <path fill="#0047a0" d="M256 170.7a85.3 85.3 0 0 0 0 170.6 42.7 42.7 0 1 0 0-85.3 42.7 42.7 0 1 0 0-85.3z"/>
              <g fill="#000">
                <path d="M102.4 102.4h64v42.7h-64zm0 64h64v42.7h-64zm0 64h64v42.7h-64z"/>
                <path d="M345.6 102.4h64v42.7h-64zm0 64h32v42.7h-32zm0-64h-32v42.7h32zm0 128h64v42.7h-64z"/>
                <path d="M102.4 307.2h32v42.7h-32zm0-64h-32v42.7h32zm0 128h64v42.7h-64zm0-64h64v42.7h-64z"/>
                <path d="M345.6 307.2h32v42.7h-32zm0-64h-32v42.7h32zm0 128h32v42.7h-32zm0-64h-32v42.7h32z"/>
              </g>
            </svg>
          <% when :ru %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-ru")) %>>
              <g fill-rule="evenodd">
                <path fill="#fff" d="M0 0h512v512H0z"/>
                <path fill="#0039a6" d="M0 170.7h512v341.3H0z"/>
                <path fill="#d52b1e" d="M0 341.3h512V512H0z"/>
              </g>
            </svg>
          <% when :ar %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-sa")) %>>
              <path fill="#006c35" d="M0 0h512v512H0z"/>
              <text transform="scale(.168 .21) translate(150 1900)" fill="#fff" font-family="sans-serif" font-size="800px" stroke-width="1pt" xml:space="preserve"><tspan x="0" y="0">لَا إِلٰهَ إِلَّا الله مُحَمَّدٌ رَسُولُ الله</tspan></text>
              <path fill="none" stroke="#fff" stroke-width="15.6" d="M48.1 380H464"/>
              <path fill="#fff" d="M48.1 380l-16-10.4 16-10.4v20.8z"/>
            </svg>
          <% when :vi %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-vn")) %>>
              <path fill="#da251d" d="M0 0h512v512H0z"/>
              <path fill="#ff0" d="M314.6 209.8L256 251.7l-58.6-41.9 22.4 67.9-58.6 41.9h72.5l22.4 67.9 22.4-67.9h72.5l-58.6-41.9z"/>
            </svg>
          <% else %>
            <%# Default to Chinese flag if locale is unknown %>
            <svg <%= tag.attributes(svg_attrs.merge(id: "flag-icon-css-cn")) %>>
              <defs>
                <path id="a" fill="#ffde00" d="M1-.3L-.7.8 0-1 .6.8-1-.3z"/>
              </defs>
              <path fill="#de2910" d="M0 0h512v512H0z"/>
              <use width="30" height="20" transform="matrix(76.8 0 0 76.8 128 128)" xlink:href="#a"/>
              <use width="30" height="20" transform="rotate(-121 142.6 -47) scale(25.5827)" xlink:href="#a"/>
              <use width="30" height="20" transform="rotate(-98.1 198 -82) scale(25.6)" xlink:href="#a"/>
              <use width="30" height="20" transform="rotate(-74 272.4 -114) scale(25.6137)" xlink:href="#a"/>
              <use width="30" height="20" transform="matrix(16 -19.968 19.968 16 256 230.4)" xlink:href="#a"/>
            </svg>
            <% end %>
          </span>
        </button>

        <%# Language Dropdown %>
        <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded-xs shadow-sm dark:bg-gray-700" id="language-dropdown">
          <ul class="py-1" role="none">
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'zh' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-cn" viewBox="0 0 512 512">
                    <defs>
                      <path id="a" fill="#ffde00" d="M1-.3L-.7.8 0-1 .6.8-1-.3z"/>
                    </defs>
                    <path fill="#de2910" d="M0 0h512v512H0z"/>
                    <use width="30" height="20" transform="matrix(76.8 0 0 76.8 128 128)" xlink:href="#a"/>
                    <use width="30" height="20" transform="rotate(-121 142.6 -47) scale(25.5827)" xlink:href="#a"/>
                    <use width="30" height="20" transform="rotate(-98.1 198 -82) scale(25.6)" xlink:href="#a"/>
                    <use width="30" height="20" transform="rotate(-74 272.4 -114) scale(25.6137)" xlink:href="#a"/>
                    <use width="30" height="20" transform="matrix(16 -19.968 19.968 16 256 230.4)" xlink:href="#a"/>
                  </svg>
                  <%= t('languages.zh') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'en' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-us" viewBox="0 0 512 512">
                    <g fill-rule="evenodd">
                      <g stroke-width="1pt">
                        <path fill="#bd3d44" d="M0 0h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z" transform="scale(3.9385)"/>
                        <path fill="#fff" d="M0 10h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z" transform="scale(3.9385)"/>
                      </g>
                      <path fill="#192f5d" d="M0 0h98.8v70H0z" transform="scale(3.9385)"/>
                    </g>
                  </svg>
                  <%= t('languages.en') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'fr' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-fr" viewBox="0 0 512 512">
                    <g fill-rule="evenodd" stroke-width="1pt">
                      <path fill="#fff" d="M0 0h512v512H0z"/>
                      <path fill="#00267f" d="M0 0h170.7v512H0z"/>
                      <path fill="#f31830" d="M341.3 0H512v512H341.3z"/>
                    </g>
                  </svg>
                  <%= t('languages.fr') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'de' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-de" viewBox="0 0 512 512">
                    <path fill="#ffce00" d="M0 341.3h512V512H0z"/>
                    <path d="M0 0h512v170.7H0z"/>
                    <path fill="#d00" d="M0 170.7h512v170.6H0z"/>
                  </svg>
                  <%= t('languages.de') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'es' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-es" viewBox="0 0 512 512">
                    <path fill="#F1BF00" d="M0 128h512v256H0z"/>
                    <path fill="#AD1519" d="M0 0h512v128H0zm0 384h512v128H0z"/>
                  </svg>
                  <%= t('languages.es') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'th' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-th" viewBox="0 0 512 512">
                    <g fill-rule="evenodd">
                      <path fill="#f4f5f8" d="M0 0h512v512H0z"/>
                      <path fill="#2d2a4a" d="M0 173.4h512v165.2H0z"/>
                      <path fill="#a51931" d="M0 0h512v88H0zm0 426.7h512V512H0z"/>
                    </g>
                  </svg>
                  <%= t('languages.th') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'ja' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <%# Updated Japanese Flag SVG %>
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-jp" viewBox="0 0 512 512">
                    <path fill="#fff" d="M0 0h512v512H0z"/>
                    <circle cx="256" cy="256" r="230" fill="#d30000"/>
                  </svg>
                  <%= t('languages.ja') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'ko' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <%# Corrected Korean Flag SVG v2 (Taegeukgi) %>
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-kr" viewBox="0 0 512 512">
                    <path fill="#fff" d="M0 0h512v512H0z"/>
                    <!-- Taegeuk -->
                    <g transform="translate(256 256)">
                      <circle r="128" fill="#CD2E3A"/>
                      <path d="M0-128a128 128 0 0 1 0 256 64 64 0 0 1 0-128 64 64 0 0 1 0-128z" fill="#0047A0"/>
                    </g>
                    <!-- Trigrams -->
                    <g fill="#000">
                      <% bar_h = 32 %>
                      <% gap = 16 %>
                      <% trigram_w = 128 %>
                      <% trigram_h = 128 %> <%# 3*bar_h + 2*gap %>
                      <% center_dist_diag = 181 %> <%# Approx 128 * sqrt(2) %>
                      <% center_offset = center_dist_diag / Math.sqrt(2) %> <%# Approx 128 %>
                      <% bar_w_broken = (trigram_w - gap) / 2 %>

                      <!-- Geon (☰) - Top Left -->
                      <g transform="translate(<%= 256 - center_offset %>, <%= 256 - center_offset %>) rotate(-45) translate(-<%= trigram_w/2 %>, -<%= trigram_h/2 %>)">
                        <rect y="0" width="<%= trigram_w %>" height="<%= bar_h %>"/>
                        <rect y="<%= bar_h + gap %>" width="<%= trigram_w %>" height="<%= bar_h %>"/>
                        <rect y="<%= 2 * (bar_h + gap) %>" width="<%= trigram_w %>" height="<%= bar_h %>"/>
                      </g>
                      <!-- Ri (☲) - Bottom Left -->
                      <g transform="translate(<%= 256 - center_offset %>, <%= 256 + center_offset %>) rotate(45) translate(-<%= trigram_w/2 %>, -<%= trigram_h/2 %>)">
                        <rect y="0" width="<%= trigram_w %>" height="<%= bar_h %>"/>
                        <rect x="0" y="<%= bar_h + gap %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="<%= bar_w_broken + gap %>" y="<%= bar_h + gap %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect y="<%= 2 * (bar_h + gap) %>" width="<%= trigram_w %>" height="<%= bar_h %>"/>
                      </g>
                      <!-- Gam (☵) - Top Right -->
                      <g transform="translate(<%= 256 + center_offset %>, <%= 256 - center_offset %>) rotate(45) translate(-<%= trigram_w/2 %>, -<%= trigram_h/2 %>)">
                        <rect x="0" y="0" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="<%= bar_w_broken + gap %>" y="0" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect y="<%= bar_h + gap %>" width="<%= trigram_w %>" height="<%= bar_h %>"/>
                        <rect x="0" y="<%= 2 * (bar_h + gap) %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="<%= bar_w_broken + gap %>" y="<%= 2 * (bar_h + gap) %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                      </g>
                      <!-- Gon (☷) - Bottom Right -->
                      <g transform="translate(<%= 256 + center_offset %>, <%= 256 + center_offset %>) rotate(-45) translate(-<%= trigram_w/2 %>, -<%= trigram_h/2 %>)">
                        <rect x="0" y="0" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="<%= bar_w_broken + gap %>" y="0" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="0" y="<%= bar_h + gap %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="<%= bar_w_broken + gap %>" y="<%= bar_h + gap %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="0" y="<%= 2 * (bar_h + gap) %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                        <rect x="<%= bar_w_broken + gap %>" y="<%= 2 * (bar_h + gap) %>" width="<%= bar_w_broken %>" height="<%= bar_h %>"/>
                      </g>
                    </g>
                  </svg>
                  <%= t('languages.ko') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'ru' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-ru" viewBox="0 0 512 512">
                    <g fill-rule="evenodd">
                      <path fill="#fff" d="M0 0h512v512H0z"/>
                      <path fill="#0039a6" d="M0 170.7h512v341.3H0z"/>
                      <path fill="#d52b1e" d="M0 341.3h512V512H0z"/>
                    </g>
                  </svg>
                  <%= t('languages.ru') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'ar' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-sa" viewBox="0 0 512 512">
                    <path fill="#006c35" d="M0 0h512v512H0z"/>
                    <text transform="scale(.168 .21) translate(150 1900)" fill="#fff" font-family="sans-serif" font-size="800px" stroke-width="1pt" xml:space="preserve"><tspan x="0" y="0">لَا إِلٰهَ إِلَّا الله مُحَمَّدٌ رَسُولُ الله</tspan></text>
                    <path fill="none" stroke="#fff" stroke-width="15.6" d="M48.1 380H464"/>
                    <path fill="#fff" d="M48.1 380l-16-10.4 16-10.4v20.8z"/>
                  </svg>
                  <%= t('languages.ar') %>
                </div>
              <% end %>
            </li>
            <li>
              <%= link_to update_preferences_users_path(preferences: { language: 'vi' }), class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white", data: { turbo_method: :patch } do %>
                <div class="inline-flex items-center">
                  <svg class="h-3.5 w-3.5 rounded-full mr-2" xmlns="http://www.w3.org/2000/svg" id="flag-icon-css-vn" viewBox="0 0 512 512">
                    <path fill="#da251d" d="M0 0h512v512H0z"/>
                    <path fill="#ff0" d="M314.6 209.8L256 251.7l-58.6-41.9 22.4 67.9-58.6 41.9h72.5l22.4 67.9 22.4-67.9h72.5l-58.6-41.9z"/>
                  </svg>
                  <%= t('languages.vi') %>
                </div>
              <% end %>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </aside>

  <%# Kept id as sidebarBackdrop, restored original z-index, added Stimulus action and target %>
  <div data-action="click->sidebar#toggle"
       data-sidebar-target="backdrop"
       aria-hidden="true"
       class="fixed inset-0 z-10 hidden bg-gray-900/50 dark:bg-gray-900/90"
       id="sidebarBackdrop"></div>

</div>
