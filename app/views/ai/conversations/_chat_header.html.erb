<div class="sticky top-0 z-10 flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
  <div class="flex items-center space-x-3">
    <!-- 标题和模型信息 -->
    <div>
      <h1 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate max-w-[200px]"><%= conversation.title || t('ai.conversations.new') %></h1>
      <% if conversation.current_assistant %>
        <div class="flex items-center mt-1">
          <div class="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mr-2"></div>
          <span class="text-xs text-gray-500 dark:text-gray-400"><%= conversation.current_assistant.name %></span>
        </div>
      <% end %>
    </div>
  </div>
  
  <div class="flex items-center space-x-2">
    <!-- 设置按钮 -->
    <button class="p-2 text-gray-500 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" title="<%= t('ai.conversations.chat.title') %>">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      </svg>
    </button>
    
    <!-- 新对话按钮 -->
    <%= link_to new_ai_conversation_path, class: "inline-flex items-center px-4 py-2 bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 text-white font-medium rounded-lg text-sm transition-colors duration-200", title: t('ai.conversations.chat.new_chat') do %>
      <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
      </svg>
      <%= t('ai.conversations.chat.new_chat') %>
    <% end %>
    
    <!-- 助手下拉菜单 -->
    <div class="relative">
      <button id="assistantDropdownButton" data-dropdown-toggle="assistantDropdown" data-dropdown-trigger="click" data-dropdown-delay="300" class="flex items-center space-x-2 p-2 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
        <span class="text-sm font-medium">
          <% if conversation.current_assistant %>
            <%= conversation.current_assistant.name %>
          <% else %>
            Default Assistant
          <% end %>
        </span>
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>
      
      <!-- 助手下拉菜单内容 -->
      <div id="assistantDropdown" class="hidden absolute right-0 mt-2 w-56 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black/5 dark:ring-white/10 z-10 divide-y divide-gray-100 dark:divide-gray-700">
        <!-- 助手下拉菜单标题 -->
        <div class="px-4 py-3 text-sm text-gray-900 dark:text-white">
          <h3 class="font-medium"><%= t('ai.conversations.chat.assistant_selection') %></h3>
          <p class="text-xs text-gray-500 dark:text-gray-400"><%= t('ai.conversations.chat.assistant_prompt') %></p>
        </div>
        
        <!-- 助手列表 -->
        <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="assistantDropdownButton">
          <% current_user.assistants.each do |assistant| %>
            <li>
              <%= button_to switch_assistant_ai_conversation_path(conversation, assistant_id: assistant.id), method: :post, class: "w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 #{assistant.id == conversation.current_assistant&.id ? 'bg-gray-100 dark:bg-gray-700' : ''}" do %>
                <div class="flex items-center">
                  <% if assistant.id == conversation.current_assistant&.id %>
                    <svg class="w-4 h-4 mr-2 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  <% else %>
                    <div class="w-4 h-4 mr-2"></div>
                  <% end %>
                  <%= assistant.name %>
                </div>
              <% end %>
            </li>
          <% end %>
        </ul>
        
        <!-- 分割线 -->
        <div class="py-1">
          <%= link_to new_ai_assistant_path, class: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600" do %>
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              <%= t('ai.conversations.chat.create_assistant') %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- 历史消息图标按钮 -->
    <button data-drawer-target="history-drawer" data-drawer-toggle="history-drawer" data-drawer-placement="right" aria-controls="history-drawer" class="p-2 text-gray-500 dark:text-gray-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700" title="<%= t('ai.conversations.chat.view_history') %>">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
      </svg>
    </button>
  </div>
</div>
