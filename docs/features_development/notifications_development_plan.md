# 开发计划：站内实时通知

## 基于 Noticed 2.0 的实现方案

### 1. 初始化通知系统
- 确认 `Gemfile` 中已经添加了 `noticed` gem（版本 ~> 2.0）。
- 运行迁移生成命令：
  ```bash
  rails noticed:install:migrations
  rails db:migrate
  ```
- 确保数据库中有 `noticed_events` 和 `noticed_notifications` 两张表。

### 2. 创建 Notifier 类
- 使用生成器创建 Notifier：
  ```bash
  rails generate noticed:notifier CommentNotifier
  rails generate noticed:notifier EventRegistrationNotifier
  rails generate noticed:notifier SystemNotifier
  ```
- Notifier 继承自 `Noticed::Event`，代表一个通知事件。
- 在 Notifier 中配置多种投递方式（delivery methods）：
  ```ruby
  class CommentNotifier < Noticed::Event
    deliver_by :action_cable do |config|
      config.channel = "NotificationChannel"
      config.stream = -> { "notifications:#{recipient.id}" }
    end
    
    deliver_by :database
    
    deliver_by :email do |config|
      config.mailer = "NotificationMailer"
      config.if = -> { recipient.email_notifications? }
    end
    
    notification_methods do
      def message
        t(".message", user: params[:commenter].name)
      end
      
      def url
        post_path(params[:post])
      end
    end
  end
  ```

### 3. 扩展用户模型
- 添加通知关联：
  ```ruby
  class User < ApplicationRecord
    has_many :notifications, as: :recipient, dependent: :destroy, class_name: "Noticed::Notification"
  end
  ```
- 实现 `notification_enabled?` 方法检查用户通知偏好。
- 添加通知相关的 scope 和辅助方法：
  ```ruby
  def unread_notifications
    notifications.unread
  end
  
  def mark_notifications_as_read
    notifications.unread.mark_as_read
  end
  ```

### 4. 实现通知触发逻辑
- 在适当的地方触发通知：
  ```ruby
  # 在评论控制器中
  CommentNotifier.with(
    record: @comment,
    post: @post,
    commenter: current_user
  ).deliver_later(@post.author)
  
  # 批量发送给多个用户
  CommentNotifier.with(record: @comment).deliver(@post.subscribers)
  ```
- 支持延迟发送和条件发送。

### 5. 集成 Solid Cable 实现实时通知
- 确认 `config/cable.yml` 和 `database.yml` 已正确配置了 Solid Cable。
- 创建 NotificationChannel：
  ```ruby
  # app/channels/notification_channel.rb
  class NotificationChannel < ApplicationCable::Channel
    def subscribed
      stream_for current_user
    end
    
    def mark_as_read(data)
      notification = current_user.notifications.find(data['id'])
      notification.mark_as_read
    end
  end
  ```
- 在 Notifier 中配置 ActionCable 投递：
  ```ruby
  deliver_by :action_cable do |config|
    config.channel = "NotificationChannel"
    config.stream = -> { recipient }
    config.message = -> {
      {
        id: notification.id,
        type: notification.type,
        message: message,
        url: url,
        created_at: notification.created_at
      }
    }
  end
  ```

### 6. 前端展示通知
- 在 navbar 中渲染 `_notification_dropdown.html.erb` 部分。
- 确保 dropdown 中的内容是动态的，以显示用户的实际通知。
- 使用 Stimulus 控制器处理：
  - WebSocket 连接和订阅 NotificationChannel
  - 接收实时通知并更新 UI
  - 处理通知的标记已读功能
  - 实现通知铃铛的实时数字更新

### 7. 测试和调试
- 为通知的生成和展示编写自动化测试。
- 测试通知细节和用户偏好设置。
- 测试 WebSocket 连接的稳定性和实时性。

### 8. 部署与上线
- 部署前确保所有依赖和迁移都已就绪。
- 部署上线后，监控实际的使用情况和用户反馈以优化用户体验。
