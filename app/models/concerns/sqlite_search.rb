module SqliteSearch
  extend ActiveSupport::Concern

  included do
    # 存储搜索字段配置
    class_attribute :search_scope_attrs, default: []
    class_attribute :fts_tokenize_config, default: "unicode61"

    # 设置回调
    after_save_commit :update_search_index
    after_destroy_commit :delete_search_index

    # 定义全文搜索作用域
    scope :full_search, ->(query) {
      return none if query.blank?

      begin
        sanitized_query = connection.quote(prepare_fts_query(query))
        sql = <<~SQL.strip
          SELECT #{scope_foreign_key} AS id FROM #{table_name}_fts
          WHERE #{table_name}_fts MATCH #{sanitized_query} ORDER BY rank;
        SQL
        ids = connection.execute(sql).map { |row| row["id"] }
        where(id: ids)
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "FTS5 search failed for #{table_name}, falling back to LIKE search: #{e.message}"
        fallback_search(query)
      end
    }

    # 短语搜索
    scope :phrase_search, ->(phrase) {
      return none if phrase.blank?

      begin
        sanitized_phrase = connection.quote("\"#{phrase.strip}\"")
        sql = <<~SQL.strip
          SELECT #{scope_foreign_key} AS id FROM #{table_name}_fts
          WHERE #{table_name}_fts MATCH #{sanitized_phrase} ORDER BY rank;
        SQL
        ids = connection.execute(sql).map { |row| row["id"] }
        where(id: ids)
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "FTS5 phrase search failed for #{table_name}: #{e.message}"
        fallback_search(phrase)
      end
    }

    # 字段特定搜索
    scope :search_in_field, ->(field, query) {
      return none if query.blank? || !search_scope_attrs.map(&:to_s).include?(field.to_s)

      begin
        sanitized_field_query = connection.quote("#{field}:#{prepare_fts_query(query)}")
        sql = <<~SQL.strip
          SELECT #{scope_foreign_key} AS id FROM #{table_name}_fts
          WHERE #{table_name}_fts MATCH #{sanitized_field_query} ORDER BY rank;
        SQL
        ids = connection.execute(sql).map { |row| row["id"] }
        where(id: ids)
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "FTS5 field search failed for #{table_name}: #{e.message}"
        fallback_search(query)
      end
    }

    # 高级搜索 - 支持 AND, OR, NOT 操作符
    scope :advanced_search, ->(query) {
      return none if query.blank?

      begin
        sanitized_query = connection.quote(query.strip)
        sql = <<~SQL.strip
          SELECT #{scope_foreign_key} AS id FROM #{table_name}_fts
          WHERE #{table_name}_fts MATCH #{sanitized_query} ORDER BY rank;
        SQL
        ids = connection.execute(sql).map { |row| row["id"] }
        where(id: ids)
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "FTS5 advanced search failed for #{table_name}: #{e.message}"
        fallback_search(query)
      end
    }

    # 带相关性评分的搜索（用于需要排序的场景）
    scope :full_search_with_rank, ->(query) {
      return none if query.blank?

      begin
        # 使用 JOIN 方式保留 rank 值
        fts_table = "#{table_name}_fts"
        foreign_key = scope_foreign_key
        prepared_query = prepare_fts_query(query)

        # 如果查询准备失败或包含中文，回退到 LIKE 搜索
        return fallback_search(query) if prepared_query.nil? || prepared_query == :use_like_search

        joins("JOIN #{fts_table} ON #{table_name}.id = #{fts_table}.#{foreign_key}")
          .where("#{fts_table} MATCH ?", prepared_query)
          .select("#{table_name}.*, rank AS relevance_score")
          .order("rank")
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "FTS5 search with rank failed for #{table_name}: #{e.message}"
        fallback_search(query)
      end
    }

    # 专门用于计数的搜索方法（不包含 rank 选择）
    scope :full_search_count, ->(query) {
      return none if query.blank?

      begin
        # 使用 JOIN 方式但不选择 rank
        fts_table = "#{table_name}_fts"
        foreign_key = scope_foreign_key
        prepared_query = prepare_fts_query(query)

        # 如果查询准备失败或包含中文，回退到 LIKE 搜索
        return fallback_search(query) if prepared_query.nil? || prepared_query == :use_like_search

        joins("JOIN #{fts_table} ON #{table_name}.id = #{fts_table}.#{foreign_key}")
          .where("#{fts_table} MATCH ?", prepared_query)
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "FTS5 search count failed for #{table_name}: #{e.message}"
        fallback_search(query)
      end
    }

    # 回退搜索方法
    scope :fallback_search, ->(query) {
      return none if query.blank?

      search_terms = query.strip.split(/\s+/).reject(&:empty?)
      conditions = []
      params = []

      search_terms.each do |term|
        sanitized_term = ActiveRecord::Base.sanitize_sql_like(term)
        field_conditions = search_scope_attrs.map { |attr| "#{attr} LIKE ?" }.join(" OR ")
        conditions << "(#{field_conditions})"
        params.concat(search_scope_attrs.map { "%#{sanitized_term}%" })
      end

      where(conditions.join(" AND "), *params)
    }
  end

  # 私有方法
  private

  def update_search_index
    primary_key = self.class.primary_key
    table_name = self.class.table_name
    foreign_key = self.class.to_s.foreign_key

    # 收集搜索字段的值
    search_attrs = self.class.search_scope_attrs.each_with_object({}) { |attr, acc|
      acc[attr] = quote_string(send(attr) || "")
    }
    id_value = attributes[primary_key]

    # 删除旧的索引数据
    sql_delete = <<~SQL.strip
      DELETE FROM #{table_name}_fts WHERE #{foreign_key} = #{id_value};
    SQL
    self.class.connection.execute(sql_delete)

    # 插入新的索引数据
    sql_insert = <<~SQL.strip
      INSERT INTO #{table_name}_fts(#{search_attrs.keys.join(", ")}, #{foreign_key})
      VALUES (#{search_attrs.values.map { |value| "'#{value}'" }.join(", ")}, #{id_value});
    SQL
    self.class.connection.execute(sql_insert)
  rescue => e
    Rails.logger.error "Failed to update search index for #{self.class.name}##{id}: #{e.message}"
  end

  def delete_search_index
    primary_key = self.class.primary_key
    table_name = self.class.table_name
    foreign_key = self.class.to_s.foreign_key
    id_value = attributes[primary_key]

    sql_delete = <<~SQL.strip
      DELETE FROM #{table_name}_fts WHERE #{foreign_key} = #{id_value};
    SQL
    self.class.connection.execute(sql_delete)
  rescue => e
    Rails.logger.error "Failed to delete search index for #{self.class.name}##{id}: #{e.message}"
  end

  def quote_string(s)
    s.to_s.gsub("\\", '\&\&').gsub("'", "''")
  end

  # 类方法
  class_methods do
    # 设置搜索范围
    def search_scope(*attrs)
      self.search_scope_attrs = attrs
    end

    # 设置分词器（支持中文）
    def search_tokenizer(tokenizer_config)
      self.fts_tokenize_config = tokenizer_config
    end

    # 准备 FTS 查询字符串
    def prepare_fts_query(query)
      # 清理查询字符串，处理特殊字符
      # 使用 Unicode 属性类来保留中文字符和其他 Unicode 字符
      cleaned_query = query.strip
                          .gsub(/[^\p{L}\p{N}\s\-]/, " ")  # 保留字母、数字、空格和连字符
                          .squeeze(" ")                     # 压缩多个空格

      # 将多个词用 OR 连接，支持部分匹配
      words = cleaned_query.split(/\s+/).reject(&:empty?)

      # 如果清理后没有有效词汇，返回 nil 以避免空查询
      return nil if words.empty?

      # 检查是否包含中文字符
      has_chinese = words.any? { |word| word.match?(/\p{Han}/) }

      # 如果包含中文字符，返回特殊标记，让调用方使用 LIKE 搜索
      return :use_like_search if has_chinese

      # 对于纯英文搜索，使用 FTS5 前缀匹配
      words.map { |word| "#{word}*" }.join(" OR ")
    end

    # 重建搜索索引
    def rebuild_search_index(*ids)
      target_ids = Array(ids)
      target_ids = self.ids if target_ids.empty?

      scope_foreign_key = to_s.foreign_key

      # 删除指定记录的索引
      delete_where = target_ids.any? ? "WHERE #{scope_foreign_key} IN (#{target_ids.join(", ")})" : ""
      sql_delete = <<~SQL.strip
        DELETE FROM #{table_name}_fts #{delete_where};
      SQL
      connection.execute(sql_delete)

      # 批量插入新索引
      target_ids.each do |id|
        record_values = where(id: id).pluck(*search_scope_attrs, :id).first
        if record_values.present?
          record_id = record_values.pop

          sql_insert = <<~SQL.strip
            INSERT INTO #{table_name}_fts(#{search_scope_attrs.join(", ")}, #{scope_foreign_key})
            VALUES (#{record_values.map { |value| "'#{quote_string(value || "")}'" }.join(", ")}, #{record_id});
          SQL
          connection.execute(sql_insert)
        end
      end
    rescue => e
      Rails.logger.error "Failed to rebuild search index for #{name}: #{e.message}"
    end

    # 搜索建议（自动完成）
    def search_suggestions(query, limit: 10)
      return [] if query.blank? || query.length < 1

      begin
        # 使用前缀匹配
        suggestion_query = query =~ /[\u4e00-\u9fa5]/ ? query : "#{query}*"

        sanitized_query = connection.quote(suggestion_query)
        sql = <<~SQL.strip
          SELECT DISTINCT #{search_scope_attrs.first} FROM #{table_name}_fts
          WHERE #{table_name}_fts MATCH #{sanitized_query}
          ORDER BY rank
          LIMIT #{limit.to_i};
        SQL

        connection.execute(sql).map { |row| row[search_scope_attrs.first.to_s] }
      rescue ActiveRecord::StatementInvalid => e
        Rails.logger.warn "FTS5 search suggestions failed: #{e.message}"
        # 回退到 LIKE 搜索
        sanitized_query = ActiveRecord::Base.sanitize_sql_like(query)
        where("#{search_scope_attrs.first} LIKE ?", "#{sanitized_query}%")
          .limit(limit)
          .pluck(search_scope_attrs.first)
          .uniq
      end
    end

    def scope_foreign_key
      to_s.foreign_key
    end

    def quote_string(s)
      s.to_s.gsub("\\", '\&\&').gsub("'", "''")
    end
  end
end
