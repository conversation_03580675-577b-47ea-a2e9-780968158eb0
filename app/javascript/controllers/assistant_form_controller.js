import { Controller } from "@hotwired/stimulus"

// 管理助手表单中的工具选择逻辑
export default class extends Controller {
  static targets = ["toolsSection", "toolChoice", "toolCheckbox"]

  connect() {
    this.toggleToolsSection()
  }

  // 根据工具选择模式切换工具选择区域的显示/隐藏
  toggleToolsSection() {
    const toolChoice = this.toolChoiceTarget.value

    if (toolChoice === "none") {
      this.toolsSectionTarget.style.display = "none"
      this.uncheckAllTools()
    } else {
      this.toolsSectionTarget.style.display = "block"
    }
  }

  // 取消选择所有工具
  uncheckAllTools() {
    this.toolCheckboxTargets.forEach(checkbox => {
      checkbox.checked = false
    })
  }

  // 选中所有工具
  selectAllTools(event) {
    const isChecked = event.currentTarget.checked
    this.toolCheckboxTargets.forEach(checkbox => {
      checkbox.checked = isChecked
    })
  }
} 