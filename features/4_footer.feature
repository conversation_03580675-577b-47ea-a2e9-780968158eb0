Feature: Footer
  As a user
  I want to see footer information
  So that I can access important links and information

  Scenario: Viewing company links in footer
    Given I visit any page
    Then I should see "About Us" in the footer
    And I should see "Contact Us" in the footer
    And I should see "Blog" in the footer

  Scenario: Viewing learn section links in footer
    Given I visit any page
    Then I should see "Courses" in the footer
    And I should see "Tutorials" in the footer

  Scenario: Viewing legal links in footer
    Given I visit any page
    Then I should see "Privacy" in the footer
    And I should see "Terms" in the footer

  Scenario: Clicking footer links redirects to correct pages
    Given I visit any page
    When I click "About Us" in the footer
    Then I should be redirected to the about page
    When I click "Privacy" in the footer
    Then I should be redirected to the privacy page
    When I click "Terms" in the footer
    Then I should be redirected to the terms page 