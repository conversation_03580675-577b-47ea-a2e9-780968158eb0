class EventPolicy < ApplicationPolicy
  # 定义作用域过滤规则
  scope_for :active_record_relation do |relation|
    return relation if user&.admin?  # Admin可以看到所有活动
    return relation.visible_to_public unless user

    # 已登录用户可见范围：使用 SQL 条件而不是 .or() 来避免结构不兼容问题
    public_condition = relation.where(visibility: :public_visibility)
    own_condition = relation.where(user_id: user.id)

    # 团队活动条件 - 需要单独处理因为有 joins
    team_event_ids = relation.where(visibility: :team_visibility)
                            .joins(:team_resource_accesses)
                            .where(team_resource_accesses: { team_member: user.team_members })
                            .pluck(:id)

    # 合并所有条件 - 明确指定表名以避免字段名冲突
    if team_event_ids.any?
      relation.where(
        "(events.visibility = ? OR events.user_id = ? OR events.id IN (?))",
        :public_visibility,
        user.id,
        team_event_ids
      ).where(events: { archived: false }).distinct
    else
      relation.where(
        "(events.visibility = ? OR events.user_id = ?)",
        :public_visibility,
        user.id
      ).where(events: { archived: false }).distinct
    end
  end

  def index?
    true # 任何人都可以查看活动列表
  end

  def show?
    admin? || public_accessible? || owner? || team_member_accessible?
  end

  def create?
    has_creation_permission?
  end

  def edit?
    update?
  end

  def update?
    has_management_permission?
  end

  def destroy?
    has_full_control_permission?
  end

  def publish?
    has_management_permission?
  end

  def cancel?
    has_management_permission?
  end

  def manage_registrations?
    has_management_permission?
  end

  def register?
    return false unless user.present?
    return false unless record.present?  # 确保 event 存在
    return false if owner?  # 活动创建者不能报名自己的活动
    return false if record.full?
    return false if record.cancelled?
    return false if record.start_time < Time.current
    return false if record.registered_users.include?(user)

    accessible?
  end

  def accessible?
    return true if public_accessible?  # 公开活动任何人都可以访问
    return false unless user           # 非公开活动需要登录
    return true if owner?              # 创建者总是可以访问

    # 团队可见性：检查用户是否是可访问团队的成员
    team_member_accessible?
  end

  private

  # 权限层次定义
  def has_creation_permission?
    # 创建权限：只有管理员、团队管理员、或者团队事件协作者可以创建活动
    admin? || user_is_team_admin? || user_is_event_moderator_in_any_team?
  end

  def has_full_control_permission?
    admin? || owner? || (record.team_visibility? && team_admin_for_this_event?)
  end

  def has_management_permission?
    has_full_control_permission? || event_moderator?
  end

  def has_view_permission?
    public_accessible? || has_management_permission? || team_member_accessible?
  end

  def owner?
    user && record&.user == user
  end

  def team_admin_for_this_event?
    return false unless user && record

    # 直接检查用户是否是该特定活动所属团队的管理员
    TeamMember.exists?(
      user: user,
      team_id: record.accessible_teams.pluck(:id),
      role: :admin
    )
  end

  def admin?
    user&.admin?
  end

  def event_moderator?
    return false unless user
    return false unless record

    begin
      # 检查用户是否通过 TeamResourceAccess 拥有该活动的 moderator 权限
      record.team_resource_accesses
            .joins(:team_member)
            .where(team_members: { user_id: user.id })
            .where(role: "moderator")
            .exists?
    rescue => e
      Rails.logger.error "EventPolicy#event_moderator? error: #{e.message}"
      false
    end
  end

  def public_accessible?
    record&.public_visibility?
  end

  def team_member_accessible?
    return false unless user
    return false unless record
    return false unless record.team_visibility?

    record.accessible_teams.any? { |team| user.teams.include?(team) }
  end

  # 创建权限的辅助方法
  def user_is_team_admin?
    return false unless user
    
    # 检查用户是否是任何团队的管理员
    TeamMember.exists?(user: user, role: :admin)
  end

  def user_is_event_moderator_in_any_team?
    return false unless user
    
    # 检查用户是否在任何团队中拥有 event moderator 权限
    TeamResourceAccess.joins(:team_member)
                      .where(team_members: { user: user })
                      .where(resource_kind: :event, role: :moderator)
                      .exists?
  end
end
