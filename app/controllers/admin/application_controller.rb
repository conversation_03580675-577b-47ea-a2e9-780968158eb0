# All Administrate controllers inherit from this
# `Administrate::ApplicationController`, making it the ideal place to put
# authentication logic or other before_actions.
#
# If you want to add pagination or other controller-level concerns,
# you're free to overwrite the RESTful controller actions.
module Admin
  class ApplicationController < Administrate::ApplicationController
    include Authentication
    include ActionPolicy::Controller

    before_action :authenticate_admin
    before_action :set_current_user

    def authenticate_admin
      # Require authentication first
      require_authentication

      # Then check if user is admin
      unless current_user&.admin?
        flash[:error] = "Access denied. Admin privileges required."
        redirect_to root_path
      end
    end

    # Override this value to specify the number of elements to display at a time
    # on index pages. Defaults to 20.
    def records_per_page
      params[:per_page] || 20
    end

    private

    def current_user
      Current.session&.user
    end

    def set_current_user
      @current_user = current_user
    end
  end
end
