Feature: Authentication UI with Shadcn Components
  As a developer
  I want to integrate shadcn components into authentication views
  So that users have a modern and consistent interface

  Scenario: Login form with Shadcn components
    Given I visit the login page
    Then I should see a shadcn styled email input
    And I should see a shadcn styled password input
    And I should see a shadcn styled submit button

  Scenario: Password reset form with Shadcn components
    Given I visit the password reset page
    Then I should see a shadcn styled email input
    And I should see a shadcn styled submit button 