class CreateEventsFts5Table < ActiveRecord::Migration[8.0]
  def up
    # 创建 FTS5 虚拟表
    create_virtual_table :events_fts, :fts5, [
      "title",
      "description",
      "prerequisites",
      "what_to_bring",
      "what_included",
      "event_id UNINDEXED",        # 使用 event_id 作为外键，不索引
      "tokenize='unicode61'"        # 使用 unicode61 分词器，支持中文
    ]

    # 注意：不再创建触发器，因为我们将使用 SqliteSearch concern 的回调来维护索引
  end

  def down
    execute "DROP TABLE IF EXISTS events_fts;"
  end
end
