<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { items: [[t('ai.assistants.title'), ai_assistants_path], [@assistant.name, ai_assistant_path(@assistant)], [t('ai.assistants.edit'), nil]] } %>

        <div class="mb-6">
          <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white"><%= t('ai.assistants.edit') %></h1>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="col-span-2">
        <%= render 'form', assistant: @assistant, submit_text: t('ai.assistants.actions.update'), cancel_path: ai_assistant_path(@assistant) %>
      </div>
      
      <div class="col-span-1">
        <%= render 'tips' %>
      </div>
    </div>
  </div>
</div>
