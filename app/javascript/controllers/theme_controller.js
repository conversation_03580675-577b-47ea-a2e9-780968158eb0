import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["darkIcon", "lightIcon", "button"];

  connect() {
    this.initializeTheme();
  }

  initializeTheme() {
    // 根据之前的设置改变按钮内的图标
    if (localStorage.getItem('color-theme') === 'dark' || (!('color-theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      this.lightIconTarget.classList.remove('hidden');
      this.darkIconTarget.classList.add('hidden');
      document.documentElement.classList.add('dark');
    } else {
      this.darkIconTarget.classList.remove('hidden');
      this.lightIconTarget.classList.add('hidden');
      document.documentElement.classList.remove('dark');
    }
  }

  toggle() {
    // 切换图标
    this.darkIconTarget.classList.toggle('hidden');
    this.lightIconTarget.classList.toggle('hidden');

    // 如果之前已经设置过主题
    if (localStorage.getItem('color-theme')) {
      if (localStorage.getItem('color-theme') === 'light') {
        document.documentElement.classList.add('dark');
        localStorage.setItem('color-theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('color-theme', 'light');
      }
    } else {
      // 如果之前没有设置过主题
      if (document.documentElement.classList.contains('dark')) {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('color-theme', 'light');
      } else {
        document.documentElement.classList.add('dark');
        localStorage.setItem('color-theme', 'dark');
      }
    }
  }
} 