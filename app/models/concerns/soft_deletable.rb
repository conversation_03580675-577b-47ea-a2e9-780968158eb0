module SoftDeletable
  extend ActiveSupport::Concern

  included do
    default_scope { where(deleted_at: nil) }
    scope :without_deleted, -> { where(deleted_at: nil) }
    scope :only_deleted, -> { unscope(where: :deleted_at).where.not(deleted_at: nil) }
    scope :with_deleted, -> { unscope(where: :deleted_at) }

    def destroy
      run_callbacks :destroy do
        update_column(:deleted_at, Time.current)
      end
    end

    def real_destroy
      super
    end

    def restore
      update_column(:deleted_at, nil)
      reload  # 确保对象状态更新
    end

    def deleted?
      deleted_at.present?
    end
  end
end
