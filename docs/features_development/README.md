# 🚀 功能开发管理

本文件夹包含 AI Pro CGC 项目的功能开发状态、计划和路线图。

## 📊 功能开发状态

### 🎯 收集用户学习兴趣

- [ ] 用户可以填写自己的学习兴趣
- [x] 用户可以在个人 profile 查看、编辑、删除自己填写的学习兴趣

### 👤 Account-User

- [x] 用户登录
- [x] 使用邮箱 Email 登录
- [x] 使用手机号 Phone 登录
- [x] 使用第三方登录
- [x] 使用微信 Wechat 登录
- [x] 用户使用手机号、邮箱注册
- [ ] 用户使用手机号、邮箱找回密码
- [x] 用户更新个人 profile
- [ ] Notification 功能

### 📝 Post

- [x] 用户创建 Post
- [x] 用户可以查看、编辑、删除自己创建的 Post
- [x] 用户可以查看、编辑、删除自己通过 base unit 文件生成的 Post
- [ ] 如果是生成的 post，会显示关联的 base unit 文件的链接：reference
      会显示关联的 base unit 文件的链接：reference

### 📁 BaseUnit

- [x] 用户上传文件
  - TODO
    1. 上传功能
    2. 存储空间相关技术，aws s3
    3. 看看相关法规，是不是数据必须存在国内
    4. 是不是放在 nas 上
- 格式限定为 PDF、DOCX、PPT、txt、epub、md、图片等格式
- 免费空间为 100MB
- 订阅后，空间为 10GB；（如果有 1 万订阅用户，存储空间为 100GB）
- [x] 用户可以批量上传文件
- [x] 用户可以查看、删除自己上传的文件
- [ ] 用户可以编辑文件 base unit 名字
- [x] 用户上传文件后，用户可以提取文件中的内容，以 markdowns 形式存储存为 post
  - 免费用户，每周可以提取 100MB
  - 订阅用户，每周可以提取 3GB
- [ ] convert 按钮单独一列，颜色 🟥 高亮明显一点
- [x] 用户可以查看、编辑、删除自己上传的文件中的 markdown 格式的 post（makeitmarkdown）
- [ ] base unit index page，用户点击查看关联 post，弹出 dialog 显示关联的 post
      用户点击查看关联 post，弹出 dialog 显示关联的 post

### 📚 KnowledgeBase

- [x] 知识库分为个人知识库，team 知识库、公共知识库
- [x] 个人知识库，用户可以创建、编辑、删除自己的知识库
- [x] team 知识库，需要有管理员，管理员可以邀请、删除、管理团队成员
- [x] 公共知识库，用户可以查看公共知识库中的内容
- [x] 知识库中会有版本控制，如果 post 被修改，会记录修改历史
- [x] 用户可以把知识库中以 markdowns 形式存储的知识，embedding 到向量数据库中
- [ ] 可以一键把所有 post 转换为向量存储到向量数据库中
- [ ] 可以选择部分 post 转换为向量存储到向量数据库中
- [ ] 显示哪些 post 已经转换为向量存储到向量数据库中，哪些 post 还没有转换

### 💬 Message

- [x] 用户可以使用 LLM 生成的回答
- [x] conversation
- [ ] in memory

### 🤖 Assistant-tools

- [ ] 用户可以选择挂载不同的知识内容，制作不同知识库的 QA assistant
- [ ] 用户可以选择不同的知识库内容，+不同的 tool，制作 ai assistant，产出不同的工件（artifacts）
- [?] 用户可以 canvas 引用 post 当中的部分内容，转发到某个"群"里面去，for 讨论、解疑答惑 **【答疑板块】**去

### 🔍 Vector Database

- [ ] vector cache 缓存技术，避免重复计算

### 🎓 面向不同教学场景的学习交互体验

#### Domain-Topic-Module-Course-Spint-Task-Quiz

- [ ] 用户可以学习免费课程
- [ ] 用户可以订阅收费课程
- [ ] 有公共课程
- [ ] 教学管理员可以创建、编辑、删除公共课程
- [ ] 用户可以查看、编辑、删除自己创建的课程
- [ ] 用户可以同步 task, quiz card 到第三方卡片学习工具，例如 anki
      quiz card 到第三方卡片学习工具，例如 anki

## 📋 详细开发计划

- **[活动系统开发计划](events_development_plan.md)** - 活动管理功能的详细开发规划

## 🗺️ 地图标记聚类功能

**状态：🟢 已完成**

### 功能特性

- **自动聚类触发**：当地图上的事件数量超过 20 个时自动启用聚类
- **动态切换**：根据事件数量自动在聚类和普通显示之间切换
- **性能优化**：减少DOM元素数量，提高地图渲染性能

### 聚类配置

```javascript
{
  maxClusterRadius: 20,           // 聚类半径20像素
  spiderfyOnMaxZoom: true,        // 最大缩放时展开
  showCoverageOnHover: false,     // 不显示覆盖范围
  zoomToBoundsOnClick: true,      // 点击缩放到边界
  disableClusteringAtZoom: 16,    // 16级以上禁用聚类
  removeOutsideVisibleBounds: true, // 移除视口外标记
  animate: true,                  // 启用动画
  chunkedLoading: true,           // 分块加载
  chunkInterval: 200,             // 分块间隔200ms
  chunkDelay: 50                  // 分块延迟50ms
}
```

### 视觉设计

- **小聚类** (< 10个): 蓝色主题 (primary-500)
- **中聚类** (10-99个): 琥珀色主题 (amber-500)
- **大聚类** (≥ 100个): 红色主题 (red-500)
- 悬停效果：1.1倍缩放和阴影
- 暗色模式适配
- 圆形设计与项目设计系统一致

### 实现文件

- `app/javascript/controllers/map_controller.js` - 核心聚类逻辑
- `app/assets/stylesheets/map.css` - 自定义样式
- `app/assets/stylesheets/MarkerCluster.css` - 聚类基础样式
- `app/assets/stylesheets/MarkerCluster.Default.css` - 聚类默认样式

## 📝 文档规范

### 命名约定

- 使用描述性的文件名，如 `feature_name_development_plan.md`
- 使用小写字母和下划线分隔

### 内容结构

每个功能计划文档应包含：

1. **功能概述** - 简要描述功能目标
2. **需求分析** - 详细的功能需求
3. **技术方案** - 实现方案和技术选型
4. **开发阶段** - 分阶段的开发计划
5. **时间安排** - 预期的开发时间线
6. **风险评估** - 潜在风险和应对措施
7. **验收标准** - 功能完成的验收条件

### 状态标识

使用以下状态标识功能开发进度：

- 🔵 **规划中** - 功能仍在规划阶段
- 🟡 **开发中** - 功能正在开发
- 🟢 **已完成** - 功能开发完成
- 🔴 **暂停** - 功能开发暂时停止
- ⚪ **取消** - 功能开发取消

## 🔄 更新流程

1. **新增计划**: 创建新的功能计划文档
2. **状态更新**: 及时更新功能开发状态
3. **内容修订**: 根据开发进展调整计划内容
4. **完成归档**: 已完成的功能可移至 `completed/` 子文件夹

## 📅 定期回顾

建议每月进行一次路线图回顾，评估：

- 当前进度是否符合预期
- 是否需要调整优先级
- 新功能需求的评估和规划


  } else if (childCount < 100) {
    c += 'medium'
  } else {
    c += 'large'
  }

  return new this.L.DivIcon({
    html: '<div><span>' + childCount + '</span></div>',
    className: 'marker-cluster' + c,
    iconSize: new this.L.Point(40, 40)
  })
}
```

---

**说明：**

- 该功能尚未开发，优先级高，待地图缓存策略完成后立即推进。
- 详细设计与进度请见 `events_development_plan.md`。

---

💡 **提示**: 在制定新的功能计划前，请先查阅现有文档，避免重复规划类似功能。
