RSpec.configure do |config|
  # Chrome Headless 配置
  Capybara.register_driver :selenium_chrome_headless do |app|
    options = Selenium::WebDriver::Chrome::Options.new(
      args: %w[
        headless
        no-sandbox
        disable-dev-shm-usage
        disable-gpu
        window-size=1400,1400
      ]
    )
    options.add_argument('--log-level=3')  # 替代原来的日志配置方式

    Capybara::Selenium::Driver.new(
      app,
      browser: :chrome,
      options: options,
      clear_local_storage: true,
      clear_session_storage: true,
      timeout: 30 # 增加超时时间
    )
  end

  Capybara.configure do |config|
    config.default_driver = :rack_test # 默认使用无头驱动
    config.javascript_driver = :selenium_chrome_headless # JS测试使用Headless Chrome
    config.enable_aria_label = true # 支持aria-label定位
    config.automatic_label_click = true # 自动处理label点击
    config.default_max_wait_time = 10 # 全局等待时间（增加以提高稳定性）
  end
end
