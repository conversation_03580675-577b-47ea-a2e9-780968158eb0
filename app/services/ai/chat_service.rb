module Ai
  class ChatService < MessageService
    class << self
      # 清除指定对话的上下文缓存
      def clear_context_cache(conversation)
        if conversation.respond_to?(:assistant_id) && conversation.assistant_id.present?
          # 确保 assistant_id 存在
          cache_key = "langchain_messages:assistant_#{conversation.assistant_id}/Conversation_#{conversation.id}"
          Rails.logger.info "Clearing cache for key: #{cache_key}" # 移除调试信息
          Rails.cache.delete(cache_key)
        else
          # Rails.logger.warn "Cannot clear cache: Conversation ID #{conversation.id} has no assistant_id or does not respond to it." # 移除调试信息
          false
        end
      end

      # 移除了 get_messageable_context 方法
    end

    # # 初始化方法，用于设置消息关联对象、用户消息ID和助手消息
    # # 如果没有，使用父类的默认值
    # def initialize(messageable:, user_message_id:, assistant_message:)
    #   @messageable = messageable
    #   @user_message_id = user_message_id
    #   @assistant_message = assistant_message
    # end

    # # 实现conversation context 特有的聊天逻辑
    # # 如果没有，则使用父类的方法
    # def process
    # end
  end
end
