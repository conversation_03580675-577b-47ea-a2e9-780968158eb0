import { Controller } from "@hotwired/stimulus"

// 连接到数据控制器="datetime-picker"
export default class extends Controller {
  static values = {
    fieldId: String,
    alertMessage: String
  }

  connect() {
    console.log("DatetimePicker controller connected")
    this.selectedDate = null
    this.selectedTime = null

    // 初始化 Flowbite 日期选择器
    this.initializeDatepicker()

    // 设置默认时间
    this.setDefaultTime()
  }

  initializeDatepicker() {
    // 查找内联日期选择器元素
    const datepickerElement = this.element.querySelector('[inline-datepicker]')

    if (datepickerElement) {
      // 直接使用 Flowbite initFlowbite 来初始化
      // 这会自动初始化所有带有正确 data 属性的组件
      if (typeof initFlowbite !== 'undefined') {
        // 设置监听器
        this.setupDateListener(datepickerElement)
        console.log("Datepicker will be initialized by Flowbite")
      } else {
        console.warn("Flowbite initFlowbite not available")
      }
    }
  }

  setupDateListener(element) {
    // 监听日期选择事件 - Flowbite 会自动处理日期选择器
    element.addEventListener('changeDate', (e) => {
      this.selectedDate = e.detail.date
      console.log("Date selected:", this.selectedDate)
    })
  }

  setDefaultTime() {
    // 设置默认时间为当前时间
    const now = new Date()
    const hour = now.getHours()
    const minute = now.getMinutes() // 使用精确的分钟数

    setTimeout(() => {
      const fieldName = this.getFieldName()
      const hourSelect = this.element.querySelector(`#hour-select-${fieldName}`)
      const minuteSelect = this.element.querySelector(`#minute-select-${fieldName}`)

      if (hourSelect) {
        hourSelect.value = String(hour).padStart(2, '0')
      }
      if (minuteSelect) {
        minuteSelect.value = String(minute).padStart(2, '0')
      }

      this.updateSelectedTime()
    }, 100)
  }

  getFieldName() {
    // 从 field ID 中提取字段名称 (例如: "event_start_time" -> "start_time")
    return this.fieldIdValue.replace('event_', '')
  }

  timeChanged(event) {
    // 时间发生改变时更新选择的时间
    this.updateSelectedTime()
  }

  updateSelectedTime() {
    // 获取当前选择的小时和分钟
    const fieldName = this.getFieldName()
    const hourSelect = this.element.querySelector(`#hour-select-${fieldName}`)
    const minuteSelect = this.element.querySelector(`#minute-select-${fieldName}`)

    if (hourSelect && minuteSelect) {
      const hour = hourSelect.value
      const minute = minuteSelect.value
      this.selectedTime = `${hour}:${minute}`
      console.log("Time updated:", this.selectedTime)
    }
  }

  save() {
    // 确保有最新的时间选择
    this.updateSelectedTime()

    if (!this.selectedDate || !this.selectedTime) {
      alert(this.alertMessageValue || "Please select date and time")
      return
    }

    // 组合日期和时间
    const dateString = this.formatDate(this.selectedDate)
    const datetimeString = `${dateString}T${this.selectedTime}`

    // 查找目标输入字段
    const targetField = document.getElementById(this.fieldIdValue)
    if (targetField) {
      targetField.value = datetimeString

      // 触发变化事件
      targetField.dispatchEvent(new Event('change', { bubbles: true }))

      console.log("Saved datetime:", datetimeString)
    }

    // 更新显示字段
    this.updateDisplayField(dateString, this.selectedTime)

    // 关闭模态框
    this.closeModal()
  }

  updateDisplayField(dateString, timeString) {
    // 查找对应的显示字段
    const fieldName = this.getFieldName()
    const displayField = document.getElementById(`${fieldName}_display`)

    if (displayField) {
      // 格式化显示文本
      const displayText = this.formatDisplayText(dateString, timeString)
      displayField.innerHTML = `<span class="text-gray-900 dark:text-gray-100">${displayText}</span>`
    }
  }

  formatDisplayText(dateString, timeString) {
    // 将日期时间格式化为国际化友好的显示格式
    const date = new Date(`${dateString}T${timeString}`)

    // 使用多种格式选项，您可以选择其中一种

    // 选项 1: ISO-like 格式 (推荐) - 2025-06-23 14:30
    const isoFormat = `${dateString} ${timeString}`

    // 选项 2: 英文月份格式 - Jun 23, 2025 14:30
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }
    const englishFormat = date.toLocaleDateString('en-US', options)

    // 选项 3: 英文月份 12小时制 - Jun 23, 2025 2:30 PM
    const options12h = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }
    const english12hFormat = date.toLocaleDateString('en-US', options12h)

    // 返回 ISO-like 格式（最国际化）
    return isoFormat
  }

  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  closeModal() {
    // 查找模态框元素
    const modal = this.element.closest('[id*="timepicker-modal"]')
    if (modal) {
      // 使用 Flowbite 的模态框 API 关闭
      const modalInstance = window.FlowbiteInstances.getInstance('Modal', modal.id)
      if (modalInstance) {
        modalInstance.hide()
      } else {
        // 备用方法：直接隐藏
        modal.classList.add('hidden')
        modal.setAttribute('aria-hidden', 'true')
      }
    }
  }

  disconnect() {
    console.log("DatetimePicker controller disconnected")
    // Flowbite 会自动管理组件的生命周期，不需要手动清理
  }
} 