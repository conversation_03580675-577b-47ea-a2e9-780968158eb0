namespace :search_index do
  desc "Rebuild FTS5 search index for all events"
  task rebuild_events: :environment do
    puts "Rebuilding search index for Event model..."

    # 清空现有索引
    ActiveRecord::Base.connection.execute("DELETE FROM events_fts;")

    # 批量重建索引
    Event.find_each(batch_size: 100) do |event|
      event.send(:update_search_index)
      print "."
    end

    puts "\nDone! Rebuilt search index for #{Event.count} events."
  end

  desc "Rebuild search index for specific event IDs"
  task :rebuild_events_by_ids, [ :ids ] => :environment do |t, args|
    ids = args[:ids].split(",").map(&:to_i)
    puts "Rebuilding search index for Event IDs: #{ids.join(", ")}"

    Event.rebuild_search_index(*ids)

    puts "Done!"
  end
end
