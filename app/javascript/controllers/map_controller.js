import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="map"
export default class extends Controller {
  static targets = ["container", "loading", "error", "eventCount", "eventCountText"]
  static values = {
    events: Array,
    center: Array,
    zoom: Number,
    apiUrl: String
  }

  async connect() {
    console.log("Map controller connected")
    this.showLoading()

    // 初始化搜索半径 - 从select元素的默认选中值获取
    const radiusSelect = this.element.querySelector('select[data-action*="updateRadius"]')
    this.searchRadius = radiusSelect ? parseFloat(radiusSelect.value) : 10

    try {
      // 动态导入 Leaflet 和聚类插件
      await this.loadMapLibraries()
      await this.initializeMap()
      await this.loadEvents()
      this.hideLoading()
    } catch (error) {
      console.error('Map initialization failed:', error)
      this.showError('地图加载失败，请刷新页面重试')
    }
  }

  disconnect() {
    if (this.map) {
      this.map.remove()
      this.map = null
    }

    // 清理聚类组
    if (this.markerCluster) {
      this.markerCluster = null
    }
  }

  // 动态加载地图库
  async loadMapLibraries() {
    try {
      // 导入 Leaflet
      const leafletModule = await import('leaflet')
      this.L = leafletModule.default || leafletModule

      // 导入聚类插件
      const clusterModule = await import('leaflet.markercluster')

      console.log('Map libraries loaded successfully')
    } catch (error) {
      console.error('Failed to load map libraries:', error)
      throw error
    }
  }

  async initializeMap() {
    // 默认中心点（中国中心位置）
    const center = this.centerValue && this.centerValue.length === 2
      ? this.centerValue
      : [32.0103, 119.5686]
    const zoom = this.zoomValue || 6

    // 创建地图实例 - 禁用默认缩放控件，稍后自定义位置
    this.map = this.L.map(this.containerTarget, {
      center: center,
      zoom: zoom,
      zoomControl: false, // 禁用默认位置
      attributionControl: true,
      preferCanvas: true, // 提高性能
      maxZoom: 18,
      minZoom: 3
    })

    // 添加自定义位置的缩放控件，避免与顶部工具栏冲突
    this.L.control.zoom({
      position: 'topright'
    }).addTo(this.map)

    // 保存初始视图用于重置
    this.initialCenter = center
    this.initialZoom = zoom

    // 添加OpenStreetMap瓦片层
    this.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19
    }).addTo(this.map)

    // 初始化标记管理 - 不再直接添加到地图，而是根据需要选择聚类或普通图层
    this.markers = this.L.layerGroup()

    // 添加地图事件监听
    this.map.on('moveend', () => this.onMapMove())
    this.map.on('zoomend', () => this.onMapZoom())
  }

  async loadEvents() {
    if (this.eventsValue && this.eventsValue.length > 0) {
      this.displayEvents(this.eventsValue)
    } else if (this.apiUrlValue) {
      await this.fetchEventsFromAPI()
    }
  }

  async fetchEventsFromAPI() {
    try {
      const response = await fetch(this.apiUrlValue, {
        headers: {
          'Accept': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      if (!response.ok) throw new Error('Failed to fetch events')

      const data = await response.json()
      this.displayEvents(data.events)

      // 如果有边界数据，调整地图视图
      if (data.meta && data.meta.bounds) {
        this.fitMapToBounds(data.meta.bounds)
      }
    } catch (error) {
      console.error('Failed to load events:', error)
      this.showError('无法加载活动数据')
    }
  }

  displayEvents(events) {
    this.clearMarkers()

    // 根据事件数量决定是否使用聚类
    const shouldCluster = this.shouldUseCluster(events.length)

    if (shouldCluster) {
      this.initializeCluster()
    } else {
      // 如果之前使用了聚类，现在需要移除
      if (this.markerCluster) {
        this.map.removeLayer(this.markerCluster)
        this.markerCluster = null
      }
      // 添加普通标记图层到地图
      this.map.addLayer(this.markers)
    }

    events.forEach(event => {
      this.addEventMarker(event)
    })

    // 更新事件计数
    this.updateEventCount(events.length)

    // 如果有事件，自动调整地图视图
    if (events.length > 0) {
      this.fitMapToEvents(events)
    }
  }

  updateEventCount(count) {
    if (this.hasEventCountTextTarget) {
      this.eventCountTextTarget.textContent = count
    }
  }

  fitMapToEvents(events) {
    const validEvents = events.filter(event => event.latitude && event.longitude)
    if (validEvents.length === 0) return

    if (validEvents.length === 1) {
      // 单个事件，居中显示
      const event = validEvents[0]
      this.map.setView([event.latitude, event.longitude], 12)
    } else {
      // 多个事件，调整视图包含所有事件
      let group
      if (this.markerCluster) {
        group = this.markerCluster
      } else {
        group = new this.L.featureGroup(this.markers.getLayers())
      }
      this.map.fitBounds(group.getBounds().pad(0.1))
    }
  }

  // 判断是否应该使用聚类
  shouldUseCluster(eventCount) {
    return eventCount > 20
  }

  // 初始化聚类组
  initializeCluster() {
    if (this.markerCluster) {
      // 如果已经存在聚类组，先移除
      this.map.removeLayer(this.markerCluster)
    }

    // 创建新的聚类组
    this.markerCluster = this.L.markerClusterGroup({
      maxClusterRadius: 20,           // 聚类半径
      spiderfyOnMaxZoom: true,        // 在最大缩放级别时展开聚类
      showCoverageOnHover: false,     // 鼠标悬停时不显示覆盖范围
      zoomToBoundsOnClick: true,      // 点击聚类时缩放到边界
      disableClusteringAtZoom: 16,    // 在缩放级别16以上时禁用聚类
      removeOutsideVisibleBounds: true, // 移除视口外的标记以提高性能
      animate: true,                  // 启用动画
      animateAddingMarkers: false,    // 禁用添加标记时的动画以提高性能
      chunkedLoading: true,           // 分块加载大量标记
      chunkInterval: 200,             // 分块间隔
      chunkDelay: 50,                 // 分块延迟
      // 自定义聚类图标
      iconCreateFunction: (cluster) => {
        const count = cluster.getChildCount()
        let className = 'marker-cluster-small'

        if (count < 10) {
          className = 'marker-cluster-small'
        } else if (count < 100) {
          className = 'marker-cluster-medium'
        } else {
          className = 'marker-cluster-large'
        }

        return this.L.divIcon({
          html: `<div><span>${count}</span></div>`,
          className: `marker-cluster ${className}`,
          iconSize: this.L.point(40, 40)
        })
      }
    })

    // 添加聚类组到地图
    this.map.addLayer(this.markerCluster)

    // 移除普通标记图层（如果存在）
    if (this.map.hasLayer(this.markers)) {
      this.map.removeLayer(this.markers)
    }

    console.log('Marker clustering initialized')
  }

  addEventMarker(event) {
    if (!event.latitude || !event.longitude) return

    const marker = this.L.marker([event.latitude, event.longitude], {
      icon: this.createCustomIcon(event)
    })

    marker.bindPopup(this.createPopupContent(event), {
      maxWidth: 300,
      className: 'event-popup'
    })

    // 添加点击事件
    marker.on('click', () => this.onMarkerClick(event))

    // 根据是否使用聚类来决定添加到哪个图层
    if (this.markerCluster) {
      this.markerCluster.addLayer(marker)
    } else {
      this.markers.addLayer(marker)
    }
  }

  createCustomIcon(event) {
    const color = event.category?.color || '#3B82F6'

    return this.L.divIcon({
      className: 'custom-event-marker',
      html: `
        <div class="w-8 h-8 rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white text-xs font-bold"
             style="background-color: ${color}">
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
          </svg>
        </div>
      `,
      iconSize: [32, 32],
      iconAnchor: [16, 16],
      popupAnchor: [0, -16]
    })
  }

  createPopupContent(event) {
    const startTime = new Date(event.start_time).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })

    const priceText = event.price > 0 ? `¥${event.price}` : '免费'
    const spotsText = event.max_participants ?
      `${event.registered_count || 0}/${event.max_participants}` :
      `${event.registered_count || 0} 人已报名`

    // 简化的弹窗内容，减少信息密度
    return `
      <div class="p-3 min-w-[260px] max-w-[320px]">
        <div class="mb-3">
          <h3 class="font-semibold text-base text-gray-900 leading-tight mb-1 line-clamp-2">${event.title}</h3>
          <div class="flex items-center justify-between">
            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-primary-100 text-primary-800">
              ${event.category?.name || '活动'}
            </span>
            <span class="text-sm font-semibold text-primary-600">${priceText}</span>
          </div>
        </div>

        <div class="space-y-1.5 mb-3 text-sm text-gray-600">
          <div class="flex items-center">
            <svg class="w-3.5 h-3.5 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
            </svg>
            <span class="truncate">${startTime}</span>
          </div>

          <div class="flex items-center">
            <svg class="w-3.5 h-3.5 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
            </svg>
            <span class="truncate">${event.location_name || '待定'}</span>
          </div>

          <div class="flex items-center">
            <svg class="w-3.5 h-3.5 mr-1.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
            </svg>
            <span>${spotsText}</span>
          </div>
        </div>

        <div class="flex gap-2">
          <a href="${event.url}"
             class="flex-1 inline-flex items-center justify-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 transition-colors">
            查看详情
          </a>
          ${event.can_register ? `
            <button onclick="window.location.href='${event.url}#register'"
                    class="flex-1 inline-flex items-center justify-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-primary-600 hover:bg-primary-700 transition-colors">
              立即报名
            </button>
          ` : ''}
        </div>
      </div>
    `
  }

  // 获取用户当前位置
  // 获取用户当前位置（带回退策略）
  async locateUser() {
    // 检查浏览器支持
    if (!navigator.geolocation) {
      this.showLocationError('您的浏览器不支持地理定位功能')
      return
    }

    // 立即显示加载状态
    this.showLocationLoading()

    try {
      // 检查权限状态（如果支持）
      if (navigator.permissions) {
        try {
          const permission = await navigator.permissions.query({ name: 'geolocation' })

          if (permission.state === 'denied') {
            this.hideLocationLoading()
            this.showLocationPermissionGuide()
            return
          }
        } catch (e) {
          // 继续执行，不阻止定位尝试
        }
      }

      // 采用带回退机制的定位
      const position = await this.getCurrentPositionWithFallback()
      const { latitude, longitude } = position.coords

      // 在地图上显示用户位置
      this.showUserLocation(latitude, longitude)

      // 搜索附近的事件，使用当前选中的搜索半径
      await this.loadNearbyEvents(latitude, longitude, this.searchRadius || 10)

      this.hideLocationLoading()
    } catch (error) {
      this.hideLocationLoading()
      console.error('Location error:', error)
      this.handleLocationError(error)
    }
  }

  // 根据精度参数封装一次 getCurrentPosition 调用
  getCurrentPosition(highAccuracy = true) {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => resolve(position),
        (error) => reject(error),
        {
          enableHighAccuracy: highAccuracy,
          timeout: 15000, // 15 秒
          maximumAge: 300000 // 5 分钟缓存
        }
      )
    })
  }

  // 带回退机制：高精度 -> 低精度 -> IP 定位
  async getCurrentPositionWithFallback() {
    try {
      return await this.getCurrentPosition(true)
    } catch (errHigh) {
      console.warn('High accuracy geolocation failed:', errHigh)
      try {
        return await this.getCurrentPosition(false)
      } catch (errLow) {
        console.warn('Low accuracy geolocation failed:', errLow)
        const ipLoc = await this.getIpLocation()
        if (ipLoc) {
          return {
            coords: {
              latitude: ipLoc.lat,
              longitude: ipLoc.lon,
              accuracy: 50000 // approximate
            }
          }
        }
        throw errLow
      }
    }
  }

  // 基于 IP 的粗略定位
  async getIpLocation() {
    try {
      const res = await fetch('https://ipapi.co/json/')
      if (!res.ok) throw new Error('ipapi request failed')
      const data = await res.json()
      if (data.latitude && data.longitude) {
        return { lat: data.latitude, lon: data.longitude }
      }
    } catch (err) {
      console.warn('IP location failed:', err)
    }
    return null
  }

  // 处理定位错误
  handleLocationError(error) {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        this.showLocationPermissionGuide()
        break
      case error.POSITION_UNAVAILABLE:
        this.showLocationError('无法获取您的位置信息，请检查GPS设置和网络连接')
        break
      case error.TIMEOUT:
        this.showLocationError('定位超时，请重试。如果问题持续，请检查网络连接')
        break
      default:
        this.showLocationError('定位失败，请重试')
        break
    }
  }

  // 显示权限引导
  showLocationPermissionGuide() {
    const message = `
      <div class="text-left">
        <h3 class="font-semibold mb-2">需要位置权限</h3>
        <p class="mb-3">要查看附近的活动，需要获取您的位置信息。</p>
        <div class="space-y-2 text-sm">
          <p><strong>如何启用位置权限：</strong></p>
          <ol class="list-decimal list-inside space-y-1 ml-2">
            <li>点击地址栏左侧的锁图标</li>
            <li>选择"位置"权限</li>
            <li>设置为"允许"</li>
            <li>刷新页面后重试</li>
          </ol>
        </div>
      </div>
    `
    this.showLocationError(message, true)
  }

  // 显示位置相关错误
  showLocationError(message, isHtml = false) {
    if (isHtml) {
      // 创建一个临时的提示框
      const dialog = document.createElement('div')
      dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'
      dialog.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4">
          ${message}
          <div class="mt-4 flex justify-end">
            <button onclick="this.closest('.fixed').remove()"
                    class="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700">
              知道了
            </button>
          </div>
        </div>
      `
      document.body.appendChild(dialog)
    } else {
      alert(message)
    }
  }

  showUserLocation(lat, lng) {
    // 验证并保存用户位置坐标
    const latNum = Number(lat)
    const lngNum = Number(lng)

    if (!isFinite(latNum) || !isFinite(lngNum)) {
      console.error('showUserLocation: invalid coordinates', lat, lng)
      return
    }

    // 保存用户位置坐标到实例变量
    this.userLocation = { lat: latNum, lng: lngNum }

    // 移除之前的用户位置标记
    if (this.userLocationMarker) {
      this.map.removeLayer(this.userLocationMarker)
    }

    // 创建用户位置标记
    this.userLocationMarker = this.L.marker([latNum, lngNum], {
      icon: this.createUserLocationIcon()
    }).addTo(this.map)

    this.userLocationMarker.bindPopup('您的位置').openPopup()

    // 将地图中心移动到用户位置
    this.map.setView([latNum, lngNum], 13)
  }

  createUserLocationIcon() {
    return this.L.divIcon({
      className: 'user-location-marker',
      html: `
        <div class="w-6 h-6 bg-blue-500 border-2 border-white rounded-full shadow-lg animate-pulse">
          <div class="w-2 h-2 bg-white rounded-full mx-auto mt-1"></div>
        </div>
      `,
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    })
  }

  async loadNearbyEvents(lat, lng, radius = 10) {
    try {
      const baseUrl = this.apiUrlValue.replace('map_data', 'nearby_events')
      const separator = baseUrl.includes('?') ? '&' : '?'
      const response = await fetch(`${baseUrl}${separator}latitude=${lat}&longitude=${lng}&radius=${radius}`, {
        headers: { 'Accept': 'application/json' }
      })

      if (!response.ok) throw new Error('Failed to load nearby events')

      const data = await response.json()
      this.displayEvents(data.events)

      // 显示搜索范围圆圈
      this.showSearchRadius(lat, lng, radius)
    } catch (error) {
      console.error('Failed to load nearby events:', error)
    }
  }

  // 显示搜索范围圆圈
  showSearchRadius(lat, lng, radius) {
    const latitude = Number(lat)
    const longitude = Number(lng)
    const radiusKm = Number(radius)

    // 验证坐标有效性
    if (!isFinite(latitude) || !isFinite(longitude)) {
      console.error('showSearchRadius: invalid coordinates', { lat, lng })
      return
    }

    // 移除旧圆圈
    if (this.searchRadiusCircle) {
      this.map.removeLayer(this.searchRadiusCircle)
    }

    try {
      // 创建搜索范围圆圈
      this.searchRadiusCircle = this.L.circle([latitude, longitude], {
        radius: radiusKm * 1000, // 转换为米
        fillColor: '#3B82F6',
        fillOpacity: 0.1,
        color: '#3B82F6',
        weight: 2,
        opacity: 0.5
      }).addTo(this.map)
    } catch (error) {
      console.error('Error creating search radius circle:', error)
    }
  }

  updateRadius(event) {
    const radius = parseFloat(event.target.value)
    this.searchRadius = radius

    // 使用保存的用户位置坐标
    if (this.userLocation && this.userLocation.lat && this.userLocation.lng) {
      const lat = this.userLocation.lat
      const lng = this.userLocation.lng

      // 验证坐标有效性
      if (isFinite(lat) && isFinite(lng)) {
        this.showSearchRadius(lat, lng, radius)
        this.loadNearbyEvents(lat, lng, radius)
      } else {
        console.error('updateRadius: invalid saved coordinates', { lat, lng })
      }
    } else {
      console.warn('updateRadius: no user location available, user needs to locate first')
    }
  }

  // 工具方法
  clearMarkers() {
    if (this.markerCluster) {
      this.markerCluster.clearLayers()
    } else {
      this.markers.clearLayers()
    }
  }

  fitMapToBounds(bounds) {
    if (!bounds) return

    const leafletBounds = this.L.latLngBounds([
      [bounds.south, bounds.west],
      [bounds.north, bounds.east]
    ])

    this.map.fitBounds(leafletBounds, { padding: [20, 20] })
  }

  // 事件处理
  onMarkerClick() {
    // 处理标记点击事件
  }

  onMapMove() {
    // 地图移动时的处理逻辑
  }

  onMapZoom() {
    // 地图缩放时的处理逻辑
  }

  // UI 状态管理
  showLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.remove('hidden')
    }
  }

  hideLoading() {
    if (this.hasLoadingTarget) {
      this.loadingTarget.classList.add('hidden')
    }
  }

  showError(message) {
    this.hideLoading()
    if (this.hasErrorTarget) {
      this.errorTarget.textContent = message
      this.errorTarget.classList.remove('hidden')
    }
  }

  showLocationLoading() {
    // 显示定位加载状态 - 使用控制器作用域内的按钮选择器
    const button = this.element.querySelector('[data-action*="locateUser"]')
    if (button) {
      // 保存原始内容以便恢复
      if (!button.dataset.originalContent) {
        button.dataset.originalContent = button.innerHTML
      }

      button.innerHTML = `
        <svg class="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      `
      button.disabled = true
      button.title = "定位中..."
    }
  }

  hideLocationLoading() {
    // 恢复定位按钮状态 - 使用控制器作用域内的按钮选择器
    const button = this.element.querySelector('[data-action*="locateUser"]')
    if (button) {
      // 恢复原始内容
      if (button.dataset.originalContent) {
        button.innerHTML = button.dataset.originalContent
      } else {
        button.innerHTML = `
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
          </svg>
        `
      }
      button.disabled = false
      button.title = "定位我的位置"
    }
  }

  // 重置地图视图到初始状态
  resetView() {
    if (this.map && this.initialCenter && this.initialZoom) {
      this.map.setView(this.initialCenter, this.initialZoom)

      // 移除用户位置标记和搜索范围
      if (this.userLocationMarker) {
        this.map.removeLayer(this.userLocationMarker)
        this.userLocationMarker = null
      }

      if (this.searchRadiusCircle) {
        this.map.removeLayer(this.searchRadiusCircle)
        this.searchRadiusCircle = null
      }

      // 清除保存的用户位置数据
      this.userLocation = null

      // 重新加载所有事件
      this.loadEvents()
    }
  }

  // 重试加载地图
  async retry() {
    this.hideError()
    this.showLoading()

    try {
      await this.loadEvents()
      this.hideLoading()
    } catch (error) {
      console.error('Retry failed:', error)
      this.showError('重试失败，请检查网络连接')
    }
  }

  // 隐藏错误状态
  hideError() {
    if (this.hasErrorTarget) {
      this.errorTarget.classList.add('hidden')
    }
  }




  // 搜索附近事件（使用现有的loadNearbyEvents方法）
  searchNearbyEvents(location, radius) {
    if (location && location.length === 2) {
      this.loadNearbyEvents(location[0], location[1], radius)
    }
  }
}
