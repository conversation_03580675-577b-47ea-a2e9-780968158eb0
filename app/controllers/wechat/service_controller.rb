class Wechat::ServiceController < ApplicationController
  include WechatInteractions

  # 处理关注事件
  on :event, with: "subscribe" do |request|
    openid = request[:FromUserName]
    user_info, service_identity = find_or_create_user_with_identity(openid)
    store_wechat_session(request, user_info, service_identity)

    # 发送欢迎消息
    request.reply.text "Welcome! 「架起女性与编程的桥梁」\n\n Send 'help' for commands.\n\n Send 'account' command or click menu '帐号' to query or create an account. \n\n You can also send pictures or voice messages. Enjoy your journey!"

    # nickname = user_info.[]("nickname") || "朋友"
    # request.reply.markdown(
    #   "# 欢迎关注 Coding Girls Club！\n\n" +
    #   "你好，#{nickname}！\n\n" +
    #   "**「架起女性与编程的桥梁」** \n\n" +
    #   "👉 发送 'help' 查看可用命令\n" +
    #   "👉 发送 'account' 管理你的账户\n" +
    #   "👉 点击菜单【帐号】管理你的账户\n\n" +
    #   "[访问官网](https://codingirlsclub.com/)"
    # )
  end

  # 处理图片消息
  on :image do |request|
    request.reply.text "Nice picture! ( #{request[:PicUrl]} )"
  end

  # 处理语音消息
  on :voice do |request|
    request.reply.text "服务号已收到您的语音消息"
  end

  # 处理位置消息
  on :location do |request|
    request.reply.text "服务号已收到您的位置消息"
  end

  # 处理异常
  rescue_from StandardError do |exception|
    Rails.logger.error "ServiceController Error: #{exception.message}" # Updated class name in log
    render plain: "success" # 返回成功响应给微信服务器，避免重试
  end
end
