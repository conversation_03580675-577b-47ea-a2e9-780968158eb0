require 'rails_helper'

RSpec.describe 'Static Pages', type: :feature do
  describe 'Home page' do
    before { visit root_path }

    it 'displays the main title and subtitle' do
      expect(page).to have_content('Coding Girls Club')
      expect(page).to have_content('CODING GIRLS CLUB')
    end

    it 'shows the mission and vision section' do
      expect(page).to have_content('Mission and Vision')
      expect(page).to have_content('Mission')
      expect(page).to have_content('Vision')
    end

    it 'displays core products section' do
      expect(page).to have_content('Core Products')
      expect(page).to have_content('Coding Girls Day')
      expect(page).to have_content('One-Day Coding')
      expect(page).to have_content('"400+300" Vocational Training')
      expect(page).to have_content('Software Development Empowerment')
    end

    it 'shows achievements section' do
      expect(page).to have_content('Project Achievements')
      expect(page).to have_content('10+ cities')
      expect(page).to have_content('17+ universities')
      expect(page).to have_content('4000+ students')
      expect(page).to have_content('1000+ coaches')
    end
  end

  describe 'About page' do
    before { visit about_path }

    it 'displays the about page title and sections' do
      expect(page).to have_content('About Us')
      expect(page).to have_content('Organization Introduction')
      expect(page).to have_content('Organization Honors')
      expect(page).to have_content('Contact Us')
      expect(page).to have_content('<EMAIL>')
    end
  end

  describe 'Privacy Policy page' do
    before { visit privacy_policy_path }

    it 'displays the privacy policy title and sections' do
      expect(page).to have_content('Privacy Policy')
      expect(page).to have_content('Information Collection')
      expect(page).to have_content('Information Usage')
      expect(page).to have_content('Information Protection')
      expect(page).to have_content('Cookie Usage')
    end
  end

  describe 'Terms page' do
    before { visit terms_path }

    it 'displays the terms title and sections' do
      expect(page).to have_content('Terms of Service')
      expect(page).to have_content('Service Description')
      expect(page).to have_content('User Responsibilities')
      expect(page).to have_content('Payment and Refund')
      expect(page).to have_content('Intellectual Property')
    end
  end
end
