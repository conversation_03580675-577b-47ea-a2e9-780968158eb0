FactoryBot.define do
  factory :notification, class: 'Noticed::Notification' do
    read_at { nil }
    seen_at { nil }
    association :recipient, factory: :user
    association :event, factory: :noticed_event

    trait :read do
      read_at { 1.hour.ago }
    end

    trait :seen do
      seen_at { 30.minutes.ago }
    end

    trait :system do
      association :event, factory: [:noticed_event, :system]
    end

    trait :event_reminder do
      association :event, factory: [:noticed_event, :event_reminder]
    end

    # 为特定用户创建通知的便利特性
    trait :for_user do
      transient do
        user { nil }
      end

      after(:build) do |notification, evaluator|
        if evaluator.user
          notification.recipient = evaluator.user
        end
      end
    end
  end

  # 为 Noticed::Event 创建工厂
  factory :noticed_event, class: 'Noticed::Event' do
    type { 'SystemNotifier' }
    params { { message: 'Test notification message' } }

    trait :system do
      type { 'SystemNotifier' }
      params { { message: 'System notification message' } }
    end

    trait :event_reminder do
      type { 'EventReminderNotifier' }
      params do
        event_instance = create(:event)
        {
          event: event_instance.id,
          reminder_type: 'day_before'
        }
      end
    end

    trait :comment do
      type { 'CommentNotifier' }
      params do
        {
          comment_id: 1,
          message: 'You have a new comment'
        }
      end
    end
  end
end
