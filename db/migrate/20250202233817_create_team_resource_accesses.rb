class CreateTeamResourceAccesses < ActiveRecord::Migration[8.0]
  def change
    create_table :team_resource_accesses do |t|
      t.references :team_member, null: false, foreign_key: true
      t.references :resource, polymorphic: true, null: false
      t.string :resource_kind, null: false
      t.string :role, null: false

      t.timestamps
    end

    # 添加唯一性索引，确保每个团队成员对每个资源只有一个访问记录
    add_index :team_resource_accesses, [ :team_member_id, :resource_type, :resource_id ],
              unique: true,
              name: 'index_team_resource_accesses_on_team_member_and_resource'

    # 添加资源类型和角色的组合索引，用于快速查询特定类型资源的特定角色访问
    add_index :team_resource_accesses, [ :resource_kind, :role ],
              name: 'index_team_resource_accesses_on_resource_kind_and_role'
  end
end
