<% if events.any? %>
  <% events.each do |event| %>
    <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
      <div class="flex items-start justify-between">
        <div class="flex-1">
          <div class="flex items-center space-x-2 mb-2">
            <span class="inline-block w-3 h-3 rounded-full <%= event_category_color_class(event.event_category) %>"></span>
            <span class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <%= event.event_category&.name || t('events.show.uncategorized') %>
            </span>
            <% if event.price > 0 %>
              <span class="text-xs text-green-600 dark:text-green-400 font-medium">
                ¥<%= event.price %>
              </span>
            <% else %>
              <span class="text-xs text-blue-600 dark:text-blue-400 font-medium">
                <%= t('events.index.free') %>
              </span>
            <% end %>
          </div>
          
          <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2">
            <%= link_to event.title, event_path(event), class: "hover:text-primary-600 dark:hover:text-primary-400" %>
          </h4>
          
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
            <%= truncate(event.description, length: 100) %>
          </p>
          
          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div class="flex items-center space-x-4">
              <span class="flex items-center">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                <%= l(event.start_time, format: :short) %>
              </span>
              
              <span class="flex items-center">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                </svg>
                <%= event.is_online? ? t('events.location.online') : (event.event_location&.name || t('events.show.location_tbd')) %>
              </span>
              
              <% if event.max_participants %>
                <span class="flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                  </svg>
                  <%= event.registered_count %>/<%= event.max_participants %>
                </span>
              <% end %>
            </div>
            
            <div class="flex items-center space-x-2">
              <% if event.difficulty_level %>
                <%= event_difficulty_badge(event) %>
              <% end %>
              
              <%= link_to t('common.actions.view_details'), 
                  event_path(event), 
                  class: "text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>
<% else %>
  <div class="text-center py-8">
    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z" />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
      <%= t('events.calendar.no_events') %>
    </h3>
    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
      <%= t('events.calendar.no_events_description') %>
    </p>
  </div>
<% end %> 