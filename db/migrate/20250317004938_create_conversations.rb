class CreateConversations < ActiveRecord::Migration[8.0]
  def change
    create_table :conversations do |t|
      t.references :user, null: false, foreign_key: true
      t.references :assistant, null: false, foreign_key: true
      t.string :title, null: false
      t.timestamps
    end

    # 添加复合索引支持按用户查询最新对话
    add_index :conversations, [ :user_id, :created_at ]

    # 添加复合索引支持按助手和创建时间查询
    add_index :conversations, [ :assistant_id, :created_at ]
  end
end
