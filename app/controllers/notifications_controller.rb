class NotificationsController < ApplicationController
  before_action :set_notification, only: [:show, :update]

  def index
    authorize! with: NotificationPolicy # 检查用户是否有权限访问通知索引
    
    @filter = params[:filter] || 'all'
    
    # 使用 policy scope 来获取用户有权限访问的通知
    @notifications = authorized_scope(current_user&.notifications&.order(created_at: :desc) || Noticed::Notification.none, with: NotificationPolicy)
    
    # 根据过滤条件筛选通知
    case @filter
    when 'unread'
      @notifications = @notifications.where(read_at: nil)
    when 'read'
      @notifications = @notifications.where.not(read_at: nil)
    else
      # 'all' - 显示所有通知
    end
    
    @notifications_by_date = @notifications.group_by do |notification|
      if notification.created_at.today?
        t('notifications.index.date_groups.today')
      elsif notification.created_at.yesterday?
        t('notifications.index.date_groups.yesterday')
      elsif notification.created_at > 1.week.ago
        t('notifications.index.date_groups.this_week')
      else
        t('notifications.index.date_groups.earlier')
      end
    end
    
    # 计算统计信息
    if current_user
      @total_count = current_user.notifications.count
      @unread_count = current_user.notifications.where(read_at: nil).count
      @read_count = current_user.notifications.where.not(read_at: nil).count
    else
      @total_count = @unread_count = @read_count = 0
    end
  end

  def show
    authorize! @notification, with: NotificationPolicy
    @notification.mark_as_read! unless @notification.read?
  end

  def update
    authorize! @notification, with: NotificationPolicy
    @notification.mark_as_read!
    render json: { success: true }
  end

  def mark_all_read
    authorize! with: NotificationPolicy # 检查用户是否有权限标记所有通知为已读
    current_user&.mark_all_notifications_as_read!
    respond_to do |format|
      format.html { redirect_to notifications_path(format: :html), notice: t('notifications.index.messages.mark_all_success') }
      format.json { render json: { success: true } }
    end
  end

  private

  def set_notification
    # 使用 authorized_scope 来确保只能访问有权限的通知
    @notification = authorized_scope(Noticed::Notification.all, with: NotificationPolicy).find(params[:id])
  rescue ActiveRecord::RecordNotFound
    if request.format.json?
      render json: { error: t('notifications.index.messages.not_found') }, status: :not_found
    else
      redirect_to notifications_path, alert: t('notifications.index.messages.not_found')
    end
  end
end
