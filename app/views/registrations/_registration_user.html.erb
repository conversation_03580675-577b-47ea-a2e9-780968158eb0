<section class="bg-gray-50 dark:bg-gray-900">
  <div class="flex flex-col items-center justify-center min-h-screen px-6 py-8 mx-auto">
      <div class="w-full max-w-md bg-white rounded-lg shadow-xs dark:border md:mt-0 dark:bg-gray-800 dark:border-gray-700">
      <div class="p-6 space-y-4 md:space-y-6">
        <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">
          <%= t('auth.registrations.title') %>
        </h1>

        <%= form_with(model: @user || User.new, url: registration_path,
            data: { turbo_frame: "_top" }, # Target the whole page on success/redirect
            html: {
              class: "space-y-4"
            }) do |f| %>
          <%= render "shared/error_messages", resource: @user %>
          <% is_oauth_origin = @user.email_address.present? && !@user.new_record? # Heuristic: if email is set on a new user instance, likely from OAuth %>

          <div>
            <%= f.label :username, t('auth.registrations.username'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.text_field :username,
                value: @user.username, # Ensure pre-filled value is used
                class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
                placeholder: t('auth.registrations.placeholders.username'),
                required: true %>
          </div>

          <div>
            <%= f.label :email_address, t('auth.registrations.email'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.email_field :email_address,
                value: @user.email_address, # Ensure pre-filled value is used
                readonly: is_oauth_origin, # Make readonly if from OAuth
                class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 #{is_oauth_origin ? 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed' : ''}",
                placeholder: t('auth.registrations.placeholders.email'),
                required: true %>
            <% if is_oauth_origin %>
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400"><%= t('auth.registrations.email_from_oauth') %></p>
            <% end %>
          </div>

          <%# Determine if phone number came from verification (should be readonly) %>
          <% is_phone_verified = @user.phone_number.present? && !is_oauth_origin %>

          <div>
            <%# Adjust label based on whether phone is verified or optional %>
            <%= f.label :phone_number, is_phone_verified ? t('auth.registrations.phone_verified') : t('auth.registrations.phone_optional'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.telephone_field :phone_number,
                value: @user.phone_number, # Ensure pre-filled value is used
                readonly: is_phone_verified, # Make readonly if from SMS verification
                class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 #{is_phone_verified ? 'bg-gray-100 dark:bg-gray-600 cursor-not-allowed' : ''}",
                placeholder: t('auth.registrations.placeholders.phone_optional') %>
             <% unless is_phone_verified %>
               <p class="mt-1 text-xs text-gray-500 dark:text-gray-400"><%= t('auth.registrations.phone_format_hint') %></p>
             <% end %>
          </div>

          <div>
            <%= f.label :password, t('auth.registrations.password'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.password_field :password, class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500", placeholder: t('auth.registrations.placeholders.password') %>
          </div>

          <div>
            <%= f.label :password_confirmation, t('auth.registrations.password_confirmation'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.password_field :password_confirmation, class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500", placeholder: t('auth.registrations.placeholders.password') %>
          </div>

          <%= f.hidden_field :step, value: "create_account" %>

          <%= f.submit t('auth.sign_up'), class: "w-full text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>

          <p class="text-sm font-light text-gray-500 dark:text-gray-400">
            <%= t('auth.registrations.account_exists') %> <%= link_to t('auth.sign_in'), new_session_path, class: "font-medium text-primary-600 hover:underline dark:text-primary-500", data: { turbo: false } %>
          </p>
        <% end %>
      </div>
    </div>
  </div>
</section>
