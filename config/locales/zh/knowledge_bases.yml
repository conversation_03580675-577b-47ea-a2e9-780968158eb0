# 知识库模块中文翻译
zh:
  knowledge_bases:
    index:
      title: "知识库"
      archived_title: "已归档知识库"
      empty_state: "未找到知识库"
    show:
      title: "知识库"
      posts: "文章"
      empty_state: "此知识库中没有文章"
      details: "详情"
      created: "创建时间"
      updated: "更新时间"
      visibility: "可见性"
      statistics: "统计"
      documents: "文档"
      contributors: "贡献者"
      team_settings: "团队设置"
      new_base_unit: "新建基础单元"
      delete_confirm: "确定要删除此知识库吗?"
    form:
      error: "表单提交时出现%{count}个错误:"
      name: "知识库名称"
      name_placeholder: "输入知识库名称"
      description: "描述"
      description_placeholder: "输入知识库描述"
      visibility: "可见性"
      team: "团队"
      save: "创建"
      cancel: "取消"
    new:
      title: "创建知识库"
      subtitle: "创建新知识库来组织和管理您的知识资源"
    edit:
      title: "编辑知识库"
      subtitle: "更新您的知识库信息"
    configure_team:
      title: "配置团队设置"
      subtitle: "请选择团队并配置访问级别"
      select_teams: "选择团队"
      select_prompt: "选择团队"
      skip: "跳过"
      update: "更新团队设置"
    manage_teams:
      title: "管理团队"
      subtitle: "添加或移除团队以管理对此知识库的访问"
      add_team: "添加团队"
      add_button: "添加团队"
      associated_teams: "关联团队"
      remove_team: "移除"
      remove_confirm: "确定要移除此团队吗?"
    posts:
      title: "知识库文章管理"
      subtitle: "管理与知识库关联的文章"
      available_posts: "可用文章"
      associated_posts: "关联文章"
      add: "添加文章"
      remove: "移除文章"
      import: "导入文章"
    actions:
      create: "创建知识库"
      archive: "归档"
      unarchive: "取消归档"
      delete: "删除"
      edit: "编辑"
      share: "分享"
      import: "导入内容"
      export: "导出内容"
    fields:
      name: "名称"
      description: "描述"
      visibility: "可见性"
      owner: "所有者"
    visibility:
      private: "私有"
      team: "仅团队"
      public: "公开"
