<!-- Notification dropdown -->
<div data-notifications-target="dropdown"
     class="z-50 hidden w-96 my-4 overflow-hidden text-base list-none bg-white divide-y divide-gray-100 rounded shadow-lg dark:divide-gray-600 dark:bg-gray-700" 
     id="notification-dropdown">
  <div class="block px-4 py-2 text-base font-medium text-center text-gray-700 bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
    Notifications
  </div>
  <div data-notifications-target="list">
    <% if authenticated? && current_user.unread_notifications.any? %>
      <% current_user.unread_notifications.order(created_at: :desc).limit(5).each_with_index do |notification, index| %>
        <% 
          # 基于通知类型获取相关信息和图标
          icon_bg_color = ['bg-primary-700', 'bg-gray-900', 'bg-red-600', 'bg-green-400', 'bg-purple-500'][index % 5]
          
          if notification.event.type == 'SystemNotifier'
            message = notification.event.params[:message]
            icon_svg = '<path d="M8.707 7.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l2-2a1 1 0 00-1.414-1.414L11 7.586V3a1 1 0 10-2 0v4.586l-.293-.293z"></path><path d="M3 5a2 2 0 012-2h1a1 1 0 010 2H5v7h2l1 2h4l1-2h2V5h-1a1 1 0 110-2h1a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"></path>'
            url = '#'
            avatar_user = nil
          elsif notification.event.type == 'EventReminderNotifier'
            event = Event.find(notification.event.params[:event]) rescue nil
            message_parts = case notification.event.params[:reminder_type]
                      when 'day_before'
                        { bold: event&.title || '活动', text: " 将在明天开始" }
                      when 'hour_before'
                        { bold: event&.title || '活动', text: " 将在1小时后开始" }
                      when 'starting_soon'
                        { bold: event&.title || '活动', text: " 现在开始了" }
                      else
                        { bold: event&.title || '活动', text: " 提醒" }
                      end
            message = "<span class=\"font-semibold text-gray-900 dark:text-white\">#{message_parts[:bold]}</span>#{message_parts[:text]}"
            icon_svg = '<path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>'
            url = event ? event_path(event) : '#'
            avatar_user = event&.user
          else
            message = 'New notification'
            icon_svg = '<path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>'
            url = '#'
            avatar_user = nil
          end
        %>
        <a href="<%= notification_path(notification) %>" 
           class="flex px-4 py-3 border-b hover:bg-gray-100 dark:hover:bg-gray-600 dark:border-gray-600"
           data-notification-id="<%= notification.id %>"
           data-action="click->notifications#markAsRead">
          <div class="flex-shrink-0 relative">
            <% if avatar_user %>
              <%= render 'shared/avatar', user: avatar_user, size: 11, alt_text: "User avatar", css_class: "rounded-full w-11 h-11" %>
            <% else %>
              <div class="inline-flex items-center justify-center w-11 h-11 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
                <span class="font-medium text-gray-600 dark:text-gray-300">SYS</span>
              </div>
            <% end %>
            <div class="absolute flex items-center justify-center w-5 h-5 -mt-5 ml-6 border border-white rounded-full <%= icon_bg_color %> dark:border-gray-700">
              <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><%= icon_svg.html_safe %></svg>
            </div>
          </div>
          <div class="w-full pl-3">
            <div class="text-gray-500 font-normal text-sm mb-1.5 dark:text-gray-400">
              <%= message.html_safe %>
            </div>
            <div class="text-xs font-medium text-primary-700 dark:text-primary-400">
              <%= t('common.time_ago', time: time_ago_in_words(notification.created_at)) %>
            </div>
          </div>
        </a>
      <% end %>
    <% else %>
      <div class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
        <%= t('layouts.dashboard.navbar.notifications.no_notifications', default: '暂无通知') %>
      </div>
    <% end %>
  </div>
  <a href="<%= notifications_path %>" class="block py-2 text-base font-normal text-center text-gray-900 bg-gray-50 hover:bg-gray-100 dark:bg-gray-700 dark:text-white dark:hover:underline">
    <div class="inline-flex items-center">
      <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
      </svg>
      View all
    </div>
  </a>
</div>
