<% content_for :title, t('events.registrations.title') %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <!-- 面包屑导航 -->
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { 
          items: [
            [t('events.index.title'), events_path],
            [@event.title, event_path(@event)],
            [t('events.registrations.title'), nil]
          ]
        } %>

        <!-- 页面标题和统计信息 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">
              <%= t('events.registrations.title') %>
            </h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              <%= t('events.registrations.event_title', event: @event.title) %>
            </p>
            <div class="mt-2 flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
              <span class="inline-flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                </svg>
                <%= t('events.registrations.total_count', count: @registrations.count) %>
              </span>
              <% if @event.max_participants %>
                <span class="inline-flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  <%= t('events.registrations.capacity', current: @registrations.count, max: @event.max_participants) %>
                </span>
              <% end %>
            </div>
          </div>
          <div class="mt-4 md:mt-0">
            <%= link_to event_path(@event), 
                class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" do %>
              <svg class="w-4 h-4 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              <%= t('events.registrations.back_to_event') %>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- 报名列表 -->
    <div class="bg-white dark:bg-gray-800 shadow-xs rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <% if @registrations.any? %>
        <!-- 表格头部 -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              <%= t('events.registrations.participant_list') %>
            </h3>
            <div class="flex items-center space-x-2">
              <!-- 状态筛选 -->
              <select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 p-2 dark:bg-gray-600 dark:border-gray-500 dark:text-white">
                <option value=""><%= t('events.registrations.all_statuses') %></option>
                <option value="confirmed"><%= t('events.registrations.statuses.confirmed') %></option>
                <option value="pending"><%= t('events.registrations.statuses.pending') %></option>
                <option value="cancelled"><%= t('events.registrations.statuses.cancelled') %></option>
              </select>
            </div>
          </div>
        </div>

        <!-- 表格内容 -->
        <div class="overflow-x-auto">
          <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
              <tr>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registrations.participant') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registrations.registered_at') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registrations.status') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registrations.amount_paid') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <%= t('events.registrations.notes') %>
                </th>
                <th scope="col" class="px-6 py-3">
                  <span class="sr-only"><%= t('common.actions.title') %></span>
                </th>
              </tr>
            </thead>
            <tbody>
              <% @registrations.each do |registration| %>
                <tr id="registration_<%= registration.id %>" class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200">
                  <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                    <div class="flex items-center">
                      <%= render 'shared/avatar', user: registration.user, size: 'h-8 w-8' %>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          <%= registration.user.username || registration.user.email_address %>
                        </div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                          <%= registration.user.email_address %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 dark:text-white">
                      <%= l(registration.registered_at, format: :short) %>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      <%= t('common.time_ago', time: time_ago_in_words(registration.registered_at)) %>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= registration_status_class(registration.status) %>">
                      <%= t("events.registrations.statuses.#{registration.status}") %>
                    </span>
                  </td>
                  <td class="px-6 py-4">
                    <% if registration.amount_paid > 0 %>
                      <span class="text-sm font-medium text-gray-900 dark:text-white">
                        ¥<%= number_with_precision(registration.amount_paid, precision: 2) %>
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                        <%= t('events.show.free') %>
                      </span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 text-sm max-w-xs">
                    <% if registration.notes.present? %>
                      <div class="truncate" title="<%= registration.notes %>">
                        <%= truncate(registration.notes, length: 50) %>
                      </div>
                    <% else %>
                      <span class="text-gray-400 dark:text-gray-500">-</span>
                    <% end %>
                  </td>
                  <td class="px-6 py-4 text-right">
                    <div class="flex items-center justify-end space-x-2">
                      <% if registration.status == 'pending' %>
                        <%= button_to t('events.registrations.confirm'), 
                            confirm_event_event_registration_path(@event, registration),
                            method: :patch,
                            class: "inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-800 dark:text-green-100 dark:hover:bg-green-700" %>
                      <% end %>
                      
                      <%= button_to t('events.registrations.cancel'), 
                          cancel_event_event_registration_path(@event, registration),
                          method: :patch,
                          data: { confirm: t('events.registrations.cancel_confirm') },
                          class: "inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-red-800 dark:text-red-100 dark:hover:bg-red-700" %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- 表格底部统计 -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
          <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div>
              <%= t('events.registrations.showing_results', count: @registrations.count) %>
            </div>
            <% if @registrations.respond_to?(:total_pages) && @registrations.total_pages > 1 %>
              <div>
                <%= paginate @registrations %>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <!-- 空状态 -->
        <div class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
          </svg>
          <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">
            <%= t('events.registrations.no_registrations') %>
          </h3>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            <%= t('events.registrations.no_registrations_desc') %>
          </p>
          <div class="mt-6">
            <%= link_to event_path(@event), 
                class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-primary-800 dark:text-primary-100 dark:hover:bg-primary-700" do %>
              <%= t('events.registrations.view_event_details') %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div> 