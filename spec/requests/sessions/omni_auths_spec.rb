require 'rails_helper'

RSpec.describe "Sessions::OmniAuths", type: :request do
  # Stub ApplicationController#extract_locale to force English for these tests
  before do
    allow_any_instance_of(ApplicationController).to receive(:extract_locale).and_return(:en)
  end

  let(:user) { create(:user) }
  let(:user_with_identity) { create(:user, :with_google_auth) }

  describe "GET /auth/:provider/callback" do
    context "用户未登录时" do
      context "已存在用户但没有关联的第三方身份" do
        it "不创建新用户，创建身份并登录" do
          user.sessions.destroy_all

          mock_omniauth_provider("github",
                               email: user.email_address,
                               uid: "github_unique_123")

          expect { get "/auth/github/callback" }.not_to change(User, :count)
          expect(response).to redirect_to(root_path)
          expect(user.omni_auth_identities.find_by(provider: "github", uid: "github_unique_123")).to be_present
          expect(flash[:notice]).to eq(I18n.t("auth.omniauth.signed_in", provider: "Github"))
        end
      end

      context "已存在用户且有关联的第三方身份" do
        let!(:identity) { create(:omni_auth_identity, :github, user: user_with_identity, uid: "github_unique_123") }

        it "不创建新用户，直接登录" do
          user_with_identity.sessions.destroy_all

          mock_omniauth_provider("github",
                               email: user_with_identity.email_address,
                               uid: "github_unique_123")

          expect { get "/auth/github/callback" }.not_to change(User, :count)
          expect(response).to redirect_to(root_path)
          expect(user_with_identity.omni_auth_identities.find_by(provider: "github", uid: "github_unique_123")).to be_present
          expect(flash[:notice]).to eq(I18n.t("auth.omniauth.signed_in", provider: "Github"))
        end
      end

      context "全新用户" do
        it "创建新用户和身份并登录" do
          new_email = "<EMAIL>"

          mock_omniauth_provider("wechat",
                               email: new_email,
                               uid: "wechat_unique_456",
                               unionid: "wechat_unionid_456",
                               name: "微信用户",
                               nickname: "WeChatUser",
                               gender: "1",
                               location: "中国北京")

          expect { get "/auth/wechat/callback" }.to change(User, :count).by(1)
          expect(response).to redirect_to(root_path)
          new_user = User.find_by(email_address: new_email)
          expect(new_user.omni_auth_identities.find_by(provider: "wechat", uid: "wechat_unique_456", unionid: "wechat_unionid_456")).to be_present
          expect(flash[:notice]).to eq(I18n.t("auth.omniauth.signed_up_and_in", provider: "Wechat"))
        end
      end
    end # Closes context "用户未登录时"

    context "用户已登录时" do
      let!(:user_session) { user.sessions.create! } # Create a session for the user
      let(:omniauth_params) { { "origin_path" => settings_users_path } } # Define origin path for logged-in redirects

      context "尝试关联新的第三方账号" do
        it "创建新的身份关联但不创建新用户" do
          mock_omniauth_provider("github",
                               email: "<EMAIL>",
                               uid: "github_unique_789")

          login_as(user)

          # Check identity count change and user count separately
          initial_user_count = User.count
          expect {
            # Pass omniauth_params in the request env
            get "/auth/github/callback", params: {}, env: { "omniauth.params" => omniauth_params, "omniauth.auth" => OmniAuth.config.mock_auth[:github] }
          }.to change { user.omni_auth_identities.count }.by(1)
          expect(User.count).to eq(initial_user_count) # Ensure user count does not change

          expect(response).to redirect_to(settings_users_path) # Correct redirect path
          expect(user.omni_auth_identities.find_by(provider: "github", uid: "github_unique_789")).to be_present
          expect(flash[:notice]).to eq(I18n.t("auth.omniauth.linked", provider: "Github"))
        end
      end

      context "尝试关联已存在的第三方账号" do
        let!(:identity) { create(:omni_auth_identity, :google, user: user, uid: "google_unique_123") }

        it "不创建新的身份关联，并提示已关联" do
          mock_omniauth_provider("google_oauth2",
                               email: user.email_address,
                               uid: "google_unique_123")

          login_as(user)

          # Pass omniauth_params in the request env
          expect { get "/auth/google_oauth2/callback", params: {}, env: { "omniauth.params" => omniauth_params, "omniauth.auth" => OmniAuth.config.mock_auth[:google_oauth2] } }.not_to change(OmniAuthIdentity, :count)
          expect(response).to redirect_to(settings_users_path) # Correct redirect path
          expect(flash[:notice]).to eq(I18n.t("auth.omniauth.already_linked", provider: "Google oauth2")) # Use humanized provider name
        end
      end # Closes context "尝试关联已存在的第三方账号"

      context "尝试关联已被其他用户绑定的账号" do
        let(:other_user) { create(:user) }

        before do
          create(:omni_auth_identity, provider: "github", uid: "others_uid", user: other_user)
        end

        it "不允许关联且提示错误" do
          mock_omniauth_provider("github",
                              email: other_user.email_address,
                               uid: "others_uid")

          login_as(user)

          # Pass omniauth_params in the request env
          expect { get "/auth/github/callback", params: {}, env: { "omniauth.params" => omniauth_params, "omniauth.auth" => OmniAuth.config.mock_auth[:github] } }.not_to change(OmniAuthIdentity, :count)
          expect(response).to redirect_to(settings_users_path) # Correct redirect path when logged in
          expect(flash[:alert]).to eq(I18n.t("auth.omniauth.mismatch", provider: "Github"))
        end
      end
    end # Closes context "用户已登录时"
  end # Closes describe "GET /auth/:provider/callback"

  describe "GET /auth/failure" do
    it "重定向到登录页面并显示错误" do
      get "/auth/failure"

      expect(response).to redirect_to new_session_path
      follow_redirect!
      # Use I18n.t for expectation, match specific failure message
      expect(response.body).to match(I18n.t("auth.omniauth.failure", provider: "Authentication"))
    end
  end

  # 测试辅助方法
  def mock_omniauth_provider(provider, email:, uid:, name: nil, nickname: nil, gender: nil, location: nil, unionid: nil)
    OmniAuth.config.test_mode = true
    # 基础认证数据
    auth_data = {
      provider: provider,
      uid: uid,
      unionid: unionid,
      info: {
        email: email,
        name: name,
        nickname: nickname,
        gender: gender,
        location: location
      }.compact
    }
    OmniAuth.config.mock_auth[provider.to_sym] = OmniAuth::AuthHash.new(auth_data)
  end
end
