name: Workflow - Deployment (staging)

on:
  push:
    branches: [master] # master 分支推送时触发

concurrency:
  group: deployment-staging
  cancel-in-progress: false

jobs:
  deploy:
    uses: ./.github/workflows/job-deploy-kamal.yml
    permissions:
      contents: read
      packages: write
      id-token: write
    with:
      kamal-destination: staging
    secrets:
      KAMAL_REGISTRY_USERNAME: ${{ secrets.KAMAL_REGISTRY_USERNAME }}
      KAMAL_REGISTRY_PASSWORD: ${{ secrets.KAMAL_REGISTRY_PASSWORD }}
      SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
      RAILS_MASTER_KEY: ${{ secrets.RAILS_MASTER_KEY }}
