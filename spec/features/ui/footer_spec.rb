require 'rails_helper'

RSpec.describe 'Footer', type: :feature do
  before do
    visit root_path
  end

  describe 'footer sections' do
    it 'displays company section with correct links' do
      within('footer') do
        within('div.grid-cols-2 > div:first-child') do
          expect(page).to have_content('About Us')
          expect(page).to have_link('About Us', href: about_path)
          expect(page).to have_link('Contact Us', href: '#')
          expect(page).to have_link('Blog', href: '#')
        end
      end
    end

    it 'displays learn section with correct links' do
      within('footer') do
        within('div.grid-cols-2 > div:nth-child(2)') do
          expect(page).to have_content('Learning Resources')
          expect(page).to have_link('Courses', href: '#')
          expect(page).to have_link('Tutorials', href: '#')
        end
      end
    end

    it 'displays legal section with correct links' do
      within('footer') do
        within('div.grid-cols-2 > div:last-child') do
          expect(page).to have_content('Legal')
          expect(page).to have_link('Privacy Policy', href: privacy_policy_path)
          expect(page).to have_link('Terms of Service', href: terms_path)
        end
      end
    end
  end

  describe 'footer navigation' do
    it 'has clickable links' do
      within('footer') do
        expect(page).to have_link('About Us', href: about_path)
        expect(page).to have_link('Privacy Policy', href: privacy_policy_path)
        expect(page).to have_link('Terms of Service', href: terms_path)
      end
    end
  end

  it 'displays copyright information' do
    within('footer') do
      expect(page).to have_content("© #{Time.current.year}")
      expect(page).to have_content('All Rights Reserved')
    end
  end
end
