class ApplicationPolicy < ActionPolicy::Base
  # 配置默认的授权上下文
  authorize :user, allow_nil: true

  # 默认规则
  default_rule :manage?
  alias_rule :create?, :update?, :destroy?, to: :manage?

  protected

  # 检查用户是否已认证
  def authenticated?
    user.present?
  end

  # 检查用户是否为管理员
  def admin?
    user&.admin?
  end

  # 统一owner检查方法（同时支持直接调用和作为pre_check）
  def owner?
    return false unless user

    # 支持两种命名方式：owner 或 user
    if record.respond_to?(:owner)
      record.owner == user
    elsif record.respond_to?(:user)
      record.user == user
    else
      false
    end
  end

  def check_authenticated!
    allow! if authenticated?
  end

  def check_admin!
    allow! if admin?
  end

  # 增强的通用权限检查方法
  def check_ownership!
    allow! if owner?
  end

  # 默认的manage?方法
  def manage?
    false  # 默认拒绝访问，子类应该覆盖这个方法
  end
end
