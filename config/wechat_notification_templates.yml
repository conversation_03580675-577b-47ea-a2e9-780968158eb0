# 微信通知模板配置
# 需要在微信公众平台申请对应的模板消息ID

development:
  # 活动注册通知模板 - 预约成功提醒
  event_registration:
    template_id: "uHM5RigbUMfoB3ad9dGKPLEP5E3h81SqJlMe4_gi5J8" # 实际模板ID
    template_number: "43843"
    template_name: "预约成功提醒"
    category: "在线教育"
    description: "讲座活动预约通知"
    data_keys:
      - thing30 # 讲座名称
      - time33 # 讲座时间
      - thing14 # 上课地址
      - amount49 # 金额
      - character_string11 # 课程链接
    jump_text: "点击查看详情"

  # 活动提醒通知模板
  event_reminder:
    template_id: "BcDeFgHiJkLmNoPqRsTuVwXyZa123456" # 替换为实际模板ID
    data_keys:
      - first # 通知开头
      - keyword1 # 活动名称
      - keyword2 # 活动时间
      - keyword3 # 提醒类型
      - remark # 备注信息

  # 系统通知模板
  system_notification:
    template_id: "CdEfGhIjKlMnOpQrStUvWxYzAb234567" # 替换为实际模板ID
    data_keys:
      - first # 通知开头
      - keyword1 # 通知类型
      - keyword2 # 通知时间
      - remark # 备注信息

production:
  # 生产环境的模板ID需要在微信公众平台申请
  event_registration:
    template_id: <%= ENV['WECHAT_EVENT_REGISTRATION_TEMPLATE_ID'] || 'uHM5RigbUMfoB3ad9dGKPLEP5E3h81SqJlMe4_gi5J8' %>
    template_number: "43843"
    template_name: "预约成功提醒"
    category: "在线教育"
    description: "讲座活动预约通知"
    data_keys:
      - thing30 # 讲座名称
      - time33 # 讲座时间
      - thing14 # 上课地址
      - amount49 # 金额
      - character_string11 # 课程链接
    jump_text: "点击查看详情"

  event_reminder:
    template_id: <%= ENV['WECHAT_EVENT_REMINDER_TEMPLATE_ID'] %>
    data_keys:
      - first
      - keyword1
      - keyword2
      - keyword3
      - remark

  system_notification:
    template_id: <%= ENV['WECHAT_SYSTEM_NOTIFICATION_TEMPLATE_ID'] %>
    data_keys:
      - first
      - keyword1
      - keyword2
      - remark

test:
  # 测试环境可以使用模拟的模板ID
  event_registration:
    template_id: "TEST_EVENT_REGISTRATION_TEMPLATE_ID"
    template_number: "43843"
    template_name: "预约成功提醒"
    category: "在线教育"
    description: "讲座活动预约通知"
    data_keys:
      - thing30 # 讲座名称
      - time33 # 讲座时间
      - thing14 # 上课地址
      - amount49 # 金额
      - character_string11 # 课程链接
    jump_text: "点击查看详情"

  event_reminder:
    template_id: "TEST_EVENT_REMINDER_TEMPLATE_ID"
    data_keys:
      - first
      - keyword1
      - keyword2
      - keyword3
      - remark

  system_notification:
    template_id: "TEST_SYSTEM_NOTIFICATION_TEMPLATE_ID"
    data_keys:
      - first
      - keyword1
      - keyword2
      - remark
