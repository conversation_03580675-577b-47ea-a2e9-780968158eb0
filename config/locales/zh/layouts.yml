# 布局和导航组件中文翻译
zh:
  layouts:
    public: # 用于 application.html.erb, _navbar.html.erb, _footer.html.erb
      navbar:
        courses: "课程"
        tutorials: "教程"
        blog: "博客"
        # 关于/联系链接使用 static_pages 中的键
      footer:
        description: "通过数字教育赋能女性，通过编程创造更多可能性。"
        download:
          title: "下载"
          ios: "iOS应用"
          android: "Android应用"
        learning_resources: "学习资源"
        resources:
          courses: "课程"
          tutorials: "教程"
        legal: "法律"
        blog: "博客"
        about_us: "关于我们" # 页脚特定标题
        contact: "联系我们" # 添加联系键
    dashboard: # 用于 user_dashboard.html.erb, _navbar_dashboard.html.erb, _footer_dashboard.html.erb, _sidebar.html.erb
      navbar:
        toggle_sidebar: "切换侧边栏"
        search:
          sr_label: "搜索"
          placeholder: "搜索"
        notifications:
          sr_label: "查看通知"
          title: "通知"
          new_message: '来自 <span class="font-semibold text-gray-900 dark:text-white"><PERSON></span> 的新消息: "嘿！你好吗？准备好演示了吗？"'
          follow: '<span class="font-semibold text-gray-900 dark:text-white">Jese leos</span> 开始关注你。'
          upgrade_request: '<span class="font-semibold text-gray-900 dark:text-white">Bonnie Green</span> 请求升级Flowbite计划。'
          love_story: '<span class="font-semibold text-gray-900 dark:text-white">Joseph Mcfall</span> 和 <span class="font-medium text-gray-900 dark:text-white">141人</span> 喜欢你的故事。查看更多故事。'
          time_ago: "刚刚"
          minutes_ago:
            one: "%{count}分钟前"
            other: "%{count}分钟前"
          accept: "接受"
          decline: "拒绝"
          view_all: "查看全部"
        apps:
          sr_label: "查看应用"
          title: "应用"
          events: "活动"
          ai_chat: "AI对话"
          knowledge: "知识库"
          inbox: "收件箱"
          profile: "个人资料"
          settings: "设置"
          products: "产品"
          pricing: "定价"
        theme:
          toggle_tooltip: "切换主题模式"
        user_menu:
          sr_label: "打开用户菜单"
          avatar_alt: "用户头像"
          guest: "访客"
          not_logged_in: "未登录"
          dashboard: "仪表盘" # 重用 sidebar.dashboard
          settings: "设置" # 重用 users.settings.title
          # 退出登录使用 auth.sign_out
      sidebar: # 合并的侧边栏键
        search:
          placeholder: "搜索"
          mobile_label: "移动搜索"
          sr_label: "搜索" # 重用 dashboard.navbar.search.sr_label
        dashboard: "仪表盘"
        # 主菜单项重用功能模块键(例如 posts.index.title)
        ai:
          title: "AI" # 可折叠部分的标题
          # assistants: 重用 ai.assistants.title
          # chat: 重用 ai.conversations.title
        knowledge_bases:
          title: "知识库" # 可折叠部分的标题
          # all: 重用 knowledge_bases.index.title
          # archived: 重用 knowledge_bases.index.archived_title
          # new: 重用 knowledge_bases.actions.create
        account_settings: # 从 settings 重命名
          title: "账户设置" # 可折叠部分的标题
          # profile: 重用 users.profile.title
          # subscriptions: 重用 users.subscriptions.title
        my_post: "我的文章"
        base_units: "基础单元"
        language_selector: "选择语言"
      footer:
        # 链接重用 static_pages 和 contact 键
        contact: "联系我们" # 添加联系键
