require 'rails_helper'

RSpec.describe ToolUsage, type: :model do
  describe '关联关系' do
    it { is_expected.to belong_to(:message) }
  end

  describe '验证' do
    it { is_expected.to validate_presence_of(:function_name) }

    describe '函数名唯一性' do
      let!(:existing_usage) { create(:tool_usage, function_name: "test_function") }

      it '在同一消息中不允许重复的函数名' do
        duplicate_usage = build(:tool_usage,
                               message: existing_usage.message,
                               function_name: "test_function")
        expect(duplicate_usage).not_to be_valid
        expect(duplicate_usage.errors[:function_name]).to include("This function has already been executed for this message")
      end

      it '允许在不同消息中使用相同的函数名' do
        other_message = create(:message)
        valid_usage = build(:tool_usage,
                           message: other_message,
                           function_name: "test_function")
        expect(valid_usage).to be_valid
      end
    end
  end

  describe '序列化' do
    it '将arguments序列化为JSON' do
      tool_usage = create(:tool_usage, arguments: { "test" => "value" })
      reloaded = ToolUsage.find(tool_usage.id)
      expect(reloaded.arguments).to eq({ "test" => "value" })
    end
  end

  describe '状态枚举' do
    let(:tool_usage) { build(:tool_usage) }

    it '定义了正确的枚举值' do
      expect(ToolUsage.statuses).to eq(
        'pending' => 'pending',
        'success' => 'success',
        'failed' => 'failed'
      )
    end

    it '有正确的枚举方法' do
      expect(tool_usage).to define_enum_for(:status)
        .with_values(pending: 'pending', success: 'success', failed: 'failed')
        .backed_by_column_of_type(:string)
        .with_prefix(:tool)
    end

    it '生成了正确的辅助方法' do
      expect(tool_usage).to respond_to(:tool_pending?)
      expect(tool_usage).to respond_to(:tool_success?)
      expect(tool_usage).to respond_to(:tool_failed?)
    end

    it '默认状态为pending' do
      expect(build(:tool_usage).status).to eq('pending')
    end
  end

  describe '状态变更' do
    let(:tool_usage) { create(:tool_usage, :pending) }

    it '可以从pending变为success' do
      tool_usage.status = 'success'
      tool_usage.result = { "data" => "Success result" }
      expect(tool_usage.save).to be true
      expect(tool_usage.reload.status).to eq('success')
    end

    it '可以从pending变为failed' do
      tool_usage.status = 'failed'
      tool_usage.result = { "error" => "Some error" }
      expect(tool_usage.save).to be true
      expect(tool_usage.reload.status).to eq('failed')
    end
  end

  describe '不同状态的工厂特性' do
    it '创建成功状态的工具使用' do
      tool_usage = create(:tool_usage, :success)
      expect(tool_usage.status).to eq('success')
      expect(tool_usage.result).to include("status" => "ok")
    end

    it '创建失败状态的工具使用' do
      tool_usage = create(:tool_usage, :failed)
      expect(tool_usage.status).to eq('failed')
      expect(tool_usage.result).to include("status" => "error")
    end

    it '创建待处理状态的工具使用' do
      tool_usage = create(:tool_usage, :pending)
      expect(tool_usage.status).to eq('pending')
      expect(tool_usage.result).to be_nil
    end
  end
end
