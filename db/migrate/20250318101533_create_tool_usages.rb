class CreateToolUsages < ActiveRecord::Migration[8.0]
  def change
    create_table :tool_usages do |t|
      t.references :message, null: false, foreign_key: true
      t.string :function_name, null: false
      t.string :status, null: false, default: "pending"
      t.text :arguments
      t.text :result
      t.datetime :started_at
      t.datetime :completed_at

      t.timestamps
    end

    # 添加复合索引，确保一个消息中同一个函数只能被执行一次
    add_index :tool_usages, [ :message_id, :function_name ], unique: true
  end
end
