<%#
  Events Map View Partial
  
  用法：
  render "events/map_view", current_tab: @current_tab
  
  参数：
  - current_tab: 当前标签页 ('upcoming', 'past', 'all')
%>

<!-- 地图视图容器 -->
<div data-view-switcher-target="mapContainer" class="<%= @current_view == 'map' ? '' : 'hidden' %>">
  <div data-controller="map"
       data-map-api-url-value="<%= map_data_events_path(tab: current_tab, **params.except(:page).permit!) %>"
       data-map-center-value="[32.0103, 119.5686]"
       data-map-zoom-value="6">

    <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700">

      <!-- 地图容器 - 改进的响应式高度 -->
      <div data-map-target="container"
           class="h-[50vh] min-h-[400px] max-h-[80vh] sm:h-[60vh] lg:h-[70vh] rounded-xl"></div>

      <!-- 地图加载状态 -->
      <div data-map-target="loading"
           class="absolute inset-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-xl flex items-center justify-center z-10">
        <div class="text-center p-6">
          <div class="relative">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-primary-200 border-t-primary-600 mx-auto mb-4"></div>
            <div class="absolute inset-0 rounded-full border-4 border-transparent border-t-primary-300 animate-pulse"></div>
          </div>
          <p class="text-base font-medium text-gray-700 dark:text-gray-300 mb-2"><%= t('events.index.map.loading_map') %></p>
          <p class="text-sm text-gray-500 dark:text-gray-400"><%= t('events.index.map.fetching_events') %></p>
        </div>
      </div>

      <!-- 地图错误状态 -->
      <div data-map-target="error"
           class="hidden absolute inset-0 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl flex items-center justify-center z-10">
        <div class="text-center p-6 max-w-md">
          <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-4">
            <svg class="h-8 w-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2"><%= t('events.index.map_load_failed.title') %></h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4"><%= t('events.index.map_load_failed.description') %></p>
          <div class="flex flex-col sm:flex-row gap-2 justify-center">
            <button data-action="click->map#retry"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              <%= t('common.actions.reload') %>
            </button>
            <button data-action="click->view-switcher#showGrid"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600">
              <%= t('events.views.list') %>
            </button>
          </div>
        </div>
      </div>

      <!-- 地图工具栏 - 优化后的紧凑设计 -->
      <div class="map-toolbar absolute top-4 left-4 right-4 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md rounded-lg shadow-lg border border-gray-200/50 dark:border-gray-600/50 z-10">
        <!-- 单行紧凑布局 -->
        <div class="flex items-center justify-between p-2.5 gap-3">
          <!-- 左侧：事件计数 -->
          <div data-map-target="eventCount" class="flex items-center gap-1.5 bg-primary-50 dark:bg-primary-900/20 px-2.5 py-1.5 rounded-md">
            <svg class="w-3.5 h-3.5 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
            </svg>
            <span class="text-xs font-semibold text-primary-700 dark:text-primary-300">
              <span data-map-target="eventCountText">0</span>
            </span>
          </div>

          <!-- 中间：搜索范围控制 -->
          <div class="hidden sm:flex items-center gap-2 bg-gray-50 dark:bg-gray-700/50 px-2.5 py-1.5 rounded-md">
            <svg class="w-3.5 h-3.5 text-gray-500 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
            <select data-action="change->map#updateRadius"
                    class="text-xs bg-transparent border-none text-gray-700 dark:text-gray-300 focus:ring-0 p-0 pr-6 min-w-[50px]">
              <option value="5"><%= t('events.index.map.distance_km', distance: 5) %></option>
              <option value="10" selected><%= t('events.index.map.distance_km', distance: 10) %></option>
              <option value="25"><%= t('events.index.map.distance_km', distance: 25) %></option>
              <option value="50"><%= t('events.index.map.distance_km', distance: 50) %></option>
              <option value="100"><%= t('events.index.map.distance_km', distance: 100) %></option>
              <option value="200"><%= t('events.index.map.distance_km', distance: 200) %></option>
              <option value="500"><%= t('events.index.map.distance_km', distance: 500) %></option>
              <option value="1000"><%= t('events.index.map.distance_km', distance: 1000) %></option>
              <option value="5000"><%= t('events.index.map.distance_km', distance: 5000) %></option>
            </select>
          </div>

          <!-- 右侧：操作按钮组 -->
          <div class="flex items-center gap-1.5">
            <!-- 定位按钮 -->
            <button data-action="click->map#locateUser"
                    title="<%= h t('events.index.map.locate_me') %>"
                    class="inline-flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-md hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"/>
              </svg>
            </button>

            <!-- 重置按钮 -->
            <button data-action="click->map#resetView"
                    title="<%= h t('events.index.map.reset_view') %>"
                    class="inline-flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 移动端搜索范围控制（在小屏幕下显示） -->
        <div class="sm:hidden border-t border-gray-200 dark:border-gray-600 px-2.5 py-2">
          <div class="flex items-center justify-center gap-2">
            <span class="text-xs text-gray-600 dark:text-gray-400"><%= t('events.index.map.range') %>:</span>
            <select data-action="change->map#updateRadius"
                    class="text-xs border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2 py-1">
              <option value="5"><%= t('events.index.map.distance_km', distance: 5) %></option>
              <option value="10" selected><%= t('events.index.map.distance_km', distance: 10) %></option>
              <option value="25"><%= t('events.index.map.distance_km', distance: 25) %></option>
              <option value="50"><%= t('events.index.map.distance_km', distance: 50) %></option>
              <option value="100"><%= t('events.index.map.distance_km', distance: 100) %></option>
              <option value="200"><%= t('events.index.map.distance_km', distance: 200) %></option>
              <option value="500"><%= t('events.index.map.distance_km', distance: 500) %></option>
              <option value="1000"><%= t('events.index.map.distance_km', distance: 1000) %></option>
              <option value="5000"><%= t('events.index.map.distance_km', distance: 5000) %></option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
