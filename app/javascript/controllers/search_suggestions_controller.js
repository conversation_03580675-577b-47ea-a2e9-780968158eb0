import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "results"]
  static values = {
    url: String,
    debounceDelay: { type: Number, default: 300 }
  }

  connect() {
    this.timeout = null
    this.resultsTarget.addEventListener('click', this.handleResultClick.bind(this))

    // 添加点击外部关闭的监听器
    document.addEventListener('click', this.clickOutside.bind(this))
  }

  disconnect() {
    this.resultsTarget.removeEventListener('click', this.handleResultClick.bind(this))
    document.removeEventListener('click', this.clickOutside.bind(this))

    if (this.timeout) {
      clearTimeout(this.timeout)
    }
  }

  search() {
    const query = this.inputTarget.value.trim()

    if (query.length < 2) {
      this.hideResults()
      return
    }

    // 清除之前的定时器
    if (this.timeout) {
      clearTimeout(this.timeout)
    }

    // 设置新的定时器
    this.timeout = setTimeout(() => {
      this.performSearch(query)
    }, this.debounceDelayValue)
  }

  async performSearch(query) {
    try {
      const response = await fetch(`${this.urlValue}?q=${encodeURIComponent(query)}`, {
        headers: {
          "Accept": "application/json",
          "X-CSRF-Token": document.querySelector('[name="csrf-token"]').content
        }
      })

      if (!response.ok) throw new Error('Network response was not ok')

      const data = await response.json()
      this.displayResults(data.suggestions)
    } catch (error) {
      console.error("Search failed:", error)
      this.hideResults()
    }
  }

  displayResults(suggestions) {
    if (suggestions.length === 0) {
      this.hideResults()
      return
    }

    const html = suggestions.map(suggestion => `
      <div class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-150" data-suggestion="${this.escapeHtml(suggestion)}">
        <div class="text-sm text-gray-900 dark:text-white">${this.highlightMatch(suggestion)}</div>
      </div>
    `).join('')

    this.resultsTarget.innerHTML = html
    this.resultsTarget.classList.remove('hidden')
  }

  highlightMatch(text) {
    const query = this.inputTarget.value.trim()
    const regex = new RegExp(`(${this.escapeRegex(query)})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-700">$1</mark>')
  }

  handleResultClick(event) {
    const suggestionEl = event.target.closest('[data-suggestion]')
    if (suggestionEl) {
      this.inputTarget.value = suggestionEl.dataset.suggestion
      this.hideResults()
      // 触发表单提交
      this.inputTarget.form.submit()
    }
  }

  hideResults() {
    this.resultsTarget.classList.add('hidden')
    this.resultsTarget.innerHTML = ''
  }

  // 点击外部关闭建议框
  clickOutside(event) {
    if (!this.element.contains(event.target)) {
      this.hideResults()
    }
  }

  escapeHtml(str) {
    const div = document.createElement('div')
    div.textContent = str
    return div.innerHTML
  }

  escapeRegex(str) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }
} 