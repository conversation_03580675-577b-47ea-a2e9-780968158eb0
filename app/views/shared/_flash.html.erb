<%# app/views/shared/_flash.html.erb %>
<div id="flash" data-controller="flash" class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-xl">
  <% flash.each do |type, message| %>
    <% if message.present? %>
      <div class="flash-message" data-type="<%= type %>" role="alert">
        <div class="flex items-center p-4 mb-4 <%= flash_class_for(type) %> rounded-lg shadow-lg">
          <%= flash_icon_for(type) %>
          <span class="sr-only"><%= t("shared.flash.types.#{type}") %></span>
          <div class="ms-3 text-sm font-medium"><%= message %></div>
          <button type="button" 
                  class="ms-auto -mx-1.5 -my-1.5 <%= flash_button_class_for(type) %> rounded-lg focus:ring-2 p-1.5 inline-flex items-center justify-center h-8 w-8"
                  data-action="click->flash#dismiss">
            <span class="sr-only"><%= t('shared.flash.close') %></span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
          </button>
        </div>
      </div>
    <% end %>
  <% end %>
</div>
