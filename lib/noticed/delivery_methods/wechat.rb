# frozen_string_literal: true

module Noticed
  module DeliveryMethods
    class Wechat < Noticed::DeliveryMethod
      def deliver
        # 检查用户是否绑定了微信
        wechat_identity = recipient.omni_auth_identities.find_by(provider: "wechat")
        return unless wechat_identity&.uid

        message_type = config[:message_type] || :template

        case message_type
        when :template
          send_template_message(wechat_identity.uid)
        when :custom
          send_custom_message(wechat_identity.uid)
        else
          Rails.logger.error "Unsupported WeChat message type: #{message_type}"
        end
      rescue => e
        Rails.logger.error "WeChat notification delivery failed: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end

      private

      # 发送模板消息
      def send_template_message(openid)
        template_id = config[:template_id]
        return unless template_id

        account = config[:account] || :service
        wechat_api = Wechat.api(account)

        template_data = build_template_data
        message_params = {
          touser: openid,
          template_id: template_id,
          url: config[:url] || notification.url,
          topcolor: config[:topcolor] || "#FF0000",
          data: template_data
        }

        # 如果配置了小程序跳转
        if config[:miniprogram]
          message_params[:miniprogram] = config[:miniprogram]
        end

        result = wechat_api.template_message_send(message_params)

        if result["errcode"] == 0
          Rails.logger.info "WeChat template message sent successfully to #{openid}"
        else
          Rails.logger.error "WeChat template message failed: #{result['errmsg']}"
        end

        result
      end

      # 发送客服消息
      def send_custom_message(openid)
        account = config[:account] || :service
        wechat_api = Wechat.api(account)

        message_content = notification.message
        message_params = {
          touser: openid,
          msgtype: "text",
          text: {
            content: message_content
          }
        }

        result = wechat_api.custom_message_send(message_params)

        if result["errcode"] == 0
          Rails.logger.info "WeChat custom message sent successfully to #{openid}"
        else
          Rails.logger.error "WeChat custom message failed: #{result['errmsg']}"
        end

        result
      end

      # 构建模板消息数据
      def build_template_data
        # 根据通知类型构建不同的模板数据
        case notification.type
        when "EventRegistrationNotifier"
          event = notification.params[:event]
          WechatNotificationTemplateService.build_event_registration_data(event)
        when "EventReminderNotifier"
          event = notification.params[:event]
          reminder_type = notification.params[:reminder_type]
          WechatNotificationTemplateService.build_event_reminder_data(event, reminder_type)
        when "SystemNotifier"
          message = notification.params[:message] || notification.message
          WechatNotificationTemplateService.build_system_notification_data(message)
        else
          # 默认模板数据格式
          {
            first: { value: notification.message, color: "#173177" },
            remark: { value: "点击查看详情", color: "#173177" }
          }
        end
      end
    end
  end
end
