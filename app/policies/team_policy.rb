class TeamPolicy < ApplicationPolicy
  # See https://actionpolicy.evilmartians.io/#/writing_policies
  #
  # def index?
  #   true
  # end
  #
  # def update?
  #   # here we can access our context and record
  #   user.admin? || (user.id == record.user_id)
  # end

  # Scoping
  # See https://actionpolicy.evilmartians.io/#/scoping
  #
  # relation_scope do |relation|
  #   next relation if user.admin?
  #   relation.where(user: user)
  # end

  # Use the method defined in ApplicationPolicy to check if user is present
  pre_check :authenticated?

  relation_scope do |relation|
    return relation if user&.admin?  # Admin可以看到所有团队
    next relation.none unless user

    # 允许查看用户所属的团队
    relation.joins(:team_members).where(team_members: { user_id: user.id })
  end

  def index?
    # Ensure user is authenticated to view the index
    return false unless authenticated?
    true
  end

  def create?
    admin? || authenticated? # 管理员或任何已登录用户都可以创建团队
  end

  def update?
    admin? || team_admin?
  end

  def destroy?
    admin? || (team_admin? && record.active?)
  end

  alias_rule :manage?, to: :update?

  private

  def team_admin?
    record.team_members.exists?(user: user, role: :admin)
  end
end
