# frozen_string_literal: true

module FeaturesHelper
  # 填充知识库表单
  # @param attributes [Hash] 知识库属性
  # @option attributes [String] :name 名称
  # @option attributes [String] :description 描述
  # @option attributes [String] :visibility 可见性 ('private_visibility', 'public_visibility', 'team_visibility')
  # @option attributes [Array<Team>] :teams 关联的团队 (仅当 visibility 为 team_visibility 时)
  # @option attributes [Boolean] :update 是否为更新操作 (默认为 false)
  def fill_knowledge_base_form(attributes)
    fill_in 'Knowledge base name', with: attributes[:name] if attributes[:name]
    fill_in 'Description', with: attributes[:description] if attributes[:description]

    if attributes[:visibility]
      select attributes[:visibility].humanize, from: 'visibility'
      # 等待 Stimulus 控制器处理可见性变化 (如果需要)
      # Capybara 的 find 会自动等待元素出现
      # Removing the expect check for visibility: :hidden as it was causing issues.
      # The core functionality (selecting teams only when visible) is still tested.
      # if attributes[:visibility] == 'team_visibility'
      #   expect(page).to have_selector('[data-knowledge-base-target="teamContainer"]', visible: true)
      # else
      #   # This expect was problematic, removing it for now.
      #   # expect(page).to have_selector('[data-knowledge-base-target="teamContainer"]', visible: :hidden)
      # end
    end

    # 处理团队选择 (仅当 visibility 为 team_visibility 且提供了 teams)
    if attributes[:visibility] == 'team_visibility' && attributes[:teams].present?
      select_teams_for_knowledge_base(attributes[:teams])
    end
  end

  # 在知识库表单中选择团队
  # @param teams [Array<Team>] 要选择的团队对象数组
  def select_teams_for_knowledge_base(teams)
    # 目标 select 元素的 label 是 "team"
    # Capybara's select can handle multiple selections if the HTML allows it
    team_names = teams.map(&:name)
    # Use the explicit ID 'team_selection' from the view _form.html.erb
    # Iterate through team names and select each one individually for multi-select.
    team_names.each do |name|
      select name, from: 'team_selection'
    end
  end

  # 创建知识库的完整流程
  def create_knowledge_base(attributes)
    visit new_knowledge_base_path
    fill_knowledge_base_form(attributes)
    click_button 'Create'
  end

  # 归档/取消归档单个知识库
  # @param knowledge_base [KnowledgeBase] 知识库对象
  # @param action [Symbol] :archive 或 :unarchive
  def toggle_archive_knowledge_base(knowledge_base, action: :archive)
    path = action == :archive ? archive_knowledge_base_path(knowledge_base) : unarchive_knowledge_base_path(knowledge_base)
    button_text = action == :archive ? 'Archive' : 'Unarchive'

    # button_to generates a form, find the button within that form
    form = find("form[action='#{path}'][method='post']") # method is post, but Rails handles PATCH
    button = form.find("button", text: button_text)

    # Check if confirmation is needed (usually not for archive/unarchive)
    # If confirmation is needed based on future implementation:
    # accept_confirm { button.click }
    # Otherwise:
    button.click
  end

  # 批量归档/取消归档知识库
  # @param knowledge_bases [Array<KnowledgeBase>] 知识库对象数组
  # @param action [Symbol] :archive 或 :unarchive
  def bulk_toggle_archive_knowledge_bases(knowledge_bases, action: :archive)
    knowledge_bases.each do |kb|
      check "knowledge_base_ids_#{kb.id}"
    end
    action_text = action == :archive ? 'Archive Selected' : 'Unarchive Selected'
    select action_text, from: 'bulk_action'
    click_button 'Apply' # The button text is 'Apply'
  end

  # 导航到管理知识库团队页面
  def visit_manage_teams_page(knowledge_base)
    visit teams_knowledge_base_path(knowledge_base)
  end

  # 在管理团队页面添加团队
  def add_team_to_knowledge_base(team_name)
    select team_name, from: 'team_id'
    click_button 'Add Team'
  end

  # 在管理团队页面移除团队
  def remove_team_from_knowledge_base(team_name)
    # Find the row/div containing the team name and the remove button
    team_element = find('.space-y-3 > div', text: team_name)
    # Find the button_to form within that element
    remove_button_form = team_element.find("form[action*='/remove_team/']")
    # Store the locator arguments used to find the element
    locator_args = [ '.space-y-3 > div', { text: team_name } ]

    accept_confirm do
      remove_button_form.find('button', text: 'Remove').click
    end
    # Add a wait after clicking remove to allow Turbo Stream/page update
    # Instead of waiting for the selector with text to disappear (which causes ArgumentError),
    # wait for the parent container to NOT have the specific text content.
    within('.space-y-3') do
      expect(page).not_to have_text(team_name)
    end
  end

  # 检查所有批量操作的复选框 (No longer needed as bulk actions check specific boxes)
  # def check_all_bulk_checkboxes
  #   all('input[type="checkbox"][name="knowledge_base_ids[]"]').each(&:check)
  # end
end
