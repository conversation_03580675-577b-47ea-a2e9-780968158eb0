# To deliver this notification:
#
# EventRegistrationNotifier.with(record: @event_registration, event: @event).deliver(@event.organizer)

class EventRegistrationNotifier < ApplicationNotifier
  # ActionCable delivery for real-time notifications
  deliver_by :action_cable do |config|
    config.channel = "NotificationChannel"
    config.stream = -> { recipient }
    config.message = -> {
      {
        id: notification.id,
        type: notification.type,
        message: message,
        url: url,
        created_at: notification.created_at.iso8601
      }
    }
  end

  # Email delivery (conditional based on user preferences)
  deliver_by :email do |config|
    config.mailer = "NotificationMailer"
    config.method = "event_registration"
    config.if = -> { recipient.notification_enabled?(:event_registration, :email) }
  end

  # WeChat delivery (conditional based on user preferences and binding)
  deliver_by :wechat do |config|
    template_config = WechatNotificationTemplateService.get_template_config("event_registration")
    config.template_id = template_config["template_id"]
    config.account = :service
    config.if = -> { recipient.wechat_notification_enabled?(:event_registration) }
    config.url = -> { Rails.application.routes.url_helpers.event_url(params[:event], host: Rails.application.routes.default_url_options[:host] || "localhost") }
    config.data = -> { WechatNotificationTemplateService.build_event_registration_data(params[:event]) }
  end

  # Required params
  required_param :event

  notification_methods do
    def message
      "You have successfully registered for the event '#{params[:event].title}'."
    end

    def url
      Rails.application.routes.url_helpers.event_path(params[:event])
    end

    def title
      "预约成功提醒"
    end

    def event_name
      params[:event].title
    end

    def event_time
      params[:event].start_time.strftime("%Y年%m月%d日")
    end

    def event_location
      params[:event].location&.name || params[:event].address || "线上活动"
    end

    def event_price
      if params[:event].price.present? && params[:event].price > 0
        "#{params[:event].price}元"
      else
        "免费"
      end
    end

    def event_url
      Rails.application.routes.url_helpers.event_url(params[:event], host: Rails.application.routes.default_url_options[:host] || "localhost")
    end
  end
end
