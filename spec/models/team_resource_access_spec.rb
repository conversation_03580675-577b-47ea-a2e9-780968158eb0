require 'rails_helper'

RSpec.describe TeamResourceAccess, type: :model do
  describe 'associations' do
    it { should belong_to(:team_member) }
    it { should belong_to(:resource) }
  end

  describe 'validations' do
    subject { build(:team_resource_access) }

    it { should validate_presence_of(:resource_kind) }
    it { should validate_presence_of(:role) }
    it { should validate_uniqueness_of(:team_member_id)
      .scoped_to([ :resource_type, :resource_id ])
      .with_message("already has access to this resource") }

    it 'validates uniqueness of team_member scoped to resource' do
      team_resource_access = create(:team_resource_access)
      duplicate = build(:team_resource_access,
                       team_member: team_resource_access.team_member,
                       resource: team_resource_access.resource)
      expect(duplicate).not_to be_valid
    end
  end

  describe 'scopes' do
    let!(:viewer_access) { create(:team_resource_access, role: :viewer) }
    let!(:editor_access) { create(:team_resource_access, role: :editor) }
    let!(:maintainer_access) { create(:team_resource_access, role: :maintainer) }

    it 'filters by role' do
      expect(described_class.where(role: :viewer)).to include(viewer_access)
      expect(described_class.where(role: :editor)).to include(editor_access)
      expect(described_class.where(role: :maintainer)).to include(maintainer_access)
    end
  end

  describe 'knowledge base access' do
    let(:team_member) { create(:team_member) }
    let(:knowledge_base) { create(:knowledge_base) }

    context 'when creating access' do
      subject(:create_access) do
        described_class.create(
          team_member: team_member,
          resource: knowledge_base,
          resource_kind: 'knowledge_base',
          role: 'viewer'
        )
      end

      it 'creates access successfully' do
        expect(create_access).to be_valid
        expect(create_access.resource_kind).to eq('knowledge_base')
        expect(create_access.role).to eq('viewer')
      end
    end

    context 'when checking access' do
      let!(:access) do
        create(:team_resource_access,
               team_member: team_member,
               resource: knowledge_base,
               resource_kind: 'knowledge_base',
               role: 'editor')
      end

      it 'can find access through team member' do
        expect(team_member.team_resource_accesses).to include(access)
      end

      it 'can find access through knowledge base' do
        expect(knowledge_base.team_resource_accesses).to include(access)
      end
    end
  end

  describe 'role validation' do
    context 'with knowledge base' do
      let(:tra) { build(:team_resource_access, resource_kind: 'knowledge_base', role: 'student') }

      it 'rejects invalid roles' do
        expect(tra).not_to be_valid
        expect(tra.errors[:role]).to include('does not match resource kind')
      end
    end
  end
end
