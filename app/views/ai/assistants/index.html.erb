<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <!-- 添加面包屑导航 -->
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { items: [[t('ai.assistants.title'), nil]] } %>

        <!-- 页面标题和操作按钮 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white"><%= t('ai.assistants.title') %></h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= t('ai.assistants.subtitle') %></p>
          </div>
          <div class="mt-4 md:mt-0">
            <%= link_to new_ai_assistant_path, class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" do %>
              <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 1v16M1 9h16"/>
              </svg>
              <%= t('ai.assistants.actions.create') %>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- 助手列表 -->
    <% if @assistants.any? %>
      <div class="grid gap-4 md:grid-cols-2 xl:grid-cols-3">
        <% @assistants.each do |assistant| %>
          <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-xs dark:border-gray-700 sm:p-6 dark:bg-gray-800">
            <div class="flex justify-between items-start mb-4">
              <h5 class="text-xl font-bold tracking-tight text-gray-900 dark:text-white">
                <%= assistant.name %>
              </h5>
              <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-sm dark:bg-blue-900 dark:text-blue-300">
                <%= assistant.tool_choice %>
              </span>
            </div>

            <p class="text-xs text-gray-500 dark:text-gray-400 mb-3">
              <span class="flex items-center">
                <svg class="w-3 h-3 me-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0a10 10 0 1 0 10 10A10.011 10.011 0 0 0 10 0Zm3.982 13.982a1 1 0 0 1-1.414 0l-3.274-3.274A1.012 1.012 0 0 1 9 10V6a1 1 0 0 1 2 0v3.586l2.982 2.982a1 1 0 0 1 0 1.414Z"/>
                </svg>
                <%= t('common.time_formats.updated_ago', time: time_ago_in_words(assistant.updated_at)) %>
              </span>
            </p>

            <div class="mb-3">
              <!-- 工具显示 -->
              <div class="flex flex-wrap gap-1 mb-2">
                <% if assistant.tools.any? %>
                  <% assistant.tools.each do |tool_name| %>
                    <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-0.5 rounded-sm dark:bg-gray-700 dark:text-gray-300">
                      <%= tool_name %>
                    </span>
                  <% end %>
                <% else %>
                  <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2 py-0.5 rounded-sm dark:bg-gray-700 dark:text-gray-300">
                    <%= t('ai.assistants.show.info.no_tools') %>
                  </span>
                <% end %>
              </div>

              <!-- 说明文字 -->
              <div class="h-16 overflow-hidden">
                <p class="font-normal text-gray-700 dark:text-gray-400 line-clamp-2">
                  <%= assistant.instructions %>
                </p>
              </div>
            </div>

            <div class="flex justify-end space-x-2">
              <%= link_to edit_ai_assistant_path(assistant), class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-xs px-3 py-1.5 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-hidden dark:focus:ring-primary-800" do %>
                <span class="flex items-center">
                  <svg class="w-3 h-3 me-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 18">
                    <path d="M12.687 14.408a3.01 3.01 0 0 1-1.533.821l-3.566.713a3 3 0 0 1-3.53-3.53l.713-3.566a3.01 3.01 0 0 1 .821-1.533L10.905 2H2.167A2.169 2.169 0 0 0 0 4.167v11.666A2.169 2.169 0 0 0 2.167 18h11.666A2.169 2.169 0 0 0 16 15.833V11.1l-3.313 3.308Zm5.53-9.065.546-.546a2.518 2.518 0 0 0 0-3.56 2.576 2.576 0 0 0-3.559 0l-.547.547 3.56 3.56Z"/>
                    <path d="M13.243 3.2 7.359 9.081a.5.5 0 0 0-.136.256L6.51 12.9a.5.5 0 0 0 .59.59l3.566-.713a.5.5 0 0 0 .255-.136L16.8 6.757 13.243 3.2Z"/>
                  </svg>
                  <%= t('common.actions.edit') %>
                </span>
              <% end %>
              <%= link_to ai_assistant_path(assistant), class: "text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-xs px-3 py-1.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700" do %>
                <span class="flex items-center">
                  <svg class="w-3 h-3 me-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 14">
                    <path d="M10 0C4.612 0 0 5.336 0 7c0 1.742 3.546 7 10 7 6.454 0 10-5.258 10-7 0-1.664-4.612-7-10-7Zm0 10a3 3 0 1 1 0-6 3 3 0 0 1 0 6Z"/>
                  </svg>
                  <%= t('common.actions.view') %>
                </span>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- 优化空状态显示 -->
      <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-xs dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <div class="text-center">
          <svg class="mx-auto mb-4 w-12 h-12 text-gray-400 dark:text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
          </svg>
          <h3 class="mb-2 text-xl font-bold text-gray-900 dark:text-white"><%= t('ai.assistants.no_assistants_found') %></h3>
          <p class="mb-4 text-gray-500 dark:text-gray-400"><%= t('ai.assistants.empty_state') %></p>
          <%= link_to new_ai_assistant_path, class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" do %>
            <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 1v16M1 9h16"/>
            </svg>
            <%= t('ai.assistants.actions.create') %>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
