<%#
# New

This view is the template for the "new resource" page.
It displays a header, and then renders the `_form` partial
to do the heavy lifting.

## Local variables:

- `page`:
  An instance of [Administrate::Page::Form][1].
  Contains helper methods to help display a form,
  and knows which attributes should be displayed in the resource's form.

[1]: http://www.rubydoc.info/gems/administrate/Administrate/Page/Form
%>

<% content_for(:title) do %>
  <%= t(
    "administrate.actions.new_resource",
    name: display_resource_name(page.resource_name, singular: true)
  ) %>
<% end %>

<header class="main-content__header">
  <h1 class="main-content__page-title">
    <%= content_for(:title) %>
  </h1>

  <%= content_for(:header_middle) %>

  <div>
    <%= link_to t("administrate.actions.back"), :back, class: "button" %>
  </div>

  <%= content_for(:header_last) %>
</header>

<%= content_for(:before_main) %>

<% if content_for?(:main) %>
  <%= content_for(:main) %>
<% else %>
  <section class="main-content__body">
    <%= render "form", page: page %>
  </section>
<% end %>

<%= content_for(:after_main) %>
