class NotificationMailer < ApplicationMailer
  def new_comment(notification)
    @notification = notification
    @user = notification.recipient
    @comment = notification.record
    @post = notification.params[:post]
    @commenter = notification.params[:commenter]
    
    mail(
      to: @user.email_address,
      subject: t('mailers.notification_mailer.new_comment.subject', post_title: @post.title)
    )
  end
  
  def event_registration(notification)
    @notification = notification
    @user = notification.recipient
    @event_registration = notification.record
    @event = notification.params[:event]
    
    mail(
      to: @user.email_address,
      subject: t('mailers.notification_mailer.event_registration.subject', event_title: @event.title)
    )
  end
  
  def system_notification(notification)
    @notification = notification
    @user = notification.recipient
    @message = notification.params[:message]
    
    mail(
      to: @user.email_address,
      subject: t('mailers.notification_mailer.system_notification.subject')
    )
  end
end
