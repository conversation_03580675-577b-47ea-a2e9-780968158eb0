module Visibility
  extend ActiveSupport::Concern

  included do
    enum :visibility, {
      private_visibility: 0,   # 私有
      team_visibility: 1,      # 团队
      public_visibility: 2     # 公开
    }

    # 不要委托 owner? 到 policy，避免循环引用
    # delegate :owner?, to: :policy

    def has_team_member?(user)
      team_resource_accesses.joins(:team_member).exists?(team_members: { user_id: user.id })
    end
    private :has_team_member?
  end

  # 提供基础状态查询方法
  # 保持公共接口但转换实现方式
  def public?
    public_visibility?
  end
end
