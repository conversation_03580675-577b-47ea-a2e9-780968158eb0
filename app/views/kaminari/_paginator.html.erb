<%= paginator.render do -%>
  <div class="inline-flex rounded-md shadow-xs">
    <%= first_page_tag unless current_page.first? %>
    <%= prev_page_tag unless current_page.first? %>
    <% each_page do |page| -%>
      <% if page.display_tag? -%>
        <%= page_tag page %>
      <% elsif !page.was_truncated? -%>
        <%= gap_tag %>
      <% end -%>
    <% end -%>
    <%= next_page_tag unless current_page.last? %>
    <%= last_page_tag unless current_page.last? %>
  </div>
<% end -%>
