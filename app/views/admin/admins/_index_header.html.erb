<% content_for(:title) do %>
  <%= display_resource_name(page.resource_name) %>
<% end %>

<header class="main-content__header">
  <h1 class="main-content__page-title" id="page-title">
    <%= content_for(:title) %>
  </h1>

  <%= content_for(:header_middle) %>

  <% if show_search_bar %>
    <%= render(
      "search",
      search_term: search_term,
      resource_name: display_resource_name(page.resource_name)
    ) %>
  <% end %>

  <div>
    <%= link_to(
      t(
        "administrate.actions.new_resource",
        name: display_resource_name(page.resource_name, singular: true).downcase
      ),
      [:new, namespace, page.resource_path.to_sym],
      class: "button",
    ) if accessible_action?(new_resource, :new) %>
  </div>

  <%= content_for(:header_last) %>
</header>
