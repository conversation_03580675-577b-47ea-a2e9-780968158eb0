en:
  wechats:
    async_task_received: "Received your async task request, processing in the background..."
    text_received: "Received your message: %{content}"
    welcome_subscribe: "Welcome! Nice to meet you! Send 'async_task' to test background jobs."
    menu_clicked: "You clicked the menu button! Key is: %{key}"
    image_received: "Image received!"
    voice_received: "Voice received!"
    voice_recognized: "Recognized voice content: %{recognition}"
    fallback_reply: "Sorry, I cannot process your request at the moment."
