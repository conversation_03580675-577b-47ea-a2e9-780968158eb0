<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-(--breakpoint-xl) lg:py-16">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= t('posts.index.title') %></h1>
        <p class="mt-2 text-lg text-gray-500 dark:text-gray-400"><%= t('posts.index.subtitle') %></p>
      </div>
      <%= link_to new_post_path, class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" do %>
        <i class="fas fa-plus mr-2"></i><%= t('posts.index.write_new') %>
      <% end %>
    </div>

    <div class="space-y-4">
      <% @posts.each do |post| %>
        <div class="p-6 bg-white border border-gray-200 rounded-lg shadow-xs dark:bg-gray-800 dark:border-gray-700">
          <div class="flex justify-between items-start">
            <div>
              <h2 class="mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
                <%= post.title %>
              </h2>
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                <span>
                  <% if post.status == 'published' %>
                    <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-xs dark:bg-green-900 dark:text-green-300"><%= t('posts.show.status.published') %></span>
                  <% else %>
                    <span class="bg-yellow-100 text-yellow-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-xs dark:bg-yellow-900 dark:text-yellow-300"><%= t('posts.show.status.draft') %></span>
                  <% end %>
                </span>
                <span class="mx-2">•</span>
                <span><%= post.created_at.strftime("%Y-%m-%d %H:%M") %></span>
              </div>
            </div>
          </div>

          <div class="flex gap-2">
            <%= link_to t('posts.actions.view'), post_path(post),
                class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>

            <%= link_to t('posts.actions.edit'), edit_post_path(post),
                class: "text-primary-500 hover:text-primary-600 border border-primary-500 hover:border-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-primary-600 dark:hover:border-primary-600 dark:focus:ring-primary-800" %>

            <%= link_to t('posts.actions.delete'), post_path(post),
                class: "text-red-500 hover:text-red-600 border border-red-500 hover:border-red-600 focus:ring-4 focus:outline-hidden focus:ring-red-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-red-600 dark:hover:border-red-600 dark:focus:ring-red-800",
                data: {
                  turbo_method: :delete,
                  turbo_confirm: t('posts.actions.delete_confirm')
                } %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</section>
