// 手动注册 slash_commands_controller
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "suggestions"]
  static values = {
    providers: { type: Array, default: [] }, 
    disabledCommands: { type: Array, default: [] } 
  }
  
  connect() {
    this.commandProviders = []
    this.registerDefaultProviders()
    this.registerCustomProviders()
    
    this.setupEventListeners()
    this.isProcessingCommand = false
    this.currentCommandStart = -1
  }
  
  registerDefaultProviders() {
    if (!this.providersValue.includes("none")) {
      if (!this.disabledCommandsValue.includes("prompt")) {
        this.registerProvider("prompt")
      }
    }
  }
  
  registerCustomProviders() {
    this.providersValue.forEach(provider => {
      if (provider !== "none" && provider !== "default" && !this.commandProviders.some(p => p.id === provider)) {
        this.registerProvider(provider)
      }
    })
  }
  
  async registerProvider(providerName) {
    try {
      const module = await import(`command_providers/${providerName}_provider`)
      const provider = new module.default(this)
      
      // 检查是否已经注册了相同ID的提供者
      if (!this.commandProviders.some(p => p.id === provider.id)) {
        this.commandProviders.push(provider)
      }
    } catch (error) {
      console.error(`Failed to load command provider: ${providerName}`, error)
    }
  }
  
  setupEventListeners() {
    this.inputTarget.addEventListener("input", this.handleInput.bind(this))
    this.inputTarget.addEventListener("keydown", this.handleKeyDown.bind(this))
    
    // 监听命令执行结果
    document.addEventListener("command:result", this.handleCommandResult.bind(this))
    
    // 点击其他地方时隐藏建议
    document.addEventListener("click", (event) => {
      if (!this.element.contains(event.target)) {
        this.hideSuggestions()
      }
    })
  }
  
  handleInput(event) {
    const text = this.inputTarget.value
    const cursorPosition = this.inputTarget.selectionStart
    
    // 查找光标前的最后一个斜杠位置
    const lastSlashIndex = text.lastIndexOf("/", cursorPosition - 1)
    
    if (lastSlashIndex !== -1 && (lastSlashIndex === 0 || text[lastSlashIndex - 1] === " ")) {
      const commandText = text.substring(lastSlashIndex + 1, cursorPosition)
      this.currentCommandStart = lastSlashIndex
      this.showCommandSuggestions(commandText)
    } else {
      this.hideSuggestions()
      this.currentCommandStart = -1
    }
  }
  
  handleKeyDown(event) {
    if (!this.suggestionsTarget.classList.contains("hidden")) {
      const items = this.suggestionsTarget.querySelectorAll(".command-item")
      const highlightedIndex = Array.from(items).findIndex(item => item.classList.contains("bg-primary-100"))
      
      switch (event.key) {
        case "ArrowDown":
          event.preventDefault()
          this.highlightItem((highlightedIndex + 1) % items.length)
          break
          
        case "ArrowUp":
          event.preventDefault()
          this.highlightItem(highlightedIndex <= 0 ? items.length - 1 : highlightedIndex - 1)
          break
          
        case "Enter":
          event.preventDefault()
          if (highlightedIndex !== -1) {
            items[highlightedIndex].click()
          }
          break
          
        case "Escape":
          event.preventDefault()
          this.hideSuggestions()
          break
          
        case "Tab":
          if (items.length > 0) {
            event.preventDefault()
            items[highlightedIndex !== -1 ? highlightedIndex : 0].click()
          }
          break
      }
    }
  }
  
  showCommandSuggestions(filter = "") {
    this.suggestionsTarget.innerHTML = ""
    let hasMatchingCommands = false
    
    this.commandProviders.forEach(provider => {
      const commands = provider.getCommands(filter)
      
      if (commands && commands.length > 0) {
        hasMatchingCommands = true
        commands.forEach(command => {
          const item = document.createElement("div")
          item.classList.add("command-item", "p-2", "hover:bg-primary-50", "dark:hover:bg-primary-900", "cursor-pointer", "flex", "items-center")
          
          if (command.icon) {
            const icon = document.createElement("span")
            icon.classList.add("mr-2", "text-gray-500")
            icon.innerHTML = command.icon
            item.appendChild(icon)
          }
          
          const name = document.createElement("span")
          name.classList.add("command-name")
          name.textContent = command.name.startsWith("/") ? command.name : `/${command.name}`
          item.appendChild(name)
          
          if (command.description) {
            const description = document.createElement("span")
            description.classList.add("ml-2", "text-sm", "text-gray-500")
            description.textContent = command.description
            item.appendChild(description)
          }
          
          item.addEventListener("click", () => {
            this.executeCommand(provider, command)
          })
          
          this.suggestionsTarget.appendChild(item)
        })
      }
    })
    
    if (hasMatchingCommands) {
      this.suggestionsTarget.classList.remove("hidden")
      this.highlightItem(0)
    } else {
      this.hideSuggestions()
    }
  }
  
  hideSuggestions() {
    this.suggestionsTarget.classList.add("hidden")
  }
  
  highlightItem(index) {
    const items = this.suggestionsTarget.querySelectorAll(".command-item")
    items.forEach((item, i) => {
      if (i === index) {
        item.classList.add("bg-primary-100", "dark:bg-primary-800")
      } else {
        item.classList.remove("bg-primary-100", "dark:bg-primary-800")
      }
    })
  }
  
  executeCommand(provider, command) {
    this.isProcessingCommand = true
    this.hideSuggestions()
    
    const options = {
      element: this.inputTarget,
      commandStart: this.currentCommandStart
    }
    
    provider.executeCommand(command, options)
  }
  
  handleCommandResult(event) {
    if (!this.isProcessingCommand) return
    
    const { success, replacementText } = event.detail
    
    if (success && replacementText) {
      const text = this.inputTarget.value
      const beforeCommand = text.substring(0, this.currentCommandStart)
      const afterCommand = text.substring(this.inputTarget.selectionStart)
      
      this.inputTarget.value = beforeCommand + replacementText + afterCommand
      
      // 将光标定位在输入框中的新位置
      const newCursorPosition = beforeCommand.length + replacementText.length
      this.inputTarget.setSelectionRange(newCursorPosition, newCursorPosition)
      this.inputTarget.focus()
    }
    
    this.isProcessingCommand = false
  }
}
