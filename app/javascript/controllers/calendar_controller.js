import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["dayModal", "dayModalTitle", "dayEventsList"]
  static values = {
    loadEventsUrl: String,
    allEventsText: String
  }

  connect() {
    console.log("Calendar controller connected")
    this.initializeModal()
  }

  initializeModal() {
    // 延迟初始化，确保 DOM 完全加载和 Flowbite 已初始化
    setTimeout(() => {
      try {
        // 查找模态框元素
        const modalElement = document.getElementById('day-events-modal')
        if (modalElement) {
          console.log("Day events modal element found")

          // 确保 Flowbite 已加载并且 Modal 类可用
          if (typeof window.FlowbiteInstances !== 'undefined') {
            // 尝试获取现有实例
            this.modalInstance = window.FlowbiteInstances.getInstance('Modal', 'day-events-modal')

            if (this.modalInstance) {
              console.log("Day events modal instance found")
            } else {
              console.log("Day events modal instance not found, will use manual methods")
            }
          } else {
            console.warn("FlowbiteInstances not available, using manual modal methods")
          }
        } else {
          console.warn("Day events modal element not found in DOM")
        }
      } catch (error) {
        console.error('Failed to initialize day-events-modal:', error)
      }
    }, 200) // 增加延迟时间确保 Flowbite 完全初始化
  }

  // 显示某天的所有事件
  showDayEvents(event) {
    const button = event.currentTarget
    const date = button.dataset.date
    const dateFormatted = button.dataset.dateFormatted

    // 检查必要的目标元素是否存在
    if (!this.hasDayModalTitleTarget || !this.hasDayEventsListTarget) {
      console.error('Modal targets not found')
      return
    }

    // 更新模态框标题
    this.dayModalTitleTarget.textContent = `${dateFormatted} ${this.allEventsTextValue || '的所有活动'}`

    // 显示加载状态
    this.dayEventsListTarget.innerHTML = `
      <div class="text-center py-4">
        <div role="status" class="flex justify-center items-center">
          <svg aria-hidden="true" class="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-primary-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
            <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5424 39.6781 93.9676 39.0409Z" fill="currentFill"/>
          </svg>
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    `

    // 手动显示模态框（因为 Flowbite 的自动触发可能有问题）
    this.showModal()

    // 如果有 AJAX URL，则加载事件数据
    if (this.loadEventsUrlValue) {
      this.loadDayEvents(date)
    }
  }

  // 手动显示模态框
  showModal() {
    const modalElement = document.getElementById('day-events-modal')
    if (modalElement) {
      console.log("Showing modal manually")

      // 移除 hidden 类并设置正确的属性
      modalElement.classList.remove('hidden')
      modalElement.setAttribute('aria-hidden', 'false')
      modalElement.classList.add('flex')

      // 添加背景遮罩点击关闭功能
      modalElement.addEventListener('click', (e) => {
        if (e.target === modalElement) {
          this.hideModal()
        }
      })

      // 添加 ESC 键关闭功能
      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          this.hideModal()
          document.removeEventListener('keydown', handleEscape)
        }
      }
      document.addEventListener('keydown', handleEscape)

      // 如果有 Flowbite 实例，尝试使用它
      if (this.modalInstance) {
        try {
          this.modalInstance.show()
          console.log("Modal shown using Flowbite instance")
        } catch (error) {
          console.warn('Flowbite modal show failed, using manual method:', error)
        }
      }
    } else {
      console.error("Modal element not found when trying to show")
    }
  }

  // 手动隐藏模态框
  hideModal() {
    const modalElement = document.getElementById('day-events-modal')
    if (modalElement) {
      modalElement.classList.add('hidden')
      modalElement.setAttribute('aria-hidden', 'true')
      modalElement.classList.remove('flex')

      // 如果有 Flowbite 实例，使用它
      if (this.modalInstance) {
        try {
          this.modalInstance.hide()
        } catch (error) {
          console.warn('Flowbite modal hide failed, using manual method:', error)
        }
      }
    }
  }

  // 通过 AJAX 加载某天的事件
  async loadDayEvents(date) {
    try {
      const response = await fetch(`${this.loadEventsUrlValue}?date=${date}`, {
        headers: {
          'Accept': 'text/html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      if (response.ok) {
        const html = await response.text()
        this.dayEventsListTarget.innerHTML = html
      } else {
        throw new Error('Failed to load events')
      }
    } catch (error) {
      console.error('Error loading day events:', error)
      this.dayEventsListTarget.innerHTML = `
        <div class="text-center py-4 text-red-500 dark:text-red-400">
          <p>加载活动时出现错误，请稍后重试。</p>
        </div>
      `
    }
  }

  // 重置模态框状态
  resetModal() {
    if (this.hasDayEventsListTarget) {
      this.dayEventsListTarget.innerHTML = ''
    }
    // 隐藏模态框
    this.hideModal()
  }

  // 清理资源
  disconnect() {
    console.log("Calendar controller disconnected")
    this.modalInstance = null
  }
}
