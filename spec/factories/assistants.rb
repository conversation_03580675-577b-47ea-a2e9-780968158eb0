FactoryBot.define do
  factory :assistant do
    user
    sequence(:name) { |n| "Assistant #{n}" }
    instructions { "You are a helpful assistant that provides clear and concise answers." }
    tool_choice { "auto" }
    tools { [ "Langchain::Tool::Calculator" ] }

    # 定义没有工具的助手
    trait :no_tools do
      tool_choice { "none" }
      tools { [] }
    end

    # 定义强制使用工具的助手
    trait :require_tools do
      tool_choice { "any" }
      tools { [ "Langchain::Tool::Calculator", "Langchain::Tool::Wikipedia" ] }
      instructions { "Always use tools to provide accurate information." }
    end

    # 定义自动选择工具的助手
    trait :auto_tools do
      tool_choice { "auto" }
      tools { [ "Langchain::Tool::Calculator" ] }
      instructions { "Use tools when necessary to provide accurate information." }
    end
  end
end
