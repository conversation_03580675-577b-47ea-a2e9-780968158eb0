<div data-controller="knowledge-base">
  <%= form_with(model: knowledge_base, local: true, id: "knowledge_base_form", class: "space-y-6") do |f| %>
    <% if knowledge_base.errors.any? %>
      <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
        <div class="font-medium"><%= t('knowledge_bases.form.error', count: knowledge_base.errors.count) %></div>
        <ul class="mt-1.5 list-disc list-inside">
          <% knowledge_base.errors.full_messages.each do |msg| %>
            <li><%= msg %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div>
      <%= f.label :name, t('knowledge_bases.form.name'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <%= f.text_field :name,
          class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
          placeholder: t('knowledge_bases.form.name_placeholder'),
          required: true %>
    </div>

    <div>
      <%= f.label :description, t('knowledge_bases.form.description'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <%= f.text_area :description,
          rows: 4,
          class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
          placeholder: t('knowledge_bases.form.description_placeholder'),
          required: true %>
    </div>

    <div>
      <%= f.label :visibility, t('knowledge_bases.form.visibility'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <%= f.select :visibility,
          KnowledgeBase.visibilities.keys.map { |v| [v.humanize, v] },
          { selected: knowledge_base.visibility },
          {
            class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
            data: {
              action: "change->knowledge-base#handleVisibilityChange",
              knowledge_base_target: "visibilitySelect"
            }
          } %>
    </div>

    <div data-knowledge-base-target="teamContainer">
      <%= f.label :team_ids, t('knowledge_bases.form.team'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <%= f.select :team_ids,
          options_from_collection_for_select(current_user.teams, :id, :name),
          { include_blank: true },
          {
            class: "select2 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
            multiple: true,
            id: "team_selection"
          } %>
    </div>

    <div class="flex items-center space-x-4">
      <%= f.submit submit_text, class: "text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>
      <%= link_to t('knowledge_bases.form.cancel'), knowledge_bases_path, class: "text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700" %>
    </div>
  <% end %>
</div>
