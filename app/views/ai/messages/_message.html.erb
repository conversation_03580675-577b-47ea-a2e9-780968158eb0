<%# 根据消息类型渲染相应的模板 %>
<% if message.role == "user" %>
  <%= render "ai/messages/message_user", message: message %>
<% elsif message.role == "assistant" %>
  <%= render "ai/messages/message_assistant", message: message %>
<% elsif message.role == "system" %>
  <%= render "ai/messages/message_system", message: message %>
<% elsif message.role == "tool" && message.tool_call_id.present? %>
  <%# 工具消息不单独显示，将由助手消息内部显示 %>
<% else %>
  <div class="message message-unknown">
    <div class="message-content">
          <p><strong><%= t('ai.common.unknown_type') %>:</strong> <%= message.role %></p>
      <p><%= message.content %></p>
    </div>
  </div>
<% end %>
