FactoryBot.define do
  factory :user do
    sequence(:username) { |n| "user_#{SecureRandom.hex(4)}_#{n}" }
    sequence(:email_address) { |n| "user#{SecureRandom.hex(4)}_#{n}@example.com" }
    password { "password123" }
    phone_number { Faker::PhoneNumber.cell_phone_in_e164 }
    gender { %w[male female].sample }
    location { Faker::Address.city }

    trait :with_unique_email do
      sequence(:email_address) { |n| "testuser#{n}@#{SecureRandom.hex(4)}.example.com" }
    end

    trait :with_omni_auth do
      after(:create) do |user|
        create(:omni_auth_identity, user: user)
      end
    end

    trait :with_google_auth do
      after(:create) do |user|
        create(:omni_auth_identity, :google, user: user)
      end
    end

    trait :with_github_auth do
      after(:create) do |user|
        create(:omni_auth_identity, :github, user: user)
      end
    end

    trait :admin do
      admin { true }
    end
  end
end
