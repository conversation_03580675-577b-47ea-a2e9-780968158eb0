class NotificationPolicy < ApplicationPolicy
  # 前置检查用户身份认证 - 只应用于需要认证的方法
  pre_check :check_authenticated!, only: %i[index? mark_all_read?]

  # 定义作用域 - 管理员可以看到所有通知，普通用户只能看到自己的
  relation_scope do |relation|
    next relation.none unless user
    next relation if user.admin?  # Admin可以看到所有通知
    relation.where(recipient: user)   # 普通用户只能看到自己的通知
  end

  def index?
    authenticated?
  end

  def show?
    admin? || owner?
  end

  def update?
    admin? || owner?
  end

  def mark_all_read?
    authenticated?
  end

  private

  def owner?
    user && record.recipient == user
  end
end
