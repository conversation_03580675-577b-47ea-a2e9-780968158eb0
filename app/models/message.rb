class Message < ApplicationRecord
  # 多态关联，使消息可以属于不同类型的对象（如对话、用户等）
  belongs_to :messageable, polymorphic: true
  # 建立与工具使用记录的一对多关系，并在消息被删除时级联删除工具使用记录
  has_many :tool_usages, dependent: :destroy

  # 消息创建后自动广播此消息
  after_create_commit -> { Ai::MessageService.broadcast(self) }
  # 消息更新后自动广播此消息
  after_update_commit -> { Ai::MessageService.broadcast(self) }

  # 定义消息角色枚举类型
  enum :role, {
    system: "system",    # 系统消息
    tool: "tool",        # 工具消息
    user: "user",        # 用户消息
    assistant: "assistant" # 助手消息
  }, prefix: false, suffix: false, validate: true

  # 验证消息内容存在，除非是包含工具调用的助手消息
  validates :content, presence: true, unless: :assistant_msg_with_tool_calling?

  # 定义按创建时间升序排列的作用域
  scope :ordered, -> { order(created_at: :asc) }

  # 获取用于 Langchain Assistant 的上下文消息
  # 排除:
  # - 系统消息
  # - 处理中的助手消息("I'm thinking...")
  scope :for_langchain_assistant_context, -> {
    where.not(role: :system)
      .where.not("role = ? AND content = ?",
                 roles[:assistant],
                 "I'm thinking...")
  }

  # 获取消息状态
  # 返回：当前状态或根据内容判断的状态（nil则为处理中，否则为已完成）
  def status
    @status || (content.nil? ? :processing : :completed)
  end

  # 设置消息状态
  # @param new_status [String, Symbol] 新状态
  def status=(new_status)
    @status = new_status.to_sym
  end

  # 判断是否为包含工具调用的助手消息
  # 工具调用情况：内容为空但有工具调用数据
  # 返回：是否为包含工具调用的助手消息
  def assistant_msg_with_tool_calling?
    # 工具调用请求 - 空content + tool_calls
    assistant? && tool_calls.present?
  end
end
