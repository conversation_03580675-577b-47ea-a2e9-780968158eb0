require 'rails_helper'
require 'digest'

RSpec.describe SmsService do
  # 使用 E.164 格式的手机号
  let(:china_phone) { '+8613812345678' }
  let(:us_phone) { '+12125551212' }
  let(:verification_code) { '123456' }
  let(:cache_key_china) { "sms_verification:#{china_phone}" }
  let(:cache_key_us) { "sms_verification:#{us_phone}" }
  let(:logger) { instance_double(ActiveSupport::Logger) }

  # 使用完全虚假的凭证进行测试
  let(:fake_sms_user) { 'fake_test_user' }
  let(:fake_sms_key) { 'fake_test_key' }
  let(:fake_zh_template_id) { 'fake_zh_template_123' }
  let(:fake_i18n_template_id) { 'fake_i18n_template_456' }

  let(:sendcloud_api_endpoint) { "#{SmsService::SENDCLOUD_API_URL}/smsapi/send" }

  before do
    # Stub the constants directly within SmsService
    stub_const("SmsService::SENDCLOUD_SMS_USER", fake_sms_user)
    stub_const("SmsService::SENDCLOUD_SMS_KEY", fake_sms_key)

    # Mock template IDs via credentials
    allow(Rails.application.credentials).to receive(:dig).with(:sendcloud, :templates, :zh_id).and_return(fake_zh_template_id)
    allow(Rails.application.credentials).to receive(:dig).with(:sendcloud, :templates, :i18n_id).and_return(fake_i18n_template_id)

    # Mock Rails logger
    allow(Rails).to receive(:logger).and_return(logger)
    allow(logger).to receive(:info)
    allow(logger).to receive(:error)

    # Mock Rails cache
    allow(Rails.cache).to receive(:write)
    allow(Rails.cache).to receive(:read)
    allow(Rails.cache).to receive(:instance_variable_get).with(:@data).and_return({})
  end

  # Helper to build expected request body for WebMock
  def expected_request_body(phone, code)
    params = SmsService.send(:build_sendcloud_params, phone, code)
    signature = SmsService.send(:generate_signature, params)
    URI.encode_www_form(params.merge("signature" => signature))
  end

  describe '.send_sms_code' do
    before do
      allow(SecureRandom).to receive(:random_number).with(100000..999999).and_return(verification_code.to_i)
    end

    context 'for China phone' do
      it 'generates code, caches it, and sends SMS successfully' do
        expected_body = expected_request_body(china_phone, verification_code)
        stub_request(:post, sendcloud_api_endpoint)
          .with(
            body: expected_body,
            headers: { 'Content-Type' => 'application/x-www-form-urlencoded' }
          )
          .to_return(
            status: 200,
            body: '{"result":true, "statusCode":200, "message":"请求成功"}',
            headers: { 'Content-Type' => 'application/json' }
          )

        expect(Rails.cache).to receive(:write).with(cache_key_china, verification_code, expires_in: 5.minutes)
        expect(logger).to receive(:info).with("SMS sent successfully to #{china_phone}")

        code = SmsService.send_sms_code(china_phone)
        expect(code).to eq(verification_code)

        expect(WebMock).to have_requested(:post, sendcloud_api_endpoint)
          .with(body: expected_body)
          .once
      end
    end

    context 'for US phone' do
      it 'generates code, caches it, and sends SMS successfully' do
        expected_body = expected_request_body(us_phone, verification_code)
        stub_request(:post, sendcloud_api_endpoint)
          .with(
            body: expected_body,
            headers: { 'Content-Type' => 'application/x-www-form-urlencoded' }
          )
          .to_return(
            status: 200,
            body: '{"result":true, "statusCode":200, "message":"Request successful"}',
            headers: { 'Content-Type' => 'application/json' }
          )

        expect(Rails.cache).to receive(:write).with(cache_key_us, verification_code, expires_in: 5.minutes)
        expect(logger).to receive(:info).with("SMS sent successfully to #{us_phone}")

        code = SmsService.send_sms_code(us_phone)
        expect(code).to eq(verification_code)

        expect(WebMock).to have_requested(:post, sendcloud_api_endpoint)
          .with(body: expected_body)
          .once
      end
    end

    context 'when API call fails' do
      it 'logs error but still returns the generated code' do
        expected_body = expected_request_body(china_phone, verification_code)
        stub_request(:post, sendcloud_api_endpoint)
          .with(body: expected_body)
          .to_return(
            status: 400,
            body: '{"result":false, "statusCode":400, "message":"Invalid parameters"}',
            headers: { 'Content-Type' => 'application/json' }
          )

        expect(Rails.cache).to receive(:write).with(cache_key_china, verification_code, expires_in: 5.minutes)
        expect(logger).to receive(:error).with("Failed to send SMS to #{china_phone}")
        expect(logger).to receive(:error).with(/Response code: 400/)

        code = SmsService.send_sms_code(china_phone)
        expect(code).to eq(verification_code) # Service should still return code even if API fails

        expect(WebMock).to have_requested(:post, sendcloud_api_endpoint)
          .with(body: expected_body)
          .once
      end
    end
  end

  describe '.generate_sms_code' do
    it 'generates a 6-digit code and caches it for 5 minutes' do
      expect(SecureRandom).to receive(:random_number).with(100000..999999).and_return(verification_code.to_i)
      expect(Rails.cache).to receive(:write).with(cache_key_china, verification_code, expires_in: 5.minutes)

      generated_code = SmsService.generate_sms_code(china_phone)
      expect(generated_code).to eq(verification_code)
    end
  end

  describe '.verify_sms_code' do
    context 'when the code is valid and matches' do
      before { allow(Rails.cache).to receive(:read).with(cache_key_china).and_return(verification_code) }

      it 'returns true' do
        expect(SmsService.verify_sms_code(china_phone, verification_code)).to be true
      end
    end

    context 'when the code is invalid (not in cache)' do
      before { allow(Rails.cache).to receive(:read).with(cache_key_china).and_return(nil) }

      it 'returns false' do
        expect(SmsService.verify_sms_code(china_phone, verification_code)).to be false
      end
    end

    context 'when the code does not match' do
      before { allow(Rails.cache).to receive(:read).with(cache_key_china).and_return('654321') }

      it 'returns false' do
        expect(SmsService.verify_sms_code(china_phone, verification_code)).to be false
      end
    end
  end

  describe '.sms_code_remaining_time' do
    let(:future_time) { Time.current + 3.minutes }
    let(:cache_entry_double) { double('CacheEntry', expires_at: future_time) }

    context 'when the code exists and has not expired' do
      before do
        allow(Rails.cache).to receive(:instance_variable_get).with(:@data).and_return({
          cache_key_china => cache_entry_double
        })
      end

      it 'returns the remaining time in seconds' do
        expect(SmsService.sms_code_remaining_time(china_phone)).to be_within(1).of(180)
      end
    end

    context 'when the code does not exist or has expired' do
      before do
        allow(Rails.cache).to receive(:instance_variable_get).with(:@data).and_return({})
      end

      it 'returns 0' do
        expect(SmsService.sms_code_remaining_time(china_phone)).to eq(0)
      end
    end
  end

  describe '.sms_cache_key (private)' do
    it 'returns the correct cache key format' do
      expect(SmsService.send(:sms_cache_key, china_phone)).to eq(cache_key_china)
      expect(SmsService.send(:sms_cache_key, us_phone)).to eq(cache_key_us)
    end
  end

  describe '.build_sendcloud_params (private)' do
    let(:vars_json) { { "%code%" => verification_code }.to_json }

    it 'builds correct params for China phone number' do
      expected_params = {
        "smsUser" => fake_sms_user, # Expect the stubbed value
        "templateId" => fake_zh_template_id,
        "msgType" => "0",
        "phone" => china_phone[3..-1],
        "vars" => vars_json
      }

      expect(SmsService.send(:build_sendcloud_params, china_phone, verification_code))
        .to eq(expected_params)
    end

    it 'builds correct params for international phone number' do
      expected_params = {
        "smsUser" => fake_sms_user, # Expect the stubbed value
        "templateId" => fake_i18n_template_id,
        "msgType" => "2",
        "phone" => us_phone,
        "vars" => vars_json
      }

      expect(SmsService.send(:build_sendcloud_params, us_phone, verification_code))
        .to eq(expected_params)
    end
  end

  describe '.generate_signature (private)' do
    def calculate_expected_signature(params)
      filtered_params = params.reject { |k, v| k == "signature" || v.nil? }
      sorted_params = filtered_params.sort.to_h
      param_str = sorted_params.map { |k, v| "#{k}=#{v}" }.join("&")
      sign_str = "#{fake_sms_key}&#{param_str}&#{fake_sms_key}"
      Digest::MD5.hexdigest(sign_str)
    end

    it 'generates correct MD5 signature' do
      params = {
        "smsUser" => fake_sms_user,
        "templateId" => fake_zh_template_id,
        "msgType" => "0",
        "phone" => china_phone[3..-1],
        "vars" => { "%code%" => verification_code }.to_json
      }
      expected_signature = calculate_expected_signature(params)

      expect(SmsService.send(:generate_signature, params)).to eq(expected_signature)
    end

    it 'excludes signature key and nil values from signature calculation' do
      params = {
        "smsUser" => fake_sms_user,
        "templateId" => fake_zh_template_id,
        "msgType" => "0",
        "phone" => china_phone[3..-1],
        "vars" => { "%code%" => verification_code }.to_json,
        "signature" => "should_be_excluded",
        "nil_param" => nil
      }
      expected_signature = calculate_expected_signature(params)

      expect(SmsService.send(:generate_signature, params)).to eq(expected_signature)
    end
  end
end
