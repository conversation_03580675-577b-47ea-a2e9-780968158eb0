# Create initial admin user
admin = User.find_or_create_by!(email_address: '<EMAIL>') do |user|
  user.username = 'wenyang_admin'
  user.password_digest = BCrypt::Password.create('password123')
  user.phone_number = '+1234567890'
  user.phone_verified = true
  user.gender = 'other'
  user.location = 'San Francisco'
  user.hobbies = [ 'coding', 'reading' ]
  user.admin = true
end

# Ensure existing admin user has admin privileges
admin.update!(admin: true) unless admin.admin?

# Create a regular user
user = User.find_or_create_by!(email_address: '<EMAIL>') do |user|
  user.username = 'user1'
  user.password_digest = BCrypt::Password.create('password123')
  user.phone_number = '+1987654321'
  user.phone_verified = false
  user.gender = 'other'
  user.location = 'New York'
  user.hobbies = [ 'technology', 'writing' ]
end

# Create teams
engineering_team = Team.find_or_create_by!(name: 'Engineering Team') do |team|
  team.description = 'Engineering department team'
  team.category = 'engineering'
  team.status = 'active'
end

demo_team = Team.find_or_create_by!(name: 'Demo Team') do |team|
  team.description = 'A demo team for testing purposes'
  team.status = 'active'
end

# Add users to teams
admin_member = TeamMember.find_or_create_by!(team: engineering_team, user: admin) do |member|
  member.role = 'admin'
end

user_member = TeamMember.find_or_create_by!(team: engineering_team, user: user) do |member|
  member.role = 'member'
end

demo_admin = TeamMember.find_or_create_by!(team: demo_team, user: admin) do |member|
  member.role = 'admin'
end

# 创建文章
welcome_post = Post.find_or_create_by!(title: 'Welcome Post') do |post|
  post.content = ActionText::Content.new('Welcome to our knowledge base!')
  post.user = admin
  post.status = 'published'
end

guide_post = Post.find_or_create_by!(title: 'Getting Started Guide') do |post|
  post.content = ActionText::Content.new('This is a guide to help you get started.')
  post.user = admin
  post.status = 'published'
end

# 创建知识库和关联
tech_docs = KnowledgeBase.find_or_create_by!(name: 'Technical Documentation') do |kb|
  kb.owner = admin
  kb.visibility = :team_visibility
  kb.description = 'Technical documentation repository'
  kb.metadata = { category: 'technical', version: '1.0' }
end

# 创建团队资源访问权限
TeamResourceAccess.find_or_create_by!(
  team_member: admin_member,
  resource: tech_docs,
  resource_kind: 'knowledge_base'
) do |access|
  access.role = 'maintainer'
end

TeamResourceAccess.find_or_create_by!(
  team_member: user_member,
  resource: tech_docs,
  resource_kind: 'knowledge_base'
) do |access|
  access.role = 'viewer'
end

# 关联文章
[ welcome_post, guide_post ].each do |post|
  KnowledgeBasePost.find_or_create_by!(
    knowledge_base: tech_docs,
    post: post
  )
end

# Create base units
intro_unit = BaseUnit.find_or_create_by!(name: 'Introduction', user: admin) do |unit|
  unit.content_type = 'text/markdown'
  unit.file_size = 1024
  unit.checksum = 'abc123'
  unit.status = 'completed'
  unit.processed_at = Time.current
  unit.metadata = { source: 'manual', category: 'introduction' }
end

advanced_unit = BaseUnit.find_or_create_by!(name: 'Advanced Topics', user: admin) do |unit|
  unit.content_type = 'text/markdown'
  unit.file_size = 2048
  unit.checksum = 'def456'
  unit.status = 'completed'
  unit.processed_at = Time.current
  unit.metadata = { source: 'manual', category: 'advanced' }
end

# 创建 prompt 模板
# 为管理员用户创建 prompt
general_prompt = Prompt.find_or_create_by!(name: '通用助手指令', user: admin) do |prompt|
  prompt.template = '你是一个通用的AI助手, 可以帮助用户解决各种问题。请尽可能提供有用、准确、无害的回答。'
end

coding_prompt = Prompt.find_or_create_by!(name: '编程助手指令', user: admin) do |prompt|
  prompt.template = '你是一个专注于{programming_language}编程的助手。请帮助用户解决以下{programming_language}代码问题，提供最佳实践和优化建议。如果可能，请提供代码示例。'
end

writing_prompt = Prompt.find_or_create_by!(name: '写作助手指令', user: admin) do |prompt|
  prompt.template = '你是一个写作助手，擅长帮助用户改进{content_type}类型的文章。请对以下内容进行分析，并提供关于结构、语法和表达的建设性反馈。

{content}'
end

# 为普通用户创建 prompt
learning_prompt = Prompt.find_or_create_by!(name: '学习辅导指令', user: user) do |prompt|
  prompt.template = '你是一个学习辅导助手，专注于{subject}领域。请帮助用户理解以下{subject}概念，并提供学习方法和资源推荐。请用简单易懂的方式解释问题。

{concept}'
end

creative_prompt = Prompt.find_or_create_by!(name: '创意写作指令', user: user) do |prompt|
  prompt.template = '你是一个创意写作助手。请根据以下提示创作一个{genre}风格的短篇故事。故事应该包含引人入胜的情节、生动的角色和吸引人的对话。

{prompt}'
end

puts "创建了 #{Prompt.count} 个 prompt 模板"

# 创建初始的 Assistant 实例，并关联用户
general_assistant = Assistant.find_or_create_by!(name: '通用助手') do |assistant|
  assistant.instructions = general_prompt.template
  assistant.tool_choice = 'auto'
  assistant.tools = [ "Langchain::Tool::Calculator", "Langchain::Tool::Wikipedia" ]
  assistant.user = admin  # 关联到管理员用户
end

coding_assistant = Assistant.find_or_create_by!(name: '编程助手') do |assistant|
  # 使用 prompt 模板并填充变量
  assistant.instructions = coding_prompt.template.gsub('{programming_language}', 'Ruby')
  assistant.tool_choice = 'auto'
  assistant.tools = [ "Langchain::Tool::Calculator" ]
  assistant.user = admin  # 关联到管理员用户
end

writing_assistant = Assistant.find_or_create_by!(name: '写作助手') do |assistant|
  # 使用 prompt 模板并填充变量
  assistant.instructions = writing_prompt.template.gsub('{content_type}', '学术').gsub('{content}', '这是一篇学术文章的内容')
  assistant.tool_choice = 'none'
  assistant.tools = []
  assistant.user = user  # 关联到普通用户
end

# 为每个用户创建对话
_admin_conversation = Conversation.find_or_create_by!(title: '管理员的对话', user: admin, assistant_id: general_assistant.id)

_user_conversation = Conversation.find_or_create_by!(title: '用户的对话', user: user, assistant_id: general_assistant.id)

puts "创建了 #{Assistant.count} 个助手实例"
puts "创建了 #{Conversation.count} 个对话实例"

# 创建事件相关数据
puts "开始创建事件数据..."

# 创建事件分类
tech_category = EventCategory.find_or_create_by!(name: '技术讲座') do |category|
  category.description = '技术相关的讲座和研讨会'
  category.color = '#3B82F6'
end

workshop_category = EventCategory.find_or_create_by!(name: '实践工作坊') do |category|
  category.description = '动手实践的工作坊活动'
  category.color = '#10B981'
end

networking_category = EventCategory.find_or_create_by!(name: '社交网络') do |category|
  category.description = '社交和网络建设活动'
  category.color = '#F59E0B'
end

training_category = EventCategory.find_or_create_by!(name: '培训课程') do |category|
  category.description = '专业技能培训课程'
  category.color = '#8B5CF6'
end

# 创建事件地点（中国城市的真实坐标）
locations_data = [
  {
    name: '丹阳市文化艺术中心',
    address: '丹阳市新民西路88号',
    city: '丹阳',
    province: '江苏省',
    country: '中国',
    latitude: 32.0103,
    longitude: 119.5686
  },
  {
    name: '杭州国际博览中心',
    address: '杭州市萧山区奔竞大道353号',
    city: '杭州',
    province: '浙江省',
    country: '中国',
    latitude: 30.1629,
    longitude: 120.1633
  },
  {
    name: '厦门国际会议中心',
    address: '厦门市思明区会展路198号',
    city: '厦门',
    province: '福建省',
    country: '中国',
    latitude: 24.4797,
    longitude: 118.1590
  },
  {
    name: '贵阳国际会议展览中心',
    address: '贵阳市观山湖区长岭南路',
    city: '贵阳',
    province: '贵州省',
    country: '中国',
    latitude: 26.6040,
    longitude: 106.6296
  },
  {
    name: '修文县文化活动中心',
    address: '修文县龙场镇翠屏路168号',
    city: '修文',
    province: '贵州省',
    country: '中国',
    latitude: 26.8342,
    longitude: 106.5907
  },
  {
    name: '大理古城文化广场',
    address: '大理市大理古城复兴路',
    city: '大理',
    province: '云南省',
    country: '中国',
    latitude: 25.6916,
    longitude: 100.1581
  },
  {
    name: '沙溪古镇会议中心',
    address: '大理州剑川县沙溪镇寺登街',
    city: '沙溪',
    province: '云南省',
    country: '中国',
    latitude: 26.1847,
    longitude: 99.8847
  },
  {
    name: '杭州西湖文化广场',
    address: '杭州市下城区中山北路581号',
    city: '杭州',
    province: '浙江省',
    country: '中国',
    latitude: 30.2741,
    longitude: 120.1551
  }
]

event_locations = locations_data.map do |location_data|
  EventLocation.find_or_create_by!(
    name: location_data[:name],
    address: location_data[:address]
  ) do |location|
    location.city = location_data[:city]
    location.province = location_data[:province]
    location.country = location_data[:country]
    location.latitude = location_data[:latitude]
    location.longitude = location_data[:longitude]
  end
end

# 创建示例事件
events_data = [
  {
    title: 'Ruby on Rails 8.0 新特性深度解析',
    description: '深入了解 Rails 8.0 的最新特性，包括 Solid Cache、Solid Queue 等新组件的使用方法和最佳实践。',
    start_time: 2.weeks.from_now.change(hour: 14, min: 0),
    end_time: 2.weeks.from_now.change(hour: 17, min: 0),
    category: tech_category,
    location: event_locations[0], # 多伦多大学
    price: 0,
    max_participants: 50,
    difficulty_level: 'intermediate',
    what_to_bring: '请携带笔记本电脑和充电器',
    what_included: '讲义资料、茶点'
  },
  {
    title: 'React + TypeScript 实战工作坊',
    description: '通过实际项目学习 React 和 TypeScript 的结合使用，构建现代化的前端应用。',
    start_time: 1.week.from_now.change(hour: 10, min: 0),
    end_time: 1.week.from_now.change(hour: 16, min: 0),
    category: workshop_category,
    location: event_locations[1], # 杭州国际博览中心
    price: 99.99,
    max_participants: 30,
    difficulty_level: 'intermediate',
    what_to_bring: '笔记本电脑、鼠标',
    what_included: '项目代码、学习资料、下午茶'
  },
  {
    title: '技术人员职业发展交流会',
    description: '与资深技术专家面对面交流，分享职业发展经验，建立专业人脉网络。',
    start_time: 3.days.from_now.change(hour: 18, min: 30),
    end_time: 3.days.from_now.change(hour: 21, min: 0),
    category: networking_category,
    location: event_locations[2], # 厦门国际会议中心
    price: 25.0,
    max_participants: 80,
    difficulty_level: 'beginner',
    what_to_bring: '名片、简历',
    what_included: '晚餐、饮品、交流礼品'
  },
  {
    title: 'Docker 容器化技术培训',
    description: '从零开始学习 Docker 容器技术，包括镜像构建、容器编排和生产环境部署。',
    start_time: 5.days.from_now.change(hour: 9, min: 0),
    end_time: 5.days.from_now.change(hour: 17, min: 0),
    category: training_category,
    location: event_locations[3], # 贵阳国际会议展览中心
    price: 199.99,
    max_participants: 25,
    difficulty_level: 'beginner',
    what_to_bring: '笔记本电脑、Docker账号',
    what_included: '培训证书、学习资料、午餐'
  },
  {
    title: 'AI 与机器学习入门讲座',
    description: '探索人工智能和机器学习的基础概念，了解当前行业趋势和应用场景。',
    start_time: 10.days.from_now.change(hour: 13, min: 0),
    end_time: 10.days.from_now.change(hour: 16, min: 0),
    category: tech_category,
    location: event_locations[4], # 修文县文化活动中心
    price: 0,
    max_participants: 100,
    difficulty_level: 'beginner',
    what_to_bring: '笔记本（可选）',
    what_included: '讲座资料、茶点'
  },
  {
    title: 'Vue.js 3 组合式API实战',
    description: '深入学习 Vue.js 3 的组合式 API，构建响应式和可维护的前端应用。',
    start_time: 1.week.from_now.change(hour: 14, min: 0),
    end_time: 1.week.from_now.change(hour: 18, min: 0),
    category: workshop_category,
    location: event_locations[5], # 大理古城文化广场
    price: 79.99,
    max_participants: 35,
    difficulty_level: 'intermediate',
    what_to_bring: '笔记本电脑、开发环境',
    what_included: '代码示例、学习指南、茶点'
  },
  {
    title: '云计算架构设计研讨会',
    description: '学习现代云计算架构设计原则，包括微服务、无服务器架构和云原生应用开发。',
    start_time: 2.weeks.from_now.change(hour: 10, min: 0),
    end_time: 2.weeks.from_now.change(hour: 15, min: 0),
    category: tech_category,
    location: event_locations[6], # 沙溪古镇会议中心
    price: 149.99,
    max_participants: 40,
    difficulty_level: 'advanced',
    what_to_bring: '笔记本电脑、云服务账号',
    what_included: '架构图纸、案例研究、午餐'
  },
  {
    title: 'Python 数据科学工作坊',
    description: '使用 Python 进行数据分析和可视化，学习 Pandas、NumPy 和 Matplotlib 等核心库。',
    start_time: 4.days.from_now.change(hour: 9, min: 30),
    end_time: 4.days.from_now.change(hour: 16, min: 30),
    category: workshop_category,
    location: event_locations[7], # 杭州西湖文化广场
    price: 129.99,
    max_participants: 20,
    difficulty_level: 'intermediate',
    what_to_bring: '笔记本电脑、Python环境',
    what_included: '数据集、代码示例、午餐'
  },
  # 添加一些过去的事件用于测试
  {
    title: 'JavaScript ES2023 新特性回顾',
    description: '回顾 JavaScript ES2023 的新特性和改进，包括新的数组方法和语法糖。',
    start_time: 1.week.ago.change(hour: 14, min: 0),
    end_time: 1.week.ago.change(hour: 17, min: 0),
    category: tech_category,
    location: event_locations[0], # 丹阳市文化艺术中心
    price: 0,
    max_participants: 60,
    difficulty_level: 'intermediate',
    what_to_bring: '笔记本电脑',
    what_included: '代码示例、学习资料'
  },
  {
    title: 'Git 版本控制最佳实践',
    description: '学习 Git 的高级用法和团队协作最佳实践，提高代码管理效率。',
    start_time: 3.days.ago.change(hour: 10, min: 0),
    end_time: 3.days.ago.change(hour: 13, min: 0),
    category: training_category,
    location: event_locations[2], # 厦门国际会议中心
    price: 49.99,
    max_participants: 40,
    difficulty_level: 'intermediate',
    what_to_bring: '笔记本电脑、Git账号',
    what_included: '实践指南、团队协作模板'
  }
]

# 创建事件
events_data.each_with_index do |event_data, index|
  event = Event.find_or_create_by!(title: event_data[:title]) do |e|
    e.description = event_data[:description]
    e.start_time = event_data[:start_time]
    e.end_time = event_data[:end_time]
    e.event_category = event_data[:category]
    e.event_location = event_data[:location]
    e.price = event_data[:price]
    e.max_participants = event_data[:max_participants]
    e.difficulty_level = event_data[:difficulty_level]
    e.what_to_bring = event_data[:what_to_bring]
    e.what_included = event_data[:what_included]
    e.user = admin
    e.status = 'published'
    e.visibility = 'public_visibility'
  end

  # 为一些事件添加讲师
  if index.even?
    EventInstructor.find_or_create_by!(event: event, user: admin) do |instructor|
      instructor.role = 'instructor'
      instructor.bio = '资深技术专家，拥有10年以上行业经验'
    end
  end

  # 为过去的事件添加一些报名记录（跳过验证）
  if event.start_time < Time.current
    # 为过去的事件创建已参加的注册记录，跳过验证
    [ admin, user ].each do |participant|
      registration = EventRegistration.find_or_initialize_by(event: event, user: participant)
      unless registration.persisted?
        registration.status = 'attended'
        registration.registered_at = event.start_time - rand(7..14).days
        registration.notes = '已参加完成'
        registration.save!(validate: false) # 跳过验证
      end
    end
  end
end

puts "创建了 #{EventCategory.count} 个事件分类"
puts "创建了 #{EventLocation.count} 个事件地点"
puts "创建了 #{Event.count} 个事件"
puts "创建了 #{EventInstructor.count} 个事件讲师记录"
puts "创建了 #{EventRegistration.count} 个事件报名记录"

# 创建额外的测试数据来验证地图聚类功能
puts "创建额外的测试事件用于地图聚类验证..."

# 中国主要城市的坐标数据（用于聚类测试）
clustering_cities = [
  { name: "北京", lat: 39.9042, lng: 116.4074 },
  { name: "上海", lat: 31.2304, lng: 121.4737 },
  { name: "广州", lat: 23.1291, lng: 113.2644 },
  { name: "深圳", lat: 22.5431, lng: 114.0579 },
  { name: "南京", lat: 32.0603, lng: 118.7969 },
  { name: "武汉", lat: 30.5928, lng: 114.3055 },
  { name: "成都", lat: 30.5728, lng: 104.0668 },
  { name: "西安", lat: 34.3416, lng: 108.9398 },
  { name: "重庆", lat: 29.5630, lng: 106.5516 },
  { name: "天津", lat: 39.3434, lng: 117.3616 },
  { name: "苏州", lat: 31.2989, lng: 120.5853 },
  { name: "青岛", lat: 36.0986, lng: 120.3719 },
  { name: "大连", lat: 38.9140, lng: 121.6147 },
  { name: "宁波", lat: 29.8683, lng: 121.5440 },
  { name: "无锡", lat: 31.4912, lng: 120.3124 },
  { name: "长沙", lat: 28.2282, lng: 112.9388 },
  { name: "郑州", lat: 34.7466, lng: 113.6254 },
  { name: "济南", lat: 36.6512, lng: 117.1201 },
  { name: "福州", lat: 26.0745, lng: 119.2965 },
  { name: "合肥", lat: 31.8206, lng: 117.2272 }
]

# 为每个城市创建2-3个事件，确保总数超过20个以触发聚类
clustering_cities.each_with_index do |city, index|
  # 为每个城市创建2个事件
  2.times do |i|
    event_title = "#{city[:name]}#{[ '技术交流会', '创业分享会', '产品发布会', '行业峰会' ][i % 4]}"

    # 添加一些随机偏移，让同城市的事件稍微分散
    lat_offset = (rand - 0.5) * 0.02  # 约2公里范围内的随机偏移
    lng_offset = (rand - 0.5) * 0.02

    # 为同城市事件创建稍微不同的位置
    event_location = EventLocation.find_or_create_by(
      name: "#{city[:name]}#{[ '国际会展中心', '科技园', '创业园区', '商务中心' ][i % 4]}",
      city: city[:name]
    ) do |loc|
      loc.address = "#{city[:name]}市#{[ '高新区', '商务区', '开发区', '中心区' ][i % 4]}"
      loc.country = "中国"
      loc.latitude = city[:lat] + lat_offset
      loc.longitude = city[:lng] + lng_offset
    end

    start_time = rand(1..30).days.from_now + rand(8..18).hours
    end_time = start_time + rand(2..4).hours

    Event.find_or_create_by(title: event_title) do |event|
      event.description = "这是一个在#{city[:name]}举办的#{[ '技术', '创业', '产品', '行业' ][i % 4]}主题活动，欢迎大家参加。"
      event.start_time = start_time
      event.end_time = end_time
      event.user = admin
      event.event_category = [ tech_category, workshop_category, networking_category, training_category ][rand(4)]
      event.event_location = event_location
      event.max_participants = [ 50, 100, 200, 300 ][rand(4)]
      event.price = [ 0, 50, 100, 200 ][rand(4)]
      event.member_price = [ 0, 30, 80, 150 ][rand(4)]
      event.status = 'published'
      event.visibility = 'public_visibility'
      event.difficulty_level = [ 'beginner', 'intermediate', 'advanced' ][rand(3)]
      event.is_online = false
    end
  end

  print "."
end

puts "\n✅ 创建了额外的聚类测试事件"
puts "总事件数量: #{Event.count}"
puts "带地理位置的事件数量: #{Event.joins(:event_location).where.not(event_locations: { latitude: nil, longitude: nil }).count}"

# 创建通知测试数据
puts "\n开始创建通知测试数据..."

# 创建另一个测试用户来作为通知的发送者
notifier_user = User.find_or_create_by!(email_address: '<EMAIL>') do |user|
  user.username = 'notifier_user'
  user.password_digest = BCrypt::Password.create('password123')
  user.phone_number = '+1555666777'
  user.phone_verified = false
  user.gender = 'other'
  user.location = 'San Francisco'
  user.hobbies = [ 'technology', 'notifications' ]
end

# 创建一些示例帖子作为通知的上下文
test_posts = []
3.times do |i|
  post = Post.find_or_create_by!(title: "测试帖子 #{i + 1}") do |p|
    p.content = ActionText::Content.new("这是第 #{i + 1} 个测试帖子的内容，用于测试通知功能。")
    p.user = notifier_user
    p.status = 'published'
  end
  test_posts << post
end

# 创建通知测试数据
notifications_data = [
  {
    notifier: SystemNotifier,
    params: { message: "系统维护通知：系统将在今晚进行维护，请及时保存您的工作。" },
    recipients: [ admin, user ],
    created_at: 3.days.ago,
    read_at: nil
  },
  {
    notifier: EventReminderNotifier,
    params: { event: Event.published.first, reminder_type: 'day_before' },
    recipients: [ admin ],
    created_at: 2.days.ago,
    read_at: 1.day.ago
  },
  {
    notifier: EventReminderNotifier,
    params: { event: Event.published.second, reminder_type: 'hour_before' },
    recipients: [ user ],
    created_at: 1.day.ago,
    read_at: nil
  },
  {
    notifier: SystemNotifier,
    params: { message: "欢迎使用我们的平台！请查看用户指南了解更多功能。" },
    recipients: [ user ],
    created_at: 5.hours.ago,
    read_at: nil
  },
  {
    notifier: SystemNotifier,
    params: { message: "您有新的功能更新可用，请查看更新日志。" },
    recipients: [ admin, user ],
    created_at: 2.hours.ago,
    read_at: nil
  },
  {
    notifier: SystemNotifier,
    params: { message: "数据库备份已完成，所有数据已安全存储。" },
    recipients: [ admin ],
    created_at: 1.week.ago,
    read_at: 6.days.ago
  },
  {
    notifier: SystemNotifier,
    params: { message: "新用户注册量本月增长了20%，欢迎查看详细报告。" },
    recipients: [ admin ],
    created_at: 4.days.ago,
    read_at: 3.days.ago
  },
  {
    notifier: SystemNotifier,
    params: { message: "您的账户安全设置已更新，如非本人操作请联系客服。" },
    recipients: [ user ],
    created_at: 30.minutes.ago,
    read_at: nil
  },
  {
    notifier: SystemNotifier,
    params: { message: "平台将于下周发布新版本，敬请期待！" },
    recipients: [ admin, user ],
    created_at: 6.hours.ago,
    read_at: nil
  },
  {
    notifier: SystemNotifier,
    params: { message: "您有一个新的私信，请及时查看。" },
    recipients: [ user ],
    created_at: 12.hours.ago,
    read_at: 10.hours.ago
  }
]

# 发送通知
notifications_data.each do |notification_data|
  begin
    # 为每个收件人创建通知
    notification_data[:recipients].each do |recipient|
      if notification_data[:notifier] == EventReminderNotifier
        # 发送事件提醒通知
        notification = notification_data[:notifier].with(
          event: notification_data[:params][:event],
          reminder_type: notification_data[:params][:reminder_type]
        ).deliver(recipient)
      else
        # 发送系统通知
        notification = notification_data[:notifier].with(
          message: notification_data[:params][:message]
        ).deliver(recipient)
      end

      # 更新通知的时间戳
      if notification && notification_data[:created_at]
        # 使用 update_columns 跳过验证和回调
        recipient.notifications.last.update_columns(
          created_at: notification_data[:created_at],
          updated_at: notification_data[:created_at]
        )

        # 如果需要设置为已读
        if notification_data[:read_at]
          recipient.notifications.last.update_columns(
            read_at: notification_data[:read_at]
          )
        end
      end
    end

    puts "✅ 创建了 #{notification_data[:notifier]} 通知"
  rescue => e
    puts "❌ 创建通知时出错: #{e.message}"
  end
end

# 创建一些较旧的通知（已读状态）
begin
  older_notification = SystemNotifier.with(
    message: "这是一条较旧的通知，应该显示为已读状态。"
  ).deliver(admin)

  # 将通知标记为已读
  if admin.notifications.any?
    admin.notifications.last.update(read_at: 1.hour.ago)
  end

  puts "✅ 创建了已读通知示例"
rescue => e
  puts "❌ 创建已读通知时出错: #{e.message}"
end

puts "\n📧 通知测试数据创建完成！"
puts "Admin 用户通知数量: #{admin.notifications.count}"
puts "User 用户通知数量: #{user.notifications.count}"
puts "未读通知数量 (Admin): #{admin.unread_notifications_count}"
puts "未读通知数量 (User): #{user.unread_notifications_count}"
