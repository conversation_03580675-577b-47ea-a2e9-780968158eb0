module Ai
  class AssistantsController < ApplicationController
    before_action :set_assistant, only: [ :show, :edit, :update, :destroy ]

    def index
      @assistants = authorized(current_user.assistants)
    end

    def new
      @assistant = current_user.assistants.new
      @assistant.tool_choice = "auto" # 设置默认值
      authorize! @assistant
      @tool_infos = LangchainToolRegistry.new.available_tool_infos
    end

    def create
      @assistant = current_user.assistants.new(assistant_params)
      authorize! @assistant

      # 处理工具选择为none的情况
      @assistant.tools = [] if @assistant.tool_choice == "none"

      if @assistant.save
        redirect_to [ :ai, @assistant ], notice: "Assistant created successfully."
      else
        @tool_infos = LangchainToolRegistry.new.available_tool_infos
        render :new
      end
    end

    def show
      authorize! @assistant
    end

    def edit
      authorize! @assistant
      @tool_infos = LangchainToolRegistry.new.available_tool_infos
    end

    def update
      authorize! @assistant
      assistant_attrs = assistant_params

      # 处理工具选择为none或tools参数为nil的情况
      if assistant_attrs[:tool_choice] == "none" || assistant_attrs[:tools].nil?
        assistant_attrs[:tools] = []
      end

      if @assistant.update(assistant_attrs)
        redirect_to [ :ai, @assistant ], notice: "Assistant updated successfully."
      else
        @tool_infos = LangchainToolRegistry.new.available_tool_infos
        render :edit
      end
    end

    def destroy
      authorize! @assistant

      @assistant.destroy!
      redirect_to ai_assistants_path, notice: "Assistant deleted successfully."
    end

    private

    def set_assistant
      @assistant = current_user.assistants.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      redirect_to ai_assistants_path, alert: "Assistant not found."
    end

    def assistant_params
      params.require(:assistant).permit(:name, :instructions, :tool_choice, tools: [])
    end
  end
end
