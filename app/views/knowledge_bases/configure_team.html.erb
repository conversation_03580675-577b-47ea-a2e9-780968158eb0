<section class="bg-white dark:bg-gray-900">
  <div class="py-8 px-4 mx-auto max-w-2xl lg:py-16">
    <h2 class="mb-4 text-xl font-bold text-gray-900 dark:text-white"><%= t('knowledge_bases.configure_team.title') %> "<%= @knowledge_base.name %>"</h2>
    <p class="mb-4 text-sm text-gray-500 dark:text-gray-400"><%= t('knowledge_bases.configure_team.subtitle') %></p>

    <%= form_with(model: @knowledge_base, url: update_team_knowledge_base_path(@knowledge_base), local: true, method: :patch) do |f| %>
      <div class="mb-4">
        <%= f.label :team_ids, t('knowledge_bases.configure_team.select_teams'), class: "block text-sm font-medium text-gray-900 dark:text-white" %>
        <%= f.collection_select :team_ids, @teams, :id, :name, { include_hidden: false, prompt: t('knowledge_bases.configure_team.select_prompt') }, { multiple: true, class: "mt-2 block w-full" } %>
      </div>
      
      <!-- 如需要，还可以添加每个团队对应的权限选择，示例略 -->

      <div class="flex items-center justify-end space-x-4 mt-6">
        <%= link_to t('knowledge_bases.configure_team.skip'), @knowledge_base, class: "text-gray-600 hover:text-gray-800" %>
        <%= f.submit t('knowledge_bases.configure_team.update'), class: "text-white bg-primary-700 hover:bg-primary-800 font-medium rounded-lg text-sm px-5 py-2.5" %>
      </div>
    <% end %>
  </div>
</section>
