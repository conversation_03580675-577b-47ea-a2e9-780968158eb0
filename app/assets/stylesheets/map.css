/* Map specific styles */

/* Custom marker styles */
.custom-event-marker {
  background: transparent !important;
  border: none !important;
}

.user-location-marker {
  background: transparent !important;
  border: none !important;
}

/* Event popup styles */
.event-popup .leaflet-popup-content-wrapper {
  border-radius: 12px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.event-popup .leaflet-popup-content {
  margin: 0;
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
}

.event-popup .leaflet-popup-tip {
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-top: none;
  border-right: none;
}

/* Line clamp utility for popup titles */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Map container improvements */
.leaflet-container {
  font-family: inherit;
}

/* Custom zoom control positioning */
.leaflet-top.leaflet-right {
  top: 60px;
  right: 10px;
}

/* Map toolbar z-index fix */
.map-toolbar {
  z-index: 1000 !important;
}

/* Ensure map toolbar is above leaflet controls */
.leaflet-container .map-toolbar {
  z-index: 1000 !important;
}

/* Attribution control styling */
.leaflet-control-attribution {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 6px;
  font-size: 11px;
}

/* Dark mode support for map controls */
@media (prefers-color-scheme: dark) {
  .leaflet-control-zoom a {
    background-color: #374151;
    color: #f3f4f6;
    border-color: #4b5563;
  }

  .leaflet-control-zoom a:hover {
    background-color: #4b5563;
    color: #ffffff;
  }

  .leaflet-control-attribution {
    background: rgba(55, 65, 81, 0.8);
    color: #d1d5db;
  }

  .leaflet-control-attribution a {
    color: #93c5fd;
  }
}

/* Loading animation improvements */
@keyframes mapPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-map-pulse {
  animation: mapPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive map toolbar */
@media (max-width: 640px) {
  .map-toolbar {
    flex-direction: column;
    gap: 0.75rem;
  }

  .map-toolbar .flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .map-toolbar button {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

/* Event count badge */
.event-count-badge {
  transition: all 0.2s ease-in-out;
}

.event-count-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Map error state improvements */
.map-error-container {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Backdrop blur fallback */
@supports not (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    background-color: rgba(255, 255, 255, 0.95);
  }

  @media (prefers-color-scheme: dark) {
    .backdrop-blur-sm {
      background-color: rgba(31, 41, 55, 0.95);
    }
  }
}

/* Custom scrollbar for map popups */
.event-popup .leaflet-popup-content::-webkit-scrollbar {
  width: 4px;
}

.event-popup .leaflet-popup-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.event-popup .leaflet-popup-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

.event-popup .leaflet-popup-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Map focus styles for accessibility */
.leaflet-container:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions for map elements */
.leaflet-marker-icon,
.leaflet-marker-shadow {
  transition: filter 0.2s ease-in-out, z-index 0.2s ease-in-out;
  cursor: pointer;
}

.leaflet-marker-icon:hover {
  z-index: 1000;
  filter: brightness(1.15) contrast(1.05) drop-shadow(0 6px 12px rgba(0, 0, 0, 0.25));
}

/* Map loading skeleton */
.map-loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@media (prefers-color-scheme: dark) {
  .map-loading-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
}

/* Custom marker cluster styles to match design system */
.marker-cluster-small {
  background-color: rgba(59, 130, 246, 0.15) !important; /* primary-500 with opacity */
  border: 2px solid rgba(59, 130, 246, 0.3) !important;
}

.marker-cluster-small div {
  background-color: rgba(59, 130, 246, 0.8) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.marker-cluster-medium {
  background-color: rgba(245, 158, 11, 0.15) !important; /* amber-500 with opacity */
  border: 2px solid rgba(245, 158, 11, 0.3) !important;
}

.marker-cluster-medium div {
  background-color: rgba(245, 158, 11, 0.8) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.marker-cluster-large {
  background-color: rgba(239, 68, 68, 0.15) !important; /* red-500 with opacity */
  border: 2px solid rgba(239, 68, 68, 0.3) !important;
}

.marker-cluster-large div {
  background-color: rgba(239, 68, 68, 0.8) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Enhanced cluster styling */
.marker-cluster {
  border-radius: 50% !important;
  transition: filter 0.2s ease-in-out, box-shadow 0.2s ease-in-out !important;
  cursor: pointer !important;
  /* 确保不干扰Leaflet的定位系统 */
  transform-origin: center center !important;
}

.marker-cluster:hover {
  z-index: 1000 !important;
  filter: brightness(1.15) contrast(1.05) !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25) !important;
}

/* 为聚类内部div添加悬停效果 */
.marker-cluster:hover div {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.marker-cluster div {
  width: 32px !important;
  height: 32px !important;
  margin-left: 4px !important;
  margin-top: 4px !important;
  line-height: 32px !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
}

.marker-cluster span {
  line-height: 32px !important;
  font-weight: 600 !important;
}

/* Dark mode support for clusters */
@media (prefers-color-scheme: dark) {
  .marker-cluster-small {
    background-color: rgba(59, 130, 246, 0.2) !important;
    border-color: rgba(59, 130, 246, 0.4) !important;
  }

  .marker-cluster-medium {
    background-color: rgba(245, 158, 11, 0.2) !important;
    border-color: rgba(245, 158, 11, 0.4) !important;
  }

  .marker-cluster-large {
    background-color: rgba(239, 68, 68, 0.2) !important;
    border-color: rgba(239, 68, 68, 0.4) !important;
  }
}

/* Cluster animation improvements */
.leaflet-cluster-anim .leaflet-marker-icon {
  transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.25s ease-in-out !important;
}

/* Spider leg styling for cluster expansion */
.leaflet-cluster-spider-leg {
  stroke: rgba(59, 130, 246, 0.6) !important;
  stroke-width: 2 !important;
  stroke-dasharray: 5, 5 !important;
}

/* 防止聚类图标位置跳动的额外规则 */
.marker-cluster {
  /* 防止布局偏移 */
  will-change: filter, box-shadow !important;
  /* 不要覆盖Leaflet的定位 - 移除position设置 */
}

/* 确保聚类动画不会影响定位 */
.leaflet-cluster-anim .marker-cluster {
  /* 只允许opacity和filter变化，不允许transform */
  transition: opacity 0.25s ease-in-out, filter 0.2s ease-in-out, box-shadow 0.2s ease-in-out !important;
}

/* 确保聚类图标在所有缩放级别都正确定位 */
.leaflet-marker-icon.marker-cluster {
  /* 让Leaflet完全控制定位 */
  margin: 0 !important;
  padding: 0 !important;
  /* 确保不会有额外的偏移 */
  top: 0 !important;
  left: 0 !important;
}

/* 修复聚类图标的内部div定位 */
.marker-cluster div {
  /* 确保内部div居中 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  /* 移除可能的边距 */
  margin: 0 !important;
  padding: 0 !important;
}

/* 自定义事件标记的稳定性 */
.custom-event-marker {
  /* 防止定位问题 */
  will-change: filter !important;
  /* 不要覆盖Leaflet的定位 - 移除position设置 */
}
