# English translations for Authentication & User Management
en:
  auth:
    sign_in: "Sign In"
    sign_up: "Sign Up"
    sign_out: "Sign Out"
    sessions:
      title: "Sign In"
      email: "Email"
      password: "Password"
      remember_me: "Remember me"
      forgot_password: "Forgot password?"
      new_account: "Don't have an account yet?"
    registrations:
      title: "Create your account"
      username: "Username"
      email: "Email"
      password: "Password"
      password_confirmation: "Confirm Password"
      account_exists: "Already have an account?"
      terms_agreement: "By signing up, you agree to our Terms of Service and Privacy Policy"
      steps:
        verification:
          title: "Verify Your Identity"
          phone_label: "Phone Number"
          phone_placeholder: "Please enter your mobile number"
          code_label: "Verification Code"
          code_placeholder: "Please enter the verification code"
          send_code: "Verification Code"
          next: "Next"
          have_account: "Do you have an account yet?"
        progress:
          verify_identity: "Verify Identity"
          create: "Create"
          account: "Account"
          send_code: "Send Code"
          create_account: "Create Account"
          subscribe: "Subscribe"
          step: "Step %{number}"
      pricing:
        get_started: "Get started"
      placeholders:
        username: "Enter username (letters, numbers and underscore only)"
        email: "<EMAIL>"
        password: "••••••••"
    passwords:
      forgot:
        title: "Forgot your password?"
        description: "Don't fret! Just type in your email and we will send you a code to reset your password!"
        email_label: "Your email"
        email_placeholder: "<EMAIL>"
        submit: "Reset password"
      reset:
        title: "Reset your password"
        new_password_label: "New password"
        confirm_password_label: "Confirm New Password"
        password_placeholder: "••••••••"
        submit: "Reset password"
      terms:
        accept: "I accept the"
        link: "Terms and Conditions"
      mailer:
        reset:
          html:
            message: "You can reset your password within the next 15 minutes on"
            link_text: "this password reset page"
          text:
            message: "You can reset your password within the next 15 minutes on this password reset page:"
    verification:
      code_sent: "Verification code sent"
      enter_code: "Enter verification code"
      resend_code: "Resend Code"
      verify_button: "Verify"
    omniauth:
      mismatch: "This %{provider} account is linked to a different user. Please sign out first!"
      signed_in: "Welcome back! You've been signed in via %{provider}."
      signed_up_and_in: "Welcome! Your account has been created and linked with %{provider}."
      linked: "Your %{provider} account has been successfully linked!"
      already_linked: "This %{provider} account is already linked to your profile."
      creation_failed: "We couldn't create your account using %{provider}. Please try again or register manually."
      failure: "%{provider} authentication failed. Please try again."
      or_continue_with: "Or continue with"
