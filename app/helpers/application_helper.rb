module ApplicationHelper
  def flash_class_for(type)
    case type.to_s
    when "notice"
      "text-primary-800 bg-primary-50 dark:bg-gray-800 dark:text-primary-400"
    when "success"
      "text-green-800 bg-green-50 dark:bg-gray-800 dark:text-green-400"
    when "alert"
      "text-red-800 bg-red-50 dark:bg-gray-800 dark:text-red-400"
    else
      "text-gray-800 bg-gray-50 dark:bg-gray-800 dark:text-gray-400"
    end
  end

  def flash_button_class_for(type)
    case type.to_s
    when "notice"
      "bg-primary-50 text-primary-500 hover:bg-primary-200 focus:ring-primary-400 dark:bg-gray-800 dark:text-primary-400 dark:hover:bg-gray-700"
    when "success"
      "bg-green-50 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700"
    when "alert"
      "bg-red-50 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700"
    else
      "bg-gray-50 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700"
    end
  end

  def flash_icon_for(type)
    svg_class = "shrink-0 w-4 h-4"
    case type.to_s
    when "notice", "success"
      content_tag(:svg, class: svg_class, 'aria-hidden': true, xmlns: "http://www.w3.org/2000/svg", fill: "currentColor", viewBox: "0 0 20 20") do
        content_tag(:path, nil, d: "M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z")
      end
    when "alert"
      content_tag(:svg, class: svg_class, 'aria-hidden': true, xmlns: "http://www.w3.org/2000/svg", fill: "currentColor", viewBox: "0 0 20 20") do
        content_tag(:path, nil, d: "M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z")
      end
    else
      content_tag(:svg, class: svg_class, 'aria-hidden': true, xmlns: "http://www.w3.org/2000/svg", fill: "currentColor", viewBox: "0 0 20 20") do
        content_tag(:path, nil, d: "M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z")
      end
    end
  end
end
