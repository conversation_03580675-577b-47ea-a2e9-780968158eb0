<!-- 活动卡片组件 -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-all duration-200">
  <!-- 活动封面图 -->
  <div class="aspect-video bg-gray-100 dark:bg-gray-700 relative">
    <% if event.cover_image.attached? %>
      <%= image_tag event.cover_image, 
          class: "w-full h-full object-cover",
          loading: "lazy" %>
    <% else %>
      <!-- 默认封面 -->
      <div class="w-full h-full flex items-center justify-center">
        <svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
      </div>
    <% end %>
    
    <!-- 状态标识 -->
    <% unless event.published? %>
      <div class="absolute top-2 right-2">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
          <%= t("events.status.#{event.status}") %>
        </span>
      </div>
    <% end %>
  </div>
  
  <!-- 活动信息 -->
  <div class="p-6">
    <!-- 分类和价格 -->
    <div class="flex items-center justify-between mb-3">
      <% if event.event_category %>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
          <%= event.event_category.name %>
        </span>
      <% end %>
      
      <div class="text-sm font-medium text-gray-900 dark:text-white">
        <% if event.price > 0 %>
          <span class="text-primary-600 dark:text-primary-400">¥<%= event.price %></span>
          <% if event.member_price && event.member_price < event.price %>
            <span class="text-xs text-gray-500 line-through ml-1">¥<%= event.member_price %></span>
          <% end %>
        <% else %>
          <span class="text-green-600 dark:text-green-400"><%= t('events.show.free') %></span>
        <% end %>
      </div>
    </div>
    
    <!-- 活动标题 -->
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
      <%= link_to event.title, event_path(event), 
          class: "hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200" %>
    </h3>
    
    <!-- 活动描述 -->
    <% if event.description.present? %>
      <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
        <%= truncate(event.description, length: 120) %>
      </p>
    <% end %>
    
    <!-- 活动元信息 -->
    <div class="space-y-2 mb-4">
      <!-- 时间 -->
      <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
        </svg>
        <%= l(event.start_time, format: :long) %>
      </div>
      
      <!-- 地点 -->
      <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
        <% if event.is_online? %>
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 2a1 1 0 100 2h2a1 1 0 100-2h-2z" clip-rule="evenodd"></path>
          </svg>
          <%= t('events.show.online_event') %>
        <% else %>
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
          </svg>
          <%= event.event_location&.city || t('common.actions.tbd') %>
        <% end %>
      </div>
      
      <!-- 参与人数 -->
      <% if event.max_participants %>
        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
          <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
          </svg>
          <% if event.available_spots %>
            <%= t('events.show.spots_available', count: event.available_spots) %>
          <% else %>
            <%= t('events.show.unlimited_spots') %>
          <% end %>
        </div>
      <% end %>
    </div>
    
    <!-- 操作按钮 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <%= link_to event_path(event),
            class: "text-primary-600 hover:text-primary-500 text-sm font-medium" do %>
          <%= t('common.actions.view_details') %> →
        <% end %>
        


        <% if allowed_to?(:update?, event) %>
          <%= link_to edit_event_path(event),
              class: "text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200 text-sm font-medium" do %>
            <%= t('common.actions.edit') %>
          <% end %>
        <% end %>
      </div>
      
      <% if authenticated? && allowed_to?(:register?, event) %>
        <% if event.registered_users.include?(current_user) %>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <%= t('events.show.already_registered') %>
          </span>
        <% elsif event.full? %>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            <%= t('events.show.event_full') %>
          </span>
        <% else %>
          <%= form_with model: [event, EventRegistration.new], local: true, class: "inline-block" do |f| %>
            <%= f.submit t('events.show.register'),
                class: "inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-200 transition-colors duration-200 border-none cursor-pointer" %>
          <% end %>
        <% end %>
      <% end %>
    </div>
  </div>
</div> 