# language: zh-CN
功能: 静态页面
  作为一个网站访客
  我想要浏览网站的各个静态页面
  以便了解更多关于程序媛汇的信息

  场景: 访问首页
    当我访问首页
    那么我应该看到"程序媛汇"标题
    而且我应该看到"CODING GIRLS CLUB"副标题
    而且我应该看到使命和愿景部分
    而且我应该看到核心产品部分
    而且我应该看到项目成果部分

  场景: 访问关于我们页面
    当我访问关于我们页面
    那么我应该看到"关于我们"标题
    而且我应该看到"机构介绍"部分
    而且我应该看到"机构荣誉"部分
    而且我应该看到"联系我们"部分
    而且我应该看到联系邮箱"<EMAIL>"

  场景: 访问隐私政策页面
    当我访问隐私政策页面
    那么我应该看到"隐私政策"标题
    而且我应该看到"信息收集"部分
    而且我应该看到"信息使用"部分
    而且我应该看到"信息保护"部分
    而且我应该看到"Cookie使用"部分

  场景: 访问服务条款页面
    当我访问服务条款页面
    那么我应该看到"服务条款"标题
    而且我应该看到"服务说明"部分
    而且我应该看到"用户责任"部分
    而且我应该看到"付款和退款"部分
    而且我应该看到"知识产权"部分
