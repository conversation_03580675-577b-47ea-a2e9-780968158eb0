import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["dropdown", "badge", "list", "bell"]
  static values = { 
    userId: Number,
    unreadCount: Number 
  }

  connect() {
    this.setupActionCable()
    this.updateBadge()
  }

  disconnect() {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
  }

  async setupActionCable() {
    if (!this.userIdValue) return

    try {
      // 动态导入 ActionCable
      const { createConsumer } = await import("@rails/actioncable")
      
      this.consumer = createConsumer()
      this.subscription = this.consumer.subscriptions.create(
        { channel: "NotificationChannel" },
        {
          connected: () => {
            console.log("Connected to NotificationChannel")
          },

          disconnected: () => {
            console.log("Disconnected from NotificationChannel")
          },

          received: (data) => {
            this.handleNotification(data)
          }
        }
      )
    } catch (error) {
      console.log("ActionCable not available, notifications will work without real-time updates")
    }
  }

  handleNotification(data) {
    switch (data.type) {
      case "new_notification":
        this.addNotificationToList(data)
        this.unreadCountValue = data.unread_count
        this.updateBadge()
        this.showNotificationAlert(data)
        break
      case "notification_read":
        this.markNotificationAsRead(data.id)
        this.unreadCountValue = data.unread_count
        this.updateBadge()
        break
      case "all_notifications_read":
        this.markAllAsRead()
        this.unreadCountValue = 0
        this.updateBadge()
        break
    }
  }

  addNotificationToList(notification) {
    if (!this.hasListTarget) return

    const notificationElement = this.createNotificationElement(notification)
    this.listTarget.insertAdjacentHTML("afterbegin", notificationElement)
  }

  createNotificationElement(notification) {
    // Select a random icon color
    const iconColors = ['bg-primary-700', 'bg-gray-900', 'bg-red-600', 'bg-green-400', 'bg-purple-500']
    const iconColor = iconColors[Math.floor(Math.random() * iconColors.length)]
    
    return `
      <a href="${notification.url}" 
         class="flex px-4 py-3 border-b hover:bg-gray-100 dark:hover:bg-gray-600 dark:border-gray-600 notification-item" 
         data-notification-id="${notification.id}"
         data-action="click->notifications#markAsRead">
        <div class="flex-shrink-0 relative">
          <img class="w-11 h-11 rounded-full" src="https://api.dicebear.com/7.x/avataaars/svg?seed=${notification.id}" alt="User avatar">
          <div class="absolute flex items-center justify-center w-5 h-5 -mt-5 ml-6 border border-white rounded-full ${iconColor} dark:border-gray-700">
            <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
            </svg>
          </div>
        </div>
        <div class="w-full pl-3">
          <div class="text-gray-500 font-normal text-sm mb-1.5 dark:text-gray-400">
            ${notification.message}
          </div>
          <div class="text-xs font-medium text-primary-700 dark:text-primary-400">
            ${this.formatTime(notification.created_at)}
          </div>
        </div>
      </a>
    `
  }

  markNotificationAsRead(notificationId) {
    const element = this.element.querySelector(`[data-notification-id="${notificationId}"]`)
    if (element) {
      element.classList.add("opacity-60")
    }
  }

  markAllAsRead() {
    const elements = this.element.querySelectorAll(".notification-item")
    elements.forEach(el => el.classList.add("opacity-60"))
  }

  updateBadge() {
    if (!this.hasBadgeTarget) return

    if (this.unreadCountValue > 0) {
      this.badgeTarget.textContent = this.unreadCountValue
      this.badgeTarget.classList.remove("hidden")
    } else {
      this.badgeTarget.classList.add("hidden")
    }
  }

  showNotificationAlert(notification) {
    // 创建一个简单的通知提示
    const alert = document.createElement("div")
    alert.className = "fixed top-4 right-4 bg-blue-500 text-white p-4 rounded-md shadow-lg z-50"
    alert.innerHTML = `
      <div class="font-medium">${notification.message}</div>
      <button class="absolute top-1 right-1 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
        ×
      </button>
    `
    
    document.body.appendChild(alert)
    
    // 3秒后自动消失
    setTimeout(() => {
      if (alert.parentElement) {
        alert.remove()
      }
    }, 3000)
  }

  markAsRead(event) {
    event.preventDefault()
    const targetUrl = event.currentTarget.href
    
    // 跳转到目标页面，后台 show 方法会处理标记为已读
    window.location.href = targetUrl
  }

  toggle(event) {
    event.preventDefault()
    if (this.hasDropdownTarget) {
      this.dropdownTarget.classList.toggle("hidden")
    }
  }

  preventDoubleClick(event) {
    const link = event.currentTarget
    
    // 如果链接已经被点击过，防止重复点击
    if (link.classList.contains('clicking')) {
      event.preventDefault()
      return false
    }
    
    // 标记为正在点击
    link.classList.add('clicking')
    
    // 2秒后移除标记（防止永久锁定）
    setTimeout(() => {
      link.classList.remove('clicking')
    }, 2000)
    
    // 允许正常导航
    return true
  }

  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now - date) / (1000 * 60))
    
    if (diffInMinutes < 1) return "刚刚"
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}小时前`
    return `${Math.floor(diffInMinutes / 1440)}天前`
  }
}
