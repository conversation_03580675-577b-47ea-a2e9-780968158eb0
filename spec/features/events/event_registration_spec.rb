require 'rails_helper'

RSpec.describe 'Event Registration', type: :feature do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:event_creator) { create(:user) }

  describe '活动报名功能' do
    context '对于公开的活动' do
      let(:public_event) { create(:event, :published, :upcoming, visibility: :public_visibility, user: event_creator, max_participants: 10) }

      context '未登录用户' do
        it '应该显示登录提示' do
          visit event_path(public_event)

          expect(page).to have_content(public_event.title)
          expect(page).to have_content(I18n.t('common.auth.please_login'))
          expect(page).to have_link(I18n.t('common.auth.login_to_register'), href: sign_in_path)
          expect(page).not_to have_button(I18n.t('events.registration.register_button'))
        end
      end

      context '已登录用户' do
        before { sign_in user }

        it '应该能成功报名活动' do
          visit event_path(public_event)

          expect(page).to have_content(public_event.title)
          expect(page).to have_button(I18n.t('events.registration.register_button'))

          # 填写报名表单
          fill_in 'event_registration_notes', with: 'I am very interested in this event'
          click_button I18n.t('events.registration.register_button')

          # 验证报名成功
          expect(page).to have_content(I18n.t('events.registration.success'))
          expect(page).to have_content(I18n.t('events.show.already_registered'))
          expect(page).to have_button(I18n.t('events.registration.cancel_registration'))

          # 验证数据库中的记录
          registration = EventRegistration.find_by(user: user, event: public_event)
          expect(registration).to be_present
          expect(registration.status).to eq('confirmed')
          expect(registration.notes).to eq('I am very interested in this event')
        end

        it '不应该允许重复报名' do
          # 先报名一次
          create(:event_registration, user: user, event: public_event)

          visit event_path(public_event)

          expect(page).to have_content(I18n.t('events.show.already_registered'))
          expect(page).to have_button(I18n.t('events.registration.cancel_registration'))
          expect(page).not_to have_button(I18n.t('events.registration.register_button'))
        end

        it '应该能取消报名' do
          registration = create(:event_registration, user: user, event: public_event)

          visit event_path(public_event)

          expect(page).to have_content(I18n.t('events.show.already_registered'))

          # 取消报名
          accept_confirm do
            click_button I18n.t('events.registration.cancel_registration')
          end

          expect(page).to have_content(I18n.t('events.registration.cancelled'))
          expect(page).to have_button(I18n.t('events.registration.register_button'))
          expect(page).not_to have_content(I18n.t('events.show.already_registered'))

          # 验证数据库记录状态被更新为已取消
          expect(registration.reload.status).to eq('cancelled')
        end

        it '不应该允许报名已满的活动' do
          # 填满活动
          create_list(:event_registration, 10, event: public_event)

          visit event_path(public_event)

          expect(page).to have_content(I18n.t('events.show.event_full'))
          expect(page).not_to have_button(I18n.t('events.registration.register_button'))
        end

        it '不应该允许报名已取消的活动' do
          cancelled_event = create(:event, :cancelled, visibility: :public_visibility, user: event_creator)

          visit event_path(cancelled_event)

          expect(page).to have_content(I18n.t('events.show.event_cancelled'))
          expect(page).not_to have_button(I18n.t('events.registration.register_button'))
        end

        it '不应该允许报名过去的活动' do
          past_event = create(:event, :past, visibility: :public_visibility, user: event_creator)

          visit event_path(past_event)

          expect(page).not_to have_button(I18n.t('events.registration.register_button'))
        end

        it '应该正确显示参与人数' do
          # 创建一些报名记录
          create_list(:event_registration, 3, event: public_event)

          visit event_path(public_event)

          expect(page).to have_content(I18n.t('events.show.registered'))
          expect(page).to have_content('3 / 10')
          expect(page).to have_content(I18n.t('events.show.spots_remaining', count: 7))
        end
      end

      context '活动创建者不能报名自己的活动' do
        before { sign_in event_creator }

        it '创建者查看自己的活动时不应该看到报名按钮' do
          visit event_path(public_event)

          expect(page).to have_content(public_event.title)
          expect(page).not_to have_button(I18n.t('events.registration.register_button'))
          expect(page).to have_content(I18n.t('events.show.no_permission'))
        end
      end
    end

    context '对于团队活动' do
      let(:team) { create(:team) }
      let(:team_member) { create(:user) }
      let(:team_event) { create(:event, :published, :upcoming, visibility: :team_visibility, user: event_creator) }

      before do
        # 将用户添加到团队
        team_membership = create(:team_member, user: team_member, team: team)
                # 将活动关联到团队 - 使用正确的 resource_kind
                create(:team_resource_access,
               resource: team_event,
               team_member: team_membership,
               resource_kind: 'event')  # 明确指定 resource_kind
      end

      it '团队成员应该能报名团队活动' do
        sign_in team_member

        visit event_path(team_event)

        expect(page).to have_content(team_event.title)
        expect(page).to have_button(I18n.t('events.registration.register_button'))

        click_button I18n.t('events.registration.register_button')

                  expect(page).to have_content(I18n.t('events.registration.success'))
          expect(page).to have_content(I18n.t('events.show.already_registered'))
      end

      it '非团队成员不应该能看到团队活动' do
        sign_in other_user

        visit event_path(team_event)

        # 由于权限限制，应该重定向到首页并显示错误消息
        expect(current_path).to eq(root_path)
        expect(page).to have_content('You are not authorized')
      end
    end

    context '对于私有活动' do
      let(:private_event) { create(:event, :published, :upcoming, visibility: :private_visibility, user: event_creator) }

      it '创建者应该能看到自己的私有活动但不能报名' do
        sign_in event_creator

        visit event_path(private_event)

        expect(page).to have_content(private_event.title)
        expect(page).not_to have_button(I18n.t('events.registration.register_button'))
        expect(page).to have_content(I18n.t('events.show.no_permission'))
      end

      it '其他用户不应该能看到私有活动' do
        sign_in other_user

        visit event_path(private_event)

        # 由于权限限制，应该重定向到首页并显示错误消息
        expect(current_path).to eq(root_path)
        expect(page).to have_content('You are not authorized')
      end
    end

    context '价格显示' do
      it '应该正确显示免费活动' do
        free_event = create(:event, :published, :upcoming, visibility: :public_visibility, price: 0, user: event_creator)
        sign_in user

        visit event_path(free_event)

        expect(page).to have_content(I18n.t('events.show.free'))
      end

      it '应该正确显示付费活动' do
        paid_event = create(:event, :published, :upcoming, visibility: :public_visibility, price: 99.99, user: event_creator)
        sign_in user

        visit event_path(paid_event)

        expect(page).to have_content('¥99.99')
      end

      it '应该正确显示会员优惠价格' do
        event_with_discount = create(:event, :published, :upcoming, visibility: :public_visibility, price: 100, member_price: 80, user: event_creator)
        sign_in user

        visit event_path(event_with_discount)

        expect(page).to have_content('¥100')
        expect(page).to have_content('¥80')
        expect(page).to have_content(I18n.t('events.show.member_discount_price', price: 80))
      end
    end

    context '在线活动' do
      let(:online_event) { create(:event, :published, :upcoming, :online, visibility: :public_visibility, user: event_creator) }

      it '已报名用户应该能看到会议链接' do
        sign_in user
        create(:event_registration, user: user, event: online_event)

        visit event_path(online_event)

        expect(page).to have_content(I18n.t('events.show.online_event'))
        expect(page).to have_link(I18n.t('events.show.join_meeting'))
      end

      it '未报名用户不应该看到会议链接' do
        sign_in user

        visit event_path(online_event)

        expect(page).to have_content(I18n.t('events.show.online_event'))
        expect(page).not_to have_link(I18n.t('events.show.join_meeting'))
      end
    end

      context '活动管理权限' do
        let(:public_event) { create(:event, :published, :upcoming, visibility: :public_visibility, user: event_creator) }

        it '活动创建者应该看到管理选项' do
          sign_in event_creator

          visit event_path(public_event)

        expect(page).to have_content(I18n.t('events.actions.management_actions'))
        expect(page).to have_link(I18n.t('events.actions.edit_event'))
        expect(page).to have_link(I18n.t('events.actions.manage_registrations'))
        expect(page).to have_link(I18n.t('events.actions.delete_event'))
      end

      it '普通用户不应该看到管理选项' do
        sign_in user

        visit event_path(public_event)

        expect(page).not_to have_content(I18n.t('events.actions.management_actions'))
        expect(page).not_to have_link(I18n.t('events.actions.edit_event'))
        expect(page).not_to have_link(I18n.t('events.actions.manage_registrations'))
        expect(page).not_to have_link(I18n.t('events.actions.delete_event'))
      end
    end
  end

  describe '活动报名列表管理' do
    let(:managed_event) { create(:event, :published, :upcoming, visibility: :public_visibility, user: event_creator) }
    let!(:registrations) { create_list(:event_registration, 3, event: managed_event) }

    context '活动创建者' do
      before { sign_in event_creator }

      it '应该能查看报名列表' do
        visit event_event_registrations_path(managed_event)

        expect(page).to have_content(I18n.t('events.registrations.title'))
        registrations.each do |registration|
          expect(page).to have_content(registration.user.username || registration.user.email_address)
        end
      end
    end

    context '普通用户' do
      before { sign_in user }

      it '不应该能访问报名列表' do
        visit event_event_registrations_path(managed_event)

        # 由于权限限制，应该重定向到首页并显示错误消息
        expect(current_path).to eq(root_path)
        expect(page).to have_content('You are not authorized')
      end
    end
  end

  # 错误处理测试
  describe '错误处理' do
    it '应该处理不存在的活动' do
      sign_in user

      visit event_path(999999)

      # Rails 会显示 404 错误页面
      expect(page).to have_content('404')
    end

    it '应该处理报名服务错误' do
      sign_in user
      full_event = create(:event, :published, :upcoming, visibility: :public_visibility, max_participants: 1, user: event_creator)
      create(:event_registration, event: full_event)

      visit event_path(full_event)

      expect(page).to have_content(I18n.t('events.show.event_full'))
      expect(page).not_to have_button(I18n.t('events.registration.register_button'))
    end
  end
end
