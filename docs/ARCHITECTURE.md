# 系统架构文档

本文档描述了 AI Pro CGC 项目的系统架构设计。

## 目录

- [总体架构](#总体架构)
- [技术架构](#技术架构)
- [数据架构](#数据架构)
- [部署架构](#部署架构)
- [安全架构](#安全架构)
- [扩展性设计](#扩展性设计)

## 总体架构

### 系统组件图

```mermaid
graph TB
    subgraph "Frontend 前端层"
        UI[Web UI]
        Mobile[Mobile App]
        API_Client[API Client]
    end
    
    subgraph "Application 应用层"
        Rails[Rails Application]
        Controllers[Controllers]
        Services[Service Objects]
        Jobs[Background Jobs]
    end
    
    subgraph "Data 数据层"
        DB[(PostgreSQL)]
        Redis[(Redis Cache)]
        Vector[(Qdrant Vector DB)]
        S3[(AWS S3)]
    end
    
    subgraph "External 外部服务"
        OpenAI[OpenAI API]
        Doubao[Doubao API]
        WeChat[WeChat API]
        SMS[SMS Service]
    end
    
    UI --> Rails
    Mobile --> Rails
    API_Client --> Rails
    
    Rails --> Controllers
    Controllers --> Services
    Services --> Jobs
    
    Rails --> DB
    Rails --> Redis
    Rails --> Vector
    Rails --> S3
    
    Services --> OpenAI
    Services --> Doubao
    Services --> WeChat
    Services --> SMS
```

### 分层架构

```mermaid
graph TD
    A[Presentation Layer 表现层] --> B[Business Logic Layer 业务逻辑层]
    B --> C[Data Access Layer 数据访问层]
    C --> D[Infrastructure Layer 基础设施层]
    
    subgraph "表现层"
        A1[Controllers]
        A2[Views]
        A3[API Endpoints]
    end
    
    subgraph "业务逻辑层"
        B1[Service Objects]
        B2[Models]
        B3[Policies]
        B4[Jobs]
    end
    
    subgraph "数据访问层"
        C1[ActiveRecord]
        C2[Cache Layer]
        C3[Vector Store]
    end
    
    subgraph "基础设施层"
        D1[Database]
        D2[Message Queue]
        D3[File Storage]
        D4[External APIs]
    end
```

## 技术架构

### 核心技术栈

| 层级           | 技术选择                   | 说明                |
| -------------- | -------------------------- | ------------------- |
| **Web 框架**   | Rails 8.0                  | 主要的 Web 应用框架 |
| **前端**       | Hotwire (Turbo + Stimulus) | 现代化前端交互      |
| **样式**       | Tailwind CSS v4            | 实用优先的 CSS 框架 |
| **数据库**     | PostgreSQL/SQLite          | 主数据库            |
| **缓存**       | Redis/Solid Cache          | 缓存和会话存储      |
| **队列**       | Solid Queue                | 后台任务处理        |
| **向量数据库** | Qdrant                     | AI 嵌入向量存储     |
| **文件存储**   | AWS S3                     | 文件和图片存储      |
| **AI 框架**    | Langchain.rb               | AI 应用开发框架     |

### AI 系统架构

```mermaid
graph TB
    subgraph "AI Pipeline"
        Input[User Input] --> Parser[Content Parser]
        Parser --> Router[Intent Router]
        
        Router --> Assistant[AI Assistant]
        Router --> KnowledgeBase[Knowledge Base]
        Router --> Tools[External Tools]
        
        Assistant --> LLM[Language Model]
        KnowledgeBase --> Vector[Vector Search]
        Tools --> APIs[External APIs]
        
        LLM --> Response[AI Response]
        Vector --> Context[Context]
        APIs --> ToolResult[Tool Results]
        
        Context --> LLM
        ToolResult --> LLM
        
        Response --> Output[Final Output]
    end
    
    subgraph "RAG System"
        Document[Documents] --> Chunker[Text Chunker]
        Chunker --> Embedder[Embedding Generator]
        Embedder --> VectorDB[(Vector Database)]
        
        Query[User Query] --> QEmbedder[Query Embedder]
        QEmbedder --> Retriever[Document Retriever]
        VectorDB --> Retriever
        Retriever --> Reranker[Result Reranker]
        Reranker --> Context
    end
```

## 数据架构

### 核心实体关系图

```mermaid
erDiagram
    User ||--o{ Session : has
    User ||--o{ OmniAuthIdentity : has
    User ||--o{ Assistant : creates
    User ||--o{ Conversation : has
    User ||--o{ Post : writes
    User ||--o{ KnowledgeBase : owns
    User ||--o{ TeamMember : joins
    
    Assistant ||--o{ Conversation : uses
    Conversation ||--o{ Message : contains
    
    KnowledgeBase ||--o{ KnowledgeBasePost : contains
    KnowledgeBase ||--o{ TeamResourceAccess : grants
    
    Post ||--o{ KnowledgeBasePost : belongs_to
    Post ||--o{ BaseUnit : generates_from
    
    Team ||--o{ TeamMember : has
    Team ||--o{ TeamResourceAccess : provides
    
    Message ||--o{ ToolUsage : triggers
    
    User {
        bigint id PK
        string username
        string email_address
        string phone_number
        text preferences
        datetime created_at
    }
    
    Assistant {
        bigint id PK
        bigint user_id FK
        string name
        text instructions
        string tool_choice
        json tools
        datetime created_at
    }
    
    KnowledgeBase {
        bigint id PK
        bigint owner_id FK
        string name
        text description
        string visibility
        boolean archived
        datetime created_at
    }
    
    Conversation {
        bigint id PK
        bigint user_id FK
        bigint assistant_id FK
        string title
        datetime created_at
    }
    
    Message {
        bigint id PK
        bigint conversation_id FK
        string role
        text content
        json tool_calls
        string status
        datetime created_at
    }
```

### 数据流架构

```mermaid
graph LR
    subgraph "Input Sources"
        WebUI[Web Interface]
        API[REST API]
        WeChat[WeChat Bot]
        Upload[File Upload]
    end
    
    subgraph "Processing Layer"
        Router[Request Router]
        Auth[Authentication]
        Validation[Data Validation]
        BusinessLogic[Business Logic]
    end
    
    subgraph "Storage Layer"
        Primary[(Primary DB)]
        Cache[(Cache)]
        Vector[(Vector DB)]
        Files[(File Storage)]
    end
    
    subgraph "AI Processing"
        Embedder[Text Embedder]
        LLM[Language Model]
        Tools[AI Tools]
    end
    
    WebUI --> Router
    API --> Router
    WeChat --> Router
    Upload --> Router
    
    Router --> Auth
    Auth --> Validation
    Validation --> BusinessLogic
    
    BusinessLogic --> Primary
    BusinessLogic --> Cache
    BusinessLogic --> Vector
    BusinessLogic --> Files
    
    BusinessLogic --> Embedder
    BusinessLogic --> LLM
    BusinessLogic --> Tools
    
    Embedder --> Vector
    LLM --> Primary
    Tools --> Primary
```

## 部署架构

### 生产环境架构

```mermaid
graph TB
    subgraph "CDN & Load Balancer"
        CDN[CloudFlare CDN]
        LB[Load Balancer]
    end
    
    subgraph "Application Servers"
        App1[Rails App 1]
        App2[Rails App 2]
        App3[Rails App 3]
    end
    
    subgraph "Background Workers"
        Worker1[Solid Queue Worker 1]
        Worker2[Solid Queue Worker 2]
    end
    
    subgraph "Data Layer"
        DB[(PostgreSQL Primary)]
        DBReplica[(PostgreSQL Replica)]
        Redis[(Redis Cluster)]
        Vector[(Qdrant Cluster)]
    end
    
    subgraph "External Storage"
        S3[(AWS S3)]
        Monitoring[Monitoring & Logs]
    end
    
    CDN --> LB
    LB --> App1
    LB --> App2
    LB --> App3
    
    App1 --> DB
    App2 --> DB
    App3 --> DB
    
    App1 --> Redis
    App2 --> Redis
    App3 --> Redis
    
    Worker1 --> DB
    Worker2 --> DB
    Worker1 --> Redis
    Worker2 --> Redis
    
    DB --> DBReplica
    
    App1 --> Vector
    App2 --> Vector
    App3 --> Vector
    
    App1 --> S3
    App2 --> S3
    App3 --> S3
```

### Docker 容器架构

```mermaid
graph TB
    subgraph "Docker Environment"
        subgraph "Web Container"
            Rails[Rails Application]
            Nginx[Nginx Proxy]
        end
        
        subgraph "Worker Container"
            SolidQueue[Solid Queue Workers]
        end
        
        subgraph "Database Container"
            PostgreSQL[(PostgreSQL)]
        end
        
        subgraph "Cache Container"
            RedisContainer[(Redis)]
        end
        
        subgraph "Vector Container"
            QdrantContainer[(Qdrant)]
        end
    end
    
    Nginx --> Rails
    Rails --> PostgreSQL
    Rails --> RedisContainer
    Rails --> QdrantContainer
    SolidQueue --> PostgreSQL
    SolidQueue --> RedisContainer
```

## 安全架构

### 安全层级

```mermaid
graph TD
    subgraph "Network Security"
        WAF[Web Application Firewall]
        HTTPS[HTTPS/TLS 1.3]
        VPN[VPN Access]
    end
    
    subgraph "Application Security"
        Auth[Authentication]
        AuthZ[Authorization]
        CSRF[CSRF Protection]
        XSS[XSS Protection]
    end
    
    subgraph "Data Security"
        Encryption[Data Encryption]
        Backup[Secure Backup]
        Audit[Audit Logging]
    end
    
    WAF --> HTTPS
    HTTPS --> Auth
    Auth --> AuthZ
    AuthZ --> Encryption
    
    CSRF --> XSS
    XSS --> Audit
    Audit --> Backup
```

### 认证与授权流程

```mermaid
sequenceDiagram
    participant User
    participant App
    participant Session
    participant Database
    participant OAuth
    
    User->>App: 登录请求
    
    alt 邮箱/密码登录
        App->>Database: 验证凭据
        Database-->>App: 用户信息
    else OAuth 登录
        App->>OAuth: 重定向到 OAuth 提供商
        OAuth-->>App: 授权码
        App->>OAuth: 交换访问令牌
        OAuth-->>App: 用户信息
    end
    
    App->>Session: 创建会话
    Session-->>App: 会话令牌
    App-->>User: 登录成功
    
    User->>App: 后续请求 (带令牌)
    App->>Session: 验证会话
    Session-->>App: 会话有效
    App->>Database: 权限检查
    Database-->>App: 权限确认
    App-->>User: 返回结果
```

## 扩展性设计

### 水平扩展策略

1. **应用层扩展**
   - 无状态应用设计
   - 负载均衡器分发请求
   - 容器化部署

2. **数据层扩展**
   - 读写分离
   - 数据库分片
   - 缓存分层

3. **AI 服务扩展**
   - 多模型支持
   - 模型负载均衡
   - 异步处理队列

### 微服务迁移路径

```mermaid
graph TB
    subgraph "当前单体架构"
        Monolith[Rails Monolith]
    end
    
    subgraph "目标微服务架构"
        APIGateway[API Gateway]
        UserService[用户服务]
        AIService[AI 服务]
        KnowledgeService[知识库服务]
        WeChatService[微信服务]
        
        APIGateway --> UserService
        APIGateway --> AIService
        APIGateway --> KnowledgeService
        APIGateway --> WeChatService
    end
    
    Monolith -.-> APIGateway
```

### 性能优化点

1. **数据库优化**
   ```sql
   -- 示例索引策略
   CREATE INDEX idx_messages_conversation_created ON messages(conversation_id, created_at);
   CREATE INDEX idx_posts_user_published ON posts(user_id, published, created_at);
   CREATE INDEX idx_knowledge_bases_visibility ON knowledge_bases(visibility, archived);
   ```

2. **缓存策略**
   ```ruby
   # 多层缓存示例
   class CacheStrategy
     def self.get_user_assistants(user_id)
       Rails.cache.fetch("user:#{user_id}:assistants", expires_in: 1.hour) do
         User.find(user_id).assistants.includes(:conversations)
       end
     end
   end
   ```

3. **异步处理**
   ```ruby
   # 后台任务示例
   class Ai::ResponseJob < ApplicationJob
     queue_as :ai_processing
     
     def perform(conversation_id, message_content)
       # AI 处理逻辑
     end
   end
   ```

## 监控与可观测性

### 监控架构

```mermaid
graph TB
    subgraph "Application Metrics"
        AppMetrics[Application Metrics]
        Performance[Performance Metrics]
        Business[Business Metrics]
    end
    
    subgraph "Infrastructure Metrics"
        SystemMetrics[System Metrics]
        DatabaseMetrics[Database Metrics]
        CacheMetrics[Cache Metrics]
    end
    
    subgraph "Monitoring Stack"
        Prometheus[Prometheus]
        Grafana[Grafana]
        AlertManager[Alert Manager]
    end
    
    subgraph "Logging"
        AppLogs[Application Logs]
        AccessLogs[Access Logs]
        ErrorLogs[Error Logs]
        ELK[ELK Stack]
    end
    
    AppMetrics --> Prometheus
    SystemMetrics --> Prometheus
    Prometheus --> Grafana
    Prometheus --> AlertManager
    
    AppLogs --> ELK
    AccessLogs --> ELK
    ErrorLogs --> ELK
```

### 关键指标

1. **应用性能指标**
   - 响应时间 (95th percentile < 500ms)
   - 吞吐量 (QPS)
   - 错误率 (< 0.1%)
   - 可用性 (99.9%+)

2. **AI 服务指标**
   - 模型响应时间
   - Token 使用量
   - 向量检索性能
   - 工具调用成功率

3. **业务指标**
   - 用户活跃度
   - 对话完成率
   - 知识库利用率
   - 存储使用情况

这个架构设计确保了系统的可扩展性、可维护性和高可用性，同时为未来的功能扩展和性能优化提供了坚实的基础。 