<section class="bg-white dark:bg-gray-900">
  <div class="px-4 py-8 mx-auto max-w-(--breakpoint-xl) lg:py-16">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white"><%= @knowledge_base.name %></h1>
        <p class="mt-2 text-lg text-gray-500 dark:text-gray-400"><%= @knowledge_base.description %></p>
      </div>

      <div class="flex gap-2 items-center">
        <div class="flex items-center gap-4" data-controller="upload" data-upload-url-value="/knowledge_bases/<%= @knowledge_base.id %>/create_base_unit" data-upload-form-data-value="<%= { custom_param: 'value' }.to_json %>">
          <button class="text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                  data-action="click->upload#triggerFileInput">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <%= t('knowledge_bases.show.new_base_unit') %>
          </button>

          <input type="file"
                class="hidden"
                data-upload-target="fileInput"
                data-action="change->upload#handleFileSelect"
                accept="<%= BaseUnit::ALLOWED_TYPES.keys.join(',') %>"
                multiple>
        </div>
        <% if allowed_to?(:archive?, @knowledge_base) %>
          <% if @knowledge_base.archived? %>
            <%= button_to "Unarchive", unarchive_knowledge_base_path(@knowledge_base),
                method: :patch,
                class: "text-red-500 hover:text-red-600 border border-red-500 hover:border-red-600 focus:ring-4 focus:outline-hidden focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-red-600 dark:hover:border-red-600 dark:focus:ring-red-800",
                form: { class: "inline-block" } %>
          <% else %>
            <%= button_to "Archive", archive_knowledge_base_path(@knowledge_base),
                method: :patch,
                class: "text-red-500 hover:text-red-600 border border-red-500 hover:border-red-600 focus:ring-4 focus:outline-hidden focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-red-600 dark:hover:border-red-600 dark:focus:ring-red-800",
                form: { class: "inline-block" } %>
          <% end %>
        <% end %>

        <% if allowed_to?(:edit?, @knowledge_base) %>
          <%= link_to "Edit", edit_knowledge_base_path(@knowledge_base),
              class: "text-primary-500 hover:text-primary-600 border border-primary-500 hover:border-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-primary-600 dark:hover:border-primary-600 dark:focus:ring-primary-800" %>

          <% if allowed_to?(:destroy?, @knowledge_base) && @knowledge_base.teams.empty? %>
            <%= link_to "Delete",
                knowledge_base_path(@knowledge_base),
                class: "text-red-500 hover:text-red-600 border border-red-500 hover:border-red-600 focus:ring-4 focus:outline-hidden focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-red-500 dark:text-red-500 dark:hover:text-red-600 dark:hover:border-red-600 dark:focus:ring-red-800",
                data: {
                  turbo_method: :delete,
                  turbo_confirm: t('knowledge_bases.show.delete_confirm')
                } %>
          <% end %>
        <% end %>
      </div>
    </div>

    <div class="p-6 bg-white rounded-lg border border-gray-200 shadow-xs dark:bg-gray-800 dark:border-gray-700">
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div>
          <h3 class="mb-2 text-lg font-semibold text-gray-900 dark:text-white"><%= t('knowledge_bases.show.details') %></h3>
          <dl class="max-w-md text-gray-900 divide-y divide-gray-200 dark:text-white dark:divide-gray-700">
            <div class="flex flex-col py-3">
              <dt class="mb-1 text-gray-500 dark:text-gray-400"><%= t('common.time_formats.created_at') %></dt>
              <dd class="text-sm"><%= l @knowledge_base.created_at, format: :long %></dd>
            </div>
            <div class="flex flex-col py-3">
              <dt class="mb-1 text-gray-500 dark:text-gray-400"><%= t('common.time_formats.updated_at') %></dt>
              <dd class="text-sm"><%= l @knowledge_base.updated_at, format: :long %></dd>
            </div>
            <div class="flex flex-col py-3">
              <dt class="mb-1 text-gray-500 dark:text-gray-400"><%= t('knowledge_bases.show.visibility') %></dt>
              <dd class="text-sm">
                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">
                  <%= @knowledge_base.visibility.humanize %>
                </span>
              </dd>
            </div>
          </dl>
        </div>

        <div>
          <h3 class="mb-2 text-lg font-semibold text-gray-900 dark:text-white"><%= t('knowledge_bases.show.statistics') %></h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('knowledge_bases.show.documents') %></dt>
              <%= link_to posts_knowledge_base_path(@knowledge_base), class: "hover:text-blue-600" do %>
                <dd class="text-xl font-bold text-gray-900 dark:text-white"><%= @knowledge_base.posts.count %></dd>
              <% end %>
            </div>
            <div class="p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('knowledge_bases.show.contributors') %></dt>
              <dd class="text-xl font-bold text-gray-900 dark:text-white">1</dd>
            </div>
          </div>
        </div>
      </div>
    </div>

    <% if @knowledge_base.team_visibility? %>
      <div class="mt-4">
        <%= link_to teams_knowledge_base_path(@knowledge_base),
               class: "text-primary-500 hover:text-primary-600 border border-primary-500 hover:border-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:border-primary-500 dark:text-primary-500 dark:hover:text-primary-600 dark:hover:border-primary-600 dark:focus:ring-primary-800" do %>
          <i class="fas fa-users-cog mr-2"></i><%= t('knowledge_bases.show.team_settings') %>
        <% end %>
      </div>
    <% end %>

    <%= form_with(url: create_post_knowledge_base_path(@knowledge_base), class: "mt-8") do |f| %>
      <div class="w-full mb-4 border border-gray-200 rounded-lg bg-gray-50 dark:bg-gray-700 dark:border-gray-600">
          <div class="px-4 py-2 bg-white rounded-t-lg dark:bg-gray-800">
              <%= f.label :content, "Your comment", class: "sr-only" %>
              <%= f.text_area :content, rows: 4,
                  class: "w-full px-0 text-sm text-gray-900 bg-white border-0 dark:bg-gray-800 focus:ring-0 dark:text-white dark:placeholder-gray-400",
                  placeholder: "Write a post...",
                  required: true %>
          </div>
          <div class="flex items-center justify-between px-3 py-2 border-t dark:border-gray-600 border-gray-200">
              <%= f.submit "Post commit",
                  class: "inline-flex items-center py-2.5 px-4 text-xs font-medium text-center text-white bg-blue-700 rounded-lg focus:ring-4 focus:ring-blue-200 dark:focus:ring-blue-900 hover:bg-blue-800" %>
          </div>
      </div>
    <% end %>

  </div>
</section>
