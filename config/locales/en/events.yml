# Events module English translations
en:
  events:
    calendar:
      modal_title_placeholder: "All events on %{date}"
    index:
      map_load_failed:
        title: "Map Load Failed"
        description: "Unable to load map data, please check your network connection and try again."
      map:
        locate_me: "Locate Me"
        reset_view: "Reset View"
        range: "Range"
        distance_km: "%{distance}km"
        loading_map: "Loading map..."
        fetching_events: "Fetching event locations"
      title: "Events"
      grid_title: "Event List"
      map_title: "Event Map"
      upcoming: "Upcoming"
      past: "Past Events"
      all: "All Events"
      no_events: "No events found"
      create_first: "Create your first event"
      no_past_events: "No past events"
      no_past_events_description: "There are no past events to display at the moment."
      no_upcoming_events: "No upcoming events"
      no_upcoming_events_description: "There are no upcoming events scheduled. Create one to get started!"
      no_events_found: "No events found"
      no_events_found_description: "Try adjusting your search criteria or filters."
      tabs:
        upcoming: "Upcoming Events"
        past: "Past Events"
        all: "All Events"
    views:
      list: "List View"
    show:
      more_participants: "and %{count} more participants"
    # Basic vocabulary
    event: "Event"
    events: "Events"
    # Form related
    form:
      basic_info: "Basic Information"
      schedule_location: "Schedule & Location"
      pricing_capacity: "Pricing & Capacity"
      visibility: "Visibility Settings"
      status_management: "Status Management"
      event_stats: "Event Statistics"
      quick_actions: "Quick Actions"
      current_status: "Current Status"
      error:
        one: "1 error needs to be fixed:"
        other: "%{count} errors need to be fixed:"
      title_placeholder: "Give your event an attractive title..."
      description_placeholder: "Describe your event content, objectives, and highlights..."
      select_category: "Select Category"
      select_difficulty: "Select Difficulty"
      status_tip_title: "Status Selection Tip"
      status_tip_content: 'Choose "Draft" to refine later before publishing; choose "Published" to make it immediately visible to users for registration.'
      tips:
        title: "Creation Tips"
        clear_title: "Use a clear and attractive title"
        detailed_description: "Provide detailed event description"
        appropriate_capacity: "Set appropriate participant limit"
        clear_schedule: "Ensure clear timing"

    # Event-specific actions (removed basic actions that are in common.yml)
    actions:
      view_event: "View Event"
      edit_event: "Edit Event"
      publish_event: "Publish Event"
      cancel_event: "Cancel Event"
      manage_registrations: "Manage Registrations"
      view_analytics: "View Analytics"
      copy_link: "Copy Link"
      share_event: "Share Event"
      management_actions: "Management Actions"
      delete_event: "Delete Event"

    # Confirmation dialogs
    confirmations:
      publish: "Are you sure you want to publish this event? Users will be able to see and register after publishing."
      cancel: "Are you sure you want to cancel this event? Registered users will be notified."
      cancel_registration: "Are you sure you want to cancel your registration?"
      delete_event: "Are you sure you want to delete this event?"
      cancel_with_warning: "⚠️ Are you sure you want to cancel this event?\n\nThis will cancel the entire event and registered users will be notified. This action cannot be undone!"

    # Event-specific fields
    fields:
      title: "Title"
      description: "Description"
      prerequisites: "Prerequisites"
      category: "Category"
      status: "Publication Status"
      difficulty: "Difficulty"
      start_time: "Start Time"
      end_time: "End Time"
      is_online: "Online Event"
      meeting_link: "Meeting Link"
      price: "Price"
      member_price: "Member Price"
      max_participants: "Max Participants"
      min_participants: "Min Participants"
      what_to_bring: "What to Bring"
      what_included: "What's Included"
      cover_image: "Cover Image"
      documents: "Documents"

    # DateTime Picker
    datetime_picker:
      modal_title: "Select Date and Time"
      select_date: "Select Date"
      select_time: "Select Time"
      hour: "Hour"
      minute: "Minute"
      start_time_button: "Start Time"
      end_time_button: "End Time"
      start_time_placeholder: "Please select start time"
      end_time_placeholder: "Please select end time"
      select_datetime_alert: "Please select date and time"

    # Event visibility options
    visibility:
      public_visibility:
        title: "Public Event"
        description: "Everyone can see and register"
      team_visibility:
        title: "Team Event"
        description: "Only team members can see and register"
      private_visibility:
        title: "Private Event"
        description: "Only you can see this event"

    # Statistics
    stats:
      registrations: "Registrations"
      created: "Created"
      published: "Published"

    # Page titles and navigation merged above

    show:
      details: "Event Details"
      register: "Register"
      already_registered: "Already Registered"
      event_full: "Event Full"
      event_cancelled: "Event Cancelled"
      spots_available: "Spots available: %{count}"
      unlimited_spots: "Unlimited spots"
      instructors: "Instructors"
      location: "Location"
      online_event: "Online Event"
      join_meeting: "Join Meeting"
      organizer: "Organizer"
      to: "to"
      location_tbd: "Location TBD"
      free: "Free"
      member_discount_price: "Member discount price ¥%{price}"
      registered: "Registered"
      spots_remaining: "%{count} spots remaining"
      no_permission: "No permission to register for this event"
      please_login: "Please log in first to register"
      login_to_register: "Login to Register"
      view_details: "View Details →"
      date: "Date"
      category: "Category"
      participants: "Participants"
      description: "Description"

    new:
      title: "Create Event"
      create: "Create Event"
      create_button: "Create Event"

    edit:
      title: "Edit Event"
      subtitle: "Edit Event: %{title}"
      save: "Save Changes"
      update_button: "Update Event"
      save_event_info: "💾 Save Event Information"
      discard_changes: "Discard Changes"
      danger_operation: "⚠️ Danger Operation"
      cancel_this_event: "Cancel This Event"
      status_management_tip: '💡 <strong>Tip:</strong> If you only want to modify event information, please use the "Save Changes" button below. Only click the red button above when you want to completely cancel this event.'
      form_explanation: "📝 <strong>Form Instructions:</strong> This button only saves modifications to the basic event information and will not change the event status. To cancel the event, please use the red button in the Status Management section above."

    # Event-specific messages (using common.messages for basic ones)
    created_successfully: "Event created successfully"
    updated_successfully: "Event updated successfully"
    archived_successfully: "Event archived successfully"
    published_successfully: "Event published successfully"
    publish_failed: "Failed to publish event"
    cancelled_successfully: "Event cancelled successfully"
    cancel_failed: "Failed to cancel event"

    # Event-specific search and filtering
    search:
      placeholder: "Search events by title, description, requirements..."
      no_results: "No events found"
      suggestions: "Search Suggestions"
      filters: "Filters"
      all_categories: "All Categories"
      all_cities: "All Cities"
      sort_by: "Sort by"
      relevance: "Relevance"
      difficulty_level: "Difficulty Level"
      free: "Free"
      under_50: "Under $50"
      "50_to_100": "$50-$100"
      over_100: "Over $100"
      sort:
        date_asc: "Date (Early to Late)"
        date_desc: "Date (Late to Early)"
        price_asc: "Price (Low to High)"
        price_desc: "Price (High to Low)"
        name_asc: "Name (A-Z)"
        popularity: "Popularity"

    # Registration related
    registration:
      title: "Event Registration"
      notes: "Notes"
      notes_placeholder: "Enter any special requirements or notes..."
      register_button: "Confirm Registration"
      cancel_registration: "Cancel Registration"
      success: "Registration successful! We will confirm your registration via email."
      cancelled: "Registration cancelled"

      # Event-specific registration errors
      event_not_accessible: "You don't have access to this event"
      event_full: "Event is full, cannot register"
      already_registered: "You are already registered for this event"
      event_cancelled: "Event is cancelled, cannot register"
      system_error: "System error, please try again later"
      already_cancelled: "Registration is already cancelled"
      cancellation_system_error: "Failed to cancel registration, please try again later"

    # Participants and registrations management
    participants:
      title: "Event Participants"
      total_count: "Total participants: %{count}"
      participant: "Participant"
      registered_at: "Registered At"
      amount_paid: "Amount Paid"
      notes: "Notes"
      cancel_confirm: "Are you sure you want to cancel this registration?"
      no_participants: "No participants yet"
      no_participants_desc: "Participants will appear here once users sign up"

    registrations:
      title: "Registration Management"
      event_title: "Event: %{event}"
      total_count: "Total registrations: %{count}"
      capacity: "Capacity: %{current}/%{max}"
      participant_list: "Registration List"
      participant: "Participant"
      registered_at: "Registered At"
      amount_paid: "Amount Paid"
      notes: "Notes"
      cancel_confirm: "Are you sure you want to cancel this registration?"
      no_registrations: "No registrations yet"
      no_registrations_desc: "Registrations will appear here once users sign up"
      back_to_event: "Back to Event"
      all_statuses: "All Statuses"
      showing_results: "Showing %{count} records"
      view_event_details: "View Event Details"

    # Categories and locations
    categories:
      title: "Event Categories"
      name: "Category Name"
      description: "Category Description"
      color: "Category Color"
      icon: "Category Icon"

    locations:
      title: "Event Locations"
      name: "Location Name"
      address: "Address"
      city: "City"
      province: "Province/State"
      country: "Country"
      directions: "Directions"

    # Instructor information
    instructors:
      title: "Instructor Team"
      role: "Role"
      bio: "Bio"
      instructor: "Instructor"
      co_instructor: "Co-Instructor"
      assistant: "Assistant"
      guest_speaker: "Guest Speaker"

    # Navigation and menu
    my_events: "My Events"
    my_registrations: "My Registrations"
    navigation:
      my_events: "My Events"
      create_event: "Create Event"
      event_analytics: "Event Analytics"
      upcoming_events: "Upcoming Events"
      past_events: "Past Events"
      manage_registrations: "Manage Registrations"

    # Empty states
    empty_states:
      no_events: "No events"
      no_upcoming: "No upcoming events"
      no_past: "No past events"
      no_all: "No events"
      no_upcoming_desc: "Create your first event"
      no_past_desc: "No past events found"
      no_all_desc: "No events in the system yet"
      no_registrations: "No registrations"
      create_first_event: "Create your first event"

    # Calendar view
    calendar:
      title: "Event Calendar"
      today: "Today"
      month_year: "%B %Y"
      more: "more"
      no_events: "No events"
      no_events_description: "No events scheduled for this day"
      loading: "Loading..."
      all_events_on: "'s all events"

    # Location
    location:
      online: "Online Event"

    # Page actions
    actions:
      edit: "Edit"
      delete: "Delete"
      cancel: "Cancel"
      register: "Register"
      unregister: "Unregister"
      management_actions: "Management Actions"
      edit_event: "Edit Event"
      manage_registrations: "Manage Registrations"
      delete_event: "Delete Event"

    # 视图切换
    views:
      grid: "Grid View"
      calendar: "Calendar View"
      map: "Map View"
      coming_soon: "Coming Soon"

  # Common actions
  common:
    actions:
      toggle: "Toggle"
