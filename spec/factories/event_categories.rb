FactoryBot.define do
  factory :event_category do
    sequence(:name) { |n| "Category #{n}" }
    sequence(:slug) { |n| "category-#{n}" }
    description { "A sample event category description" }
    color { "#3B82F6" }
    icon { "calendar" }

    trait :technology do
      name { "Technology" }
      slug { "technology" }
      description { "Technology and programming related events" }
      color { "#10B981" }
      icon { "code" }
    end

    trait :business do
      name { "Business" }
      slug { "business" }
      description { "Business and entrepreneurship events" }
      color { "#F59E0B" }
      icon { "briefcase" }
    end

    trait :art do
      name { "Art & Design" }
      slug { "art-design" }
      description { "Creative arts and design workshops" }
      color { "#EF4444" }
      icon { "palette" }
    end

    trait :with_events do
      after(:create) do |category|
        create_list(:event, 3, event_category: category)
      end
    end
  end
end
