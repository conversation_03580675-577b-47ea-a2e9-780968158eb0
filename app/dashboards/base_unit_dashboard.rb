require "administrate/base_dashboard"

class BaseUnitDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    name: Field::String,
    user: Field::BelongsTo,
    knowledge_base: Field::BelongsTo,
    posts: Field::<PERSON><PERSON><PERSON>,
    file_attachment: Field::<PERSON><PERSON><PERSON>,
    file_blob: Field::<PERSON><PERSON><PERSON>,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    name
    user
    knowledge_base
    posts
    created_at
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    name
    user
    knowledge_base
    posts
    file_attachment
    file_blob
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    name
    user
    knowledge_base
  ].freeze

  COLLECTION_FILTERS = {}.freeze

  def display_resource(base_unit)
    "#{base_unit.name} (#{base_unit.user&.username})"
  end
end