require 'rails_helper'

RSpec.describe Event, type: :model do
  let(:user) { create(:user) }
  let(:category) { create(:event_category) }
  let(:location) { create(:event_location) }

  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:event_category).optional }
    it { should belong_to(:event_location).optional }
    it { should have_many(:event_registrations).dependent(:destroy) }
    it { should have_many(:registered_users).through(:event_registrations) }
    it { should have_many(:event_instructors).dependent(:destroy) }
    it { should have_many(:instructors).through(:event_instructors) }
    it { should have_one_attached(:cover_image) }
    it { should have_many_attached(:documents) }
  end

  describe 'validations' do
    subject { build(:event, user: user) }

    it { should validate_presence_of(:title) }
    it { should validate_presence_of(:start_time) }
    it { should validate_presence_of(:end_time) }
    it { should validate_presence_of(:visibility) }

    it { should validate_length_of(:title).is_at_most(255) }
    it { should validate_numericality_of(:price).is_greater_than_or_equal_to(0) }

    describe 'end_time_after_start_time' do
      it 'is invalid when end_time is before start_time' do
        event = build(:event,
                     start_time: 1.hour.from_now,
                     end_time: 30.minutes.from_now,
                     user: user)
        expect(event).not_to be_valid
        expect(event.errors[:end_time]).to include('must be after start time')
      end

      it 'is valid when end_time is after start_time' do
        event = build(:event,
                     start_time: 1.hour.from_now,
                     end_time: 2.hours.from_now,
                     user: user)
        expect(event).to be_valid
      end
    end

    describe 'meeting_link_present_if_online' do
      it 'is invalid when online event has no meeting link' do
        event = build(:event, is_online: true, meeting_link: nil, user: user)
        expect(event).not_to be_valid
        expect(event.errors[:meeting_link]).to include('is required for online events')
      end

      it 'is valid when online event has meeting link' do
        event = build(:event,
                     is_online: true,
                     meeting_link: 'https://zoom.us/j/123456789',
                     user: user)
        expect(event).to be_valid
      end

      it 'is valid when offline event has no meeting link' do
        event = build(:event, is_online: false, meeting_link: nil, user: user)
        expect(event).to be_valid
      end
    end
  end

  describe 'enums' do
    it 'defines status enum with correct values' do
      expect(Event.statuses.keys).to match_array(['draft', 'published', 'cancelled', 'completed'])
    end

    it 'defines difficulty_level enum with correct values' do
      expect(Event.difficulty_levels.keys).to match_array(['beginner', 'intermediate', 'advanced'])
    end
  end

  describe 'scopes' do
    let!(:upcoming_event) { create(:event, :upcoming, :published, user: user) }
    let!(:past_event) { create(:event, :past, user: user) }
    let!(:draft_event) { create(:event, :draft, user: user) }

    describe '.upcoming' do
      it 'returns only upcoming events' do
        expect(Event.upcoming).to include(upcoming_event)
        expect(Event.upcoming).not_to include(past_event)
      end
    end

    describe '.published' do
      it 'returns only published events' do
        expect(Event.published).to include(upcoming_event)
        expect(Event.published).not_to include(draft_event)
      end
    end
  end

  describe 'instance methods' do
    let(:event) { create(:event, max_participants: 10, user: user) }

    describe '#available_spots' do
      context 'when max_participants is set' do
        it 'returns remaining spots' do
          create_list(:event_registration, 3, event: event)
          expect(event.available_spots).to eq(7)
        end
      end

      context 'when max_participants is nil' do
        it 'returns nil' do
          event.update(max_participants: nil)
          expect(event.available_spots).to be_nil
        end
      end
    end

    describe '#full?' do
      it 'returns true when event is full' do
        create_list(:event_registration, 10, event: event)
        expect(event.full?).to be true
      end

      it 'returns false when event has spots' do
        create_list(:event_registration, 5, event: event)
        expect(event.full?).to be false
      end
    end


  end
end
