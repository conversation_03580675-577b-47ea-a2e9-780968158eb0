class CreatePosts < ActiveRecord::Migration[7.0]
  def change
    create_table :posts do |t|
      t.references :user, null: false, foreign_key: true
      t.references :base_unit, null: true, foreign_key: true
      t.string :title, null: false
      t.string :status, default: 'draft'
      t.datetime :published_at

      t.timestamps
    end

    add_index :posts, :status
    add_index :posts, :published_at
  end
end
