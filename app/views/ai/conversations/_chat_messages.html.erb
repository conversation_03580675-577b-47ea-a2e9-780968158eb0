<div id="conversation_messages" class="px-4 py-8 space-y-4">
  <%= turbo_stream_from "conversation_#{@conversation.id}" %>

  <%# Check if there are any messages *other than* system messages (which are hidden) %>
  <% visible_messages = @conversation.messages.where.not(role: :system) %>
  <% if visible_messages.empty? %>
    <%# Empty state: Show prompt and potentially assistant instructions %>
    <div class="flex flex-col items-center justify-center h-full text-center text-gray-500 dark:text-gray-400 pt-10 pb-20">
      <svg class="w-10 h-10 mb-3 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
      </svg>
      <p class="text-lg font-medium mb-1"><%= t('ai.conversations.new') %></p>

      <%# Updated prompt text with assistant name %>
      <% assistant = @conversation.assistant %>
      <p class="text-sm max-w-md">
        <%= t('ai.conversations.empty_state') %> <%= assistant ? assistant.name : "AI Assistant" %>
      </p>

      <%# Display assistant instructions if available %>
      <% if assistant && assistant.instructions.present? %>
        <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 w-full max-w-md">
          <h4 class="text-sm font-semibold mb-2 text-gray-700 dark:text-gray-300"><%= t('ai.assistants.fields.instructions') %>:</h4>
          <div class="text-xs text-gray-600 dark:text-gray-400 text-left bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md max-h-40 overflow-y-auto">
            <%= simple_format(assistant.instructions) %>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <%# Display messages (system messages are skipped by the _message partial) %>
    <% @conversation.messages.ordered.each do |message| %>
      <%= render "ai/messages/message", message: message %>
    <% end %>
  <% end %>

  <div class="h-4 flex-none">
    <!-- 底部间隔，防止消息被输入框遮挡 -->
  </div>
</div>
