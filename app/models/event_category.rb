class EventCategory < ApplicationRecord
  # 关联关系
  has_many :events, dependent: :destroy

  # 验证规则
  validates :name, presence: true, length: { maximum: 255 }
  validates :slug, presence: true, uniqueness: true, length: { maximum: 255 }

  # 回调函数
  before_validation :generate_slug, if: -> { name.present? && slug.blank? && !@skip_slug_generation }

  # 作用域
  scope :active, -> { joins(:events).where(events: { archived: false }).distinct }

  # 自定义 slug= 方法来跟踪手动设置
  def slug=(value)
    @slug_manually_set = true if value.present?
    super(value)
  end

  # 实例方法
  def to_s
    name
  end

  # 允许测试时跳过自动生成 slug（用于测试验证）
  def skip_slug_generation!
    @skip_slug_generation = true
  end

  private

  def generate_slug
    return if name.blank?

    # 使用 parameterize 方法，它可以处理 Unicode 字符
    # 如果 parameterize 结果为空（比如全是 Unicode 字符），使用 transliterate
    parameterized = name.parameterize
    if parameterized.blank?
      # 对于纯 Unicode 字符，使用 transliterate 然后 parameterize
      transliterated = I18n.transliterate(name.to_s)
      parameterized = transliterated.parameterize
    end

    self.slug = parameterized.presence || "category-#{SecureRandom.hex(4)}"
  end
end
