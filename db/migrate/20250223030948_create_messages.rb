class CreateMessages < ActiveRecord::Migration[8.0]
  def change
    create_table :messages do |t|
      t.references :messageable, polymorphic: true, null: false
      t.string :role, null: false
      t.text :content
      t.json :tool_calls
      t.string :tool_call_id
      t.timestamps
    end

    # 添加复合索引支持按角色和创建时间查询
    add_index :messages, [ :messageable_type, :messageable_id, :role, :created_at ]

    # 添加工具调用相关索引
    add_index :messages, :tool_call_id

    # 添加创建时间索引，支持消息排序和时间范围查询
    add_index :messages, :created_at
  end
end
