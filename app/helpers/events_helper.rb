module Events<PERSON>elper
  def registration_status_class(status)
    case status
    when "confirmed"
      "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    when "pending"
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    when "cancelled"
      "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    when "attended"
      "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    else
      "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    end
  end

  def event_status_badge(event)
    case event.status
    when "draft"
      content_tag :span, "Draft", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
    when "published"
      content_tag :span, "Published", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
    when "cancelled"
      content_tag :span, "Cancelled", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
    when "completed"
      content_tag :span, "Completed", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
    else
      content_tag :span, event.status.humanize, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
    end
  end

  def event_price_display(event)
    return "Free" if event.price.zero?
    "¥#{event.price}"
  end

  def event_spots_remaining(event)
    return "Unlimited spots" unless event.max_participants

    remaining = event.available_spots

    if remaining.zero?
      "Event is full"
    elsif remaining == 1
      "1 spot remaining"
    else
      "#{remaining} spots remaining"
    end
  end

  def event_time_display(event)
    if event.start_time.to_date == event.end_time.to_date
      # Same day event
      "#{event.start_time.strftime('%B %d, %Y')} • #{event.start_time.strftime('%l:%M %p')} - #{event.end_time.strftime('%l:%M %p')}"
    else
      # Multi-day event
      "#{event.start_time.strftime('%B %d, %Y')} - #{event.end_time.strftime('%B %d, %Y')}"
    end
  end

  def event_registration_button(event)
    if !authenticated?
      link_to "Sign in to register", new_session_path, class: "btn btn-outline-primary"
    elsif current_user.event_registrations.where(event: event).where.not(status: "cancelled").exists?
      content_tag :span, "Already registered", class: "btn btn-success disabled"
    elsif allowed_to?(:register?, event)
      link_to "Register", new_event_registration_path(event), class: "btn btn-primary"
    else
      content_tag :span, "Registration not available", class: "btn btn-secondary disabled"
    end
  end

  def authenticated?
    current_user.present?
  end

  def event_difficulty_badge(event)
    case event.difficulty_level
    when "beginner"
      content_tag :span, "Beginner", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
    when "intermediate"
      content_tag :span, "Intermediate", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
    when "advanced"
      content_tag :span, "Advanced", class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
    else
      content_tag :span, event.difficulty_level.humanize, class: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
    end
  end

  # 日历视图的事件颜色类
  def event_calendar_color_class(event)
    return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200" unless event.event_category

    case event.event_category.id % 6
    when 0
      "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    when 1
      "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    when 2
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
    when 3
      "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    when 4
      "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    when 5
      "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200"
    else
      "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
    end
  end

  # 事件分类的颜色点
  def event_category_color_class(category)
    return "bg-gray-400" unless category

    case category.id % 6
    when 0
      "bg-blue-500"
    when 1
      "bg-green-500"
    when 2
      "bg-yellow-500"
    when 3
      "bg-red-500"
    when 4
      "bg-purple-500"
    when 5
      "bg-pink-500"
    else
      "bg-gray-500"
    end
  end
end
