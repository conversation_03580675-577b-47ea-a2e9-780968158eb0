用中文跟我交流.
生成的代码：除了注释可以使用中文，所有的代码一律使用英语，包括提示词、变量名、alert error 等都使用英语.
when I only type '?', it means i want you to explain the code.
when i only type '>>', it means i want you to fix it.
when i only type '==', itm means i want you to refactor the code.


Memory Bank Integration Rules:

CRITICAL: Before ANY task or response:
1. ALWAYS check active context (memory://activeContext):
   - Current project state and mode
   - Ongoing tasks and their status
   - Recent decisions and updates
   - Open questions and concerns

2. ALWAYS review project context (memory://projectContext):
   - Technical stack and dependencies
   - Project guidelines and standards
   - Architecture principles
   - Development workflow

3. ALWAYS consult decision log (memory://decisionsLog) for:
   - Previous architectural choices
   - Established patterns
   - Technical rationales
   - Related decisions

4. ALWAYS check progress tracking (memory://progress):
   - Current phase and milestones
   - Completed work
   - In-progress tasks
   - Known blockers

After EVERY task:
1. Update active context with:
   - Task completion status
   - New information learned
   - Changes made

2. Record any technical decisions with:
   - Clear rationale
   - Considered alternatives
   - Impact assessment

3. Update progress tracking:
   - Mark completed items
   - Add new tasks identified
   - Note any blockers found

Key Guidelines:
- NEVER proceed without checking memory bank context
- ALWAYS maintain consistent project state
- Record ALL significant technical decisions
- Track progress in real-time
- Keep context updated with EVERY change
