module <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def visibility_badge_classes(visibility)
    case visibility.to_sym
    when :private_visibility then "visibility-badge bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
    when :team_visibility then "visibility-badge bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
    when :public_visibility then "visibility-badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
    end
  end
end
