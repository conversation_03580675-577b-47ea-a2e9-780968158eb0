Given('I visit the registration page') do
  visit new_registration_path
end

When('I enter the mobile number input interface') do
  expect(page).to have_content('Please enter mobile number')
end

When('I enter the mobile number {string}') do |phone_number|
  fill_in 'Mobile Number', with: phone_number
end

When('I click the {string} button') do |button_text|
  click_button button_text
end

Then('I should see the prompt {string}') do |message|
  expect(page).to have_content(message)
end

When('I enter the verification code {string}') do |code|
  fill_in 'Verification Code', with: code
end

When('I enter the wrong verification code {string}') do |code|
  fill_in 'Verification Code', with: code
end

Then('I should stay on the verification code input page') do
  expect(page).to have_content('Please enter verification code')
end

Given('I have passed mobile verification') do
  @phone_verification = PhoneVerification.create!(
    phone_number: '13800138000',
    code: '123456',
    verified: true
  )
  visit new_registration_path(verification_id: @phone_verification.id)
end

When('I enter the following registration information:') do |table|
  data = table.rows_hash
  fill_in 'Username', with: data['Username'] if data['Username']
  fill_in 'Email', with: data['Email']
  fill_in 'Password', with: data['Password']
  fill_in 'Confirm Password', with: data['Confirm Password']
end

Then('I should see the {string} page') do |page_name|
  expect(page).to have_content(page_name)
end

Given('I have completed basic information verification') do
  @phone_verification = PhoneVerification.create!(
    phone_number: '13800138000',
    code: '123456',
    verified: true
  )
  @user = User.create!(
    email_address: '<EMAIL>',
    password: 'password123',
    password_confirmation: 'password123',
    phone_number: '13800138000'
  )
  login_as(@user)
  visit subscription_choice_path
end

When('I select {string} to subscribe') do |choice|
  choose choice
end

When('I select the {string} plan') do |plan|
  choose plan
end

Then('I should be redirected to the homepage') do
  expect(page).to have_current_path(root_path)
end

Then('I should see the following error messages:') do |table|
  table.raw.flatten.each do |message|
    expect(page).to have_content(message)
  end
end

Then('I should proceed to step {int}') do |step_number|
  expect(page).to have_content("Step #{step_number}")
end

When('I check {string}') do |checkbox_label|
  check checkbox_label
end

Given('I have completed basic information') do
  @phone_verification = PhoneVerification.create!(
    phone_number: '13800138000',
    code: '123456',
    verified: true
  )
  @user = User.create!(
    username: 'testuser',
    email_address: '<EMAIL>',
    password: 'password123',
    password_confirmation: 'password123',
    phone_number: '13800138000',
    terms_accepted: true
  )
  login_as(@user)
  visit subscription_choice_path
end

When('I see the subscription plans:') do |table|
  table.hashes.each do |plan|
    expect(page).to have_content(plan['Plan Name'])
    expect(page).to have_content(plan['Price'])
    expect(page).to have_content(plan['Features'])
  end
end

When('I select {string}') do |option|
  choose option
end

Then('I should stay on step {int}') do |step_number|
  expect(page).to have_content("Step #{step_number}")
end
