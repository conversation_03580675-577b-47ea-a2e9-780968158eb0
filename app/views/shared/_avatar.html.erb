<%# Renders a user avatar image tag, falling back to a placeholder. %>
<%# %>
<%# Locals: %>
<%#   user:      The user object. Can be nil. %>
<%#   size:      (Integer) Tailwind size unit (e.g., 8 for w-8 h-8). Default: 8. %>
<%#   alt_text:  (String) Alt text for the image. Default: Translated 'User avatar'. %>
<%#   css_class: (String) Additional CSS classes. Default: 'rounded-full'. %>
<%#   placeholder_url: (String) URL for the placeholder image. %>
<%# %>
<% user ||= nil %>
<% size ||= 8 # Corresponds to w-8 h-8 (32px) %>
<% alt_text ||= t('shared.avatar.alt_text', default: 'User avatar') %>
<% css_class ||= "rounded-full" %>
<% placeholder_url ||= "https://api.dicebear.com/7.x/avataaars/svg?seed=#{user&.id || 'default'}" %>
<%# Assuming Tailwind w-X corresponds to X*0.25rem, and 1rem = 16px %>
<% image_size_px = size * 4 %>

<% if user&.avatar&.attached? %>
  <%# Generate a variant for performance %>
  <%= image_tag user.avatar.variant(resize_to_limit: [image_size_px, image_size_px]),
                  class: "w-#{size} h-#{size} #{css_class}",
                  alt: alt_text %>
<% else %>
  <%# Display placeholder if user is nil or avatar is not attached %>
  <%= image_tag placeholder_url,
                  class: "w-#{size} h-#{size} #{css_class}",
                  alt: alt_text %>
<% end %>
