<%#
# Collection

This partial is used on the `index` and `show` pages
to display a collection of resources in an HTML table.

## Local variables:

- `collection_presenter`:
  An instance of [Administrate::Page::Collection][1].
  The table presenter uses `ResourceDashboard::COLLECTION_ATTRIBUTES` to determine
  the columns displayed in the table
- `resources`:
  An ActiveModel::Relation collection of resources to be displayed in the table.
  By default, the number of resources is limited by pagination
  or by a hard limit to prevent excessive page load times

[1]: http://www.rubydoc.info/gems/administrate/Administrate/Page/Collection
%>

<table aria-labelledby="<%= table_title %>" data-controller="table" data-action="click->table#visitDataUrl keydown->table#visitDataUrl">
  <thead>
    <tr>
      <% collection_presenter.attribute_types.each do |attr_name, attr_type| %>
        <th class="cell-label
        cell-label--<%= attr_type.html_class %>
        cell-label--<%= collection_presenter.ordered_html_class(attr_name) %>
        cell-label--<%= "#{collection_presenter.resource_name}_#{attr_name}" %>"
        scope="col"
        aria-sort="<%= sort_order(collection_presenter.ordered_html_class(attr_name)) %>">
        <%= link_to(sanitized_order_params(page, collection_field_name).merge(
          collection_presenter.order_params_for(attr_name, key: collection_field_name)
        )) do %>
        <%= t(
          "helpers.label.#{collection_presenter.resource_name}.#{attr_name}",
          default: resource_class.human_attribute_name(attr_name).titleize,
        ) %>
            <% if collection_presenter.ordered_by?(attr_name) %>
              <span class="cell-label__sort-indicator cell-label__sort-indicator--<%= collection_presenter.ordered_html_class(attr_name) %>">
                <svg aria-hidden="true">
                  <use xlink:href="#icon-up-caret" />
                </svg>
              </span>
            <% end %>
          <% end %>
        </th>
      <% end %>
      <%= render(
        "collection_header_actions",
        collection_presenter: collection_presenter,
        page: page,
        resources: resources,
        table_title: "page-title"
      ) %>
    </tr>
  </thead>

  <tbody>
    <% resources.each do |resource| %>
      <tr class="js-table-row"
          <% if accessible_action?(resource, :show) %>
            <%= %(tabindex=0 role=link data-url=#{polymorphic_path([namespace, resource])}) %>
          <% end %>
          >
        <% collection_presenter.attributes_for(resource).each do |attribute| %>
          <td class="cell-data cell-data--<%= attribute.html_class %>">
            <% if accessible_action?(resource, :show) -%>
              <a href="<%= polymorphic_path([namespace, resource]) -%>"
                 tabindex="-1"
                 class="action-show"
                 >
                <%= render_field attribute %>
              </a>
            <% else %>
              <%= render_field attribute %>
            <% end -%>
          </td>
        <% end %>

        <%= render(
          "collection_item_actions",
          collection_presenter: collection_presenter,
          collection_field_name: collection_field_name,
          page: page,
          namespace: namespace,
          resource: resource,
          table_title: "page-title"
        ) %>
      </tr>
    <% end %>
  </tbody>
</table>
