class CreateEventInstructors < ActiveRecord::Migration[8.0]
  def change
    create_table :event_instructors do |t|
      t.references :event, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :role, default: 'instructor'
      t.text :bio
      t.string :title

      t.timestamps
    end

    add_index :event_instructors, [ :event_id, :user_id ], unique: true
    add_index :event_instructors, :role
  end
end
