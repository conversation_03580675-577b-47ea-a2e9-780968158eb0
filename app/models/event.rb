class Event < ApplicationRecord
  # 复用现有的 Concerns
  include Publishable    # 发布状态管理
  include Archivable     # 归档功能
  include Cacheable      # 缓存策略
  include Visibility     # 可见性控制（使用现有的 concern）
  include SqliteSearch   # FTS5 全文搜索功能

  # 配置 FTS5 搜索
  search_scope :title, :description, :prerequisites, :what_to_bring, :what_included
  search_tokenizer "unicode61"  # 使用 unicode61 分词器支持中文

  # 基础关联
  belongs_to :user
  belongs_to :event_category, optional: true
  belongs_to :event_location, optional: true

  # 报名和教师关联
  has_many :event_registrations, dependent: :destroy
  has_many :registered_users, -> { where(event_registrations: { status: [ "pending", "confirmed", "attended" ] }) }, through: :event_registrations, source: :user
  has_many :confirmed_users, -> { where(event_registrations: { status: [ "confirmed", "attended" ] }) }, through: :event_registrations, source: :user
  has_many :event_instructors, dependent: :destroy
  has_many :instructors, through: :event_instructors, source: :user

  # 团队资源访问控制（复用现有系统）
  has_many :team_resource_accesses, as: :resource, dependent: :destroy
  has_many :team_members, through: :team_resource_accesses
  has_many :accessible_teams, through: :team_members, source: :team

  # 文件附件（利用现有 Active Storage）
  has_one_attached :cover_image
  has_many_attached :documents

  # 枚举值定义 - Rails 8 语法
  enum :status, {
    draft: "draft",
    published: "published",
    cancelled: "cancelled",
    completed: "completed"
  }

  # visibility enum 已在 Visibility concern 中定义，无需重复定义

  enum :difficulty_level, {
    beginner: "beginner",
    intermediate: "intermediate",
    advanced: "advanced"
  }

  # 作用域
  scope :upcoming, -> { where("start_time > ?", Time.current) }
  scope :past, -> { where("end_time < ?", Time.current) }
  scope :published, -> { where(status: "published") }
  scope :visible_to_public, -> { where(visibility: :public_visibility) }  # 使用 Visibility concern 的枚举值
  scope :in_city, ->(city) { joins(:event_location).where(event_locations: { city: city }) }
  scope :by_category, ->(category_id) { where(event_category_id: category_id) }
  scope :accessible_by_user, ->(user) {
    return visible_to_public unless user

    left_joins(team_resource_accesses: { team_member: :team })
      .where(
        "events.visibility = ? OR events.user_id = ? OR teams.id IN (?)",
        Event.visibilities[:public_visibility], user.id, user.team_ids
      ).distinct
  }

  # 为了向后兼容，将现有的搜索方法别名到新的方法
  # 这样现有的代码不需要修改
  class << self
    alias_method :fts_search, :full_search_count      # 使用专门的计数版本，避免 rank 问题
    alias_method :fts_search_with_relevance, :full_search_with_rank  # 带相关性评分的版本
    alias_method :phrase_search_count, :phrase_search
    alias_method :search_in_field_count, :search_in_field
    alias_method :advanced_search_count, :advanced_search
  end

  # 验证规则
  validates :title, presence: true, length: { maximum: 255 }
  validates :start_time, :end_time, presence: true
  validates :price, numericality: { greater_than_or_equal_to: 0 }
  validates :max_participants, numericality: { greater_than: 0 }, allow_nil: true
  validates :visibility, presence: true
  validate :end_time_after_start_time
  validate :meeting_link_present_if_online
  validate :cover_image_format, if: -> { cover_image.attached? }

  # 实例方法

  def available_spots
    return nil unless max_participants
    max_participants - confirmed_users.count
  end

  def full?
    max_participants && confirmed_users.count >= max_participants
  end

  private

  def end_time_after_start_time
    return unless start_time && end_time
    errors.add(:end_time, "must be after start time") if end_time <= start_time
  end

  def meeting_link_present_if_online
    errors.add(:meeting_link, "is required for online events") if is_online? && meeting_link.blank?
  end

  def cover_image_format
    return unless cover_image.attached?

    unless cover_image.content_type.in?([ "image/png", "image/jpg", "image/jpeg" ])
      errors.add(:cover_image, "must be a PNG, JPG, or JPEG")
    end

    if cover_image.byte_size > 5.megabytes
      errors.add(:cover_image, "must be less than 5MB")
    end
  end
end
