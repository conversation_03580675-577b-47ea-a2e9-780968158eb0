class EventLocation < ApplicationRecord
  # 关联关系
  has_many :events, dependent: :destroy

  # 验证规则
  validates :name, presence: true, length: { maximum: 255 }
  validates :latitude, numericality: { in: -90.0..90.0 }, allow_nil: true
  validates :longitude, numericality: { in: -180.0..180.0 }, allow_nil: true

  # 作用域
  scope :by_city, ->(city) { where(city: city) }
  scope :by_country, ->(country) { where(country: country) }
  scope :with_coordinates, -> { where.not(latitude: nil, longitude: nil) }

  # 地理位置搜索作用域
  scope :within_radius, ->(lat, lng, radius_km) {
    where(
      "6371 * acos(
        cos(radians(?)) * cos(radians(latitude)) *
        cos(radians(longitude) - radians(?)) +
        sin(radians(?)) * sin(radians(latitude))
      ) <= ?",
      lat, lng, lat, radius_km
    )
  }

  scope :within_bounds, ->(north, south, east, west) {
    where(
      latitude: south..north,
      longitude: west..east
    )
  }

  # 实例方法
  def full_address
    [ address, city, province, country ].filter(&:present?).join(", ")
  end

  def has_coordinates?
    latitude.present? && longitude.present?
  end

  def to_coordinates
    return nil unless has_coordinates?
    [ latitude.to_f, longitude.to_f ]
  end

  # 计算到指定点的距离（公里）
  def distance_to(lat, lng)
    return nil unless latitude && longitude

    rad_per_deg = Math::PI / 180
    rkm = 6371

    dlat_rad = (lat - latitude) * rad_per_deg
    dlon_rad = (lng - longitude) * rad_per_deg

    lat1_rad = latitude * rad_per_deg
    lat2_rad = lat * rad_per_deg

    a = Math.sin(dlat_rad/2)**2 + Math.cos(lat1_rad) * Math.cos(lat2_rad) * Math.sin(dlon_rad/2)**2
    c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))

    rkm * c
  end

  # 地理编码（可选，需要集成地理编码服务）
  def geocode_address
    nil if address.blank?

    # 这里可以集成 Geocoder gem 或其他地理编码服务
    # geocode_result = Geocoder.search(full_address)
    # if geocode_result.any?
    #   self.latitude = geocode_result.first.latitude
    #   self.longitude = geocode_result.first.longitude
    # end
  end

  # 类方法：搜索建议
  def self.search_suggestions(query, limit: 10)
    return [] if query.blank? || query.length < 2

    # 使用 SQLite 兼容的 LIKE 语法 (不区分大小写)
    where("name LIKE ? OR city LIKE ? OR address LIKE ?",
          "%#{query}%", "%#{query}%", "%#{query}%")
      .limit(limit)
      .map do |location|
        {
          id: location.id,
          name: location.name,
          city: location.city,
          full_address: location.full_address,
          latitude: location.latitude,
          longitude: location.longitude
        }
      end
  end
end
