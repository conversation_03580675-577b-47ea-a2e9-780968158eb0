require 'rails_helper'

RSpec.describe Team, type: :model do
  describe 'validations' do
    subject { build(:team) }

    it { should validate_presence_of(:name) }
    it { should validate_uniqueness_of(:name).case_insensitive }
    it { should validate_inclusion_of(:status).in_array(Team::STATUSES) }
  end

  describe 'associations' do
    it { should have_many(:team_members).dependent(:destroy) }
    it { should have_many(:members).through(:team_members).source(:user) }
    it { should have_many(:team_resource_accesses).through(:team_members) }
    it { should have_many(:knowledge_bases).through(:team_resource_accesses).source(:resource) }
  end

  describe 'scopes' do
    let!(:active_team) { create(:team, status: 'active') }
    let!(:inactive_team) { create(:team, :inactive) }

    describe '.active' do
      it { expect(described_class.active).to include(active_team) }
      it { expect(described_class.active).not_to include(inactive_team) }
    end

    describe '.inactive' do
      it { expect(described_class.inactive).to include(inactive_team) }
      it { expect(described_class.inactive).not_to include(active_team) }
    end
  end

  describe 'instance methods' do
    let(:team) { create(:team) }
    let(:user) { create(:user) }
    let(:admin_user) { create(:user) }
    let!(:admin_member) { create(:team_member, :admin, team: team, user: admin_user) }
    let!(:regular_member) { create(:team_member, team: team, user: user) }

    describe '#admins' do
      it 'returns admin users' do
        expect(team.admins).to include(admin_user)
        expect(team.admins).not_to include(user)
      end
    end

    describe '#admin?' do
      it 'returns true for admin user' do
        expect(team.admin?(admin_user)).to be true
      end

      it 'returns false for regular member' do
        expect(team.admin?(user)).to be false
      end
    end

    describe '#member?' do
      it 'returns true for team member' do
        expect(team.member?(user)).to be true
      end

      it 'returns false for non-member' do
        non_member = create(:user)
        expect(team.member?(non_member)).to be false
      end
    end

    describe '#active?' do
      subject { team.active? }

      context 'when active' do
        let(:team) { create(:team, status: 'active') }
        it { is_expected.to be true }
      end

      context 'when inactive' do
        let(:team) { create(:team, :inactive) }
        it { is_expected.to be false }
      end
    end
  end
end
