# Gemini Project Conventions

This document outlines the conventions and guidelines for working on this Rails 8 project with Gemini.

## 1. Core Technologies

- **Ruby:** 3.2.x
- **Rails:** 8.0.1
- **Database:** SQLite3
- **Web Server:** Puma
- **JavaScript:** Importmap, Hotwire (Turbo/Stimulus)
- **CSS:** Tailwind CSS v4.2
- **Deployment:** Kamal

## 2. Development Workflow

### 2.1. Getting Started

1.  Run `bundle install` to install dependencies.
2.  Run `rails db:migrate` to set up the database.
3.  Run `rails dev` to start the development server.

### 2.2. Code Style

- **Ruby:** Follow the [Rails community style guide](https://rails.rubystyle.guide/). We use `rubocop-rails-omakase` for linting.
- **JavaScript:** Follow the [Stimulus style guide](https://stimulus.hotwired.dev/handbook/best-practices).
- **CSS:** Follow the [Tailwind CSS best practices](https://tailwindcss.com/docs/utility-first).

### 2.3. Testing

- **Frameworks:** RSpec and Cucumber.
- **Unit Tests:** Use RSpec for model and service tests.
- **Feature Tests:** Use Cucumber for integration and UI tests.
- **Factories:** Use Factory Bot for creating test data.

## 3. Application Architecture

### 3.1. Authentication

- **Local:** `has_secure_password`
- **Third-Party:** OmniAuth (WeChat, Google, GitHub)

### 3.2. Authorization

- **Framework:** Action Policy

### 3.3. AI/Langchain

- **Library:** `langchainrb_rails`
- **Vector Store:** Qdrant
- **LLM:** OpenAI

## 4. Commits and Pull Requests

- **Commits:** Write clear and concise commit messages.
- **Pull Requests:** Provide a detailed description of the changes.

## 5. Gemini's Role

As an AI assistant, I will adhere to these conventions when performing the following tasks:

- **Code Generation:** Generate code that follows the established style guides.
- **Refactoring:** Refactor code to improve readability and performance.
- **Bug Fixes:** Identify and fix bugs in the codebase.
- **Feature Implementation:** Implement new features according to the project's architecture.
