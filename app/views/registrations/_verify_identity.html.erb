<div class="flex flex-col items-center px-6 mx-auto">
    <div class="w-full max-w-md bg-white rounded-lg shadow-xs dark:border md:mt-0 dark:bg-gray-800 dark:border-gray-700">
        <div class="p-6 space-y-4 md:space-y-6">
            <h1 class="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">
                <%= t('auth.registrations.steps.verification.title') %>
            </h1>
            <%= form_with(url: registration_path,
                html: {
                    class: "space-y-4 md:space-y-6",
                    data: {
                        turbo_stream: true,
                        controller: "registration-steps",
                        registration_steps_target: "form"
                    }
                }) do |f| %>

                <div>
                    <%= f.label :phone, t('auth.registrations.steps.verification.phone_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
                    <%= f.telephone_field :phone,
                        id: "phone",
                        name: "phone",
                        class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
                        placeholder: t('auth.registrations.steps.verification.phone_placeholder'),
                        data: { registration_steps_target: "phoneInput" } %>
                </div>

                <div>
                    <%= f.label :verification_code, t('auth.registrations.steps.verification.code_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
                    <div class="flex space-x-2">
                        <%= f.text_field :verification_code,
                            class: "bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm rounded-lg focus:ring-primary-600 focus:border-primary-600 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
                            placeholder: t('auth.registrations.steps.verification.code_placeholder'),
                            data: { registration_steps_target: "verificationCodeInput" } %>
                        <button type="button"
                                data-registration-steps-target="verificationButton"
                                data-action="click->registration-steps#sendVerificationCode"
                                class="px-4 py-2 text-sm font-medium text-primary-600 bg-white border border-primary-600 rounded-lg hover:bg-primary-50 focus:ring-4 focus:outline-hidden focus:ring-primary-300">
                            <%= t('auth.registrations.steps.verification.send_code') %>
                        </button>
                    </div>
                </div>

                <%= f.hidden_field :step, value: "send_verification_code" %>

                <button type="button"
                        data-action="click->registration-steps#verifyCodeAndProceed"
                        class="w-full text-white bg-primary-500 hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800">
                  <%= t('auth.registrations.steps.verification.next') %>
                </button>
            <% end %>

            <!-- 分隔线和第三方注册选项 -->
            <div class="flex flex-col space-y-4 mt-6">
              <div class="relative">
                <div class="absolute inset-0 flex items-center">
                  <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                  <span class="px-2 bg-white text-gray-500 dark:bg-gray-800 dark:text-gray-400">
                    <%= t('auth.omniauth.or_continue_with') %>
                  </span>
                </div>
              </div>

              <div class="grid grid-cols-2 gap-3"> <%# Changed grid-cols-3 to grid-cols-2 %>
                <%# Removed params: { origin: 'registration' } %>
                <%= form_tag "/auth/github", method: :post, data: { turbo: false } do %>
                  <button type="submit" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600">
                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12.006 2a9.847 9.847 0 0 0-6.484 2.44 10.32 10.32 0 0 0-3.393 6.17 10.48 10.48 0 0 0 1.317 6.955 10.045 10.045 0 0 0 5.4 4.418c.504.095.683-.223.683-.494 0-.245-.01-1.052-.014-1.908-2.78.62-3.366-1.21-3.366-1.21a2.711 2.711 0 0 0-1.11-1.5c-.907-.637.07-.621.07-.621.317.044.62.163.885.346.266.183.487.426.647.71.135.253.318.476.538.655a2.079 2.079 0 0 0 2.37.196c.045-.52.27-1.006.635-1.37-2.219-.259-4.554-1.138-4.554-5.07a4.022 4.022 0 0 1 1.031-2.75 3.77 3.77 0 0 1 .096-2.713s.839-.275 2.749 1.05a9.26 9.26 0 0 1 5.004 0c1.906-1.325 2.74-1.05 2.74-1.05.37.858.406 1.828.101 2.713a4.017 4.017 0 0 1 1.029 2.75c0 3.939-2.339 4.805-4.564 5.058a2.471 2.471 0 0 1 .679 1.897c0 1.372-.012 2.477-.012 2.814 0 .272.18.592.687.492a10.05 10.05 0 0 0 5.388-4.421 10.473 10.473 0 0 0 1.313-6.948 10.32 10.32 0 0 0-3.39-6.165A9.847 9.847 0 0 0 12.007 2Z" clip-rule="evenodd"/>
                    </svg>
                  </button>
                <% end %>

                <%= form_tag "/auth/wechat", method: :post, data: { turbo: false } do %>
                  <button type="submit" class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600">
                    <svg class="w-6 h-6 text-green-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" height="24" fill="currentColor" viewBox="0 0 576 512">
                      <path fill="currentColor" d="M385.2 167.6c6.4 0 12.6.3 18.8 1.1C387.4 90.3 303.3 32 207.7 32 100.5 32 13 104.8 13 197.4c0 53.4 29.3 97.5 77.9 131.6l-19.3 58.6 68-34.1c24.4 4.8 43.8 9.7 68.2 9.7 6.2 0 12.1-.3 18.3-.8-4-12.9-6.2-26.6-6.2-40.8-.1-84.9 72.9-154 165.3-154zm-104.5-52.9c14.5 0 24.2 9.7 24.2 24.4 0 14.5-9.7 24.2-24.2 24.2-14.8 0-29.3-9.7-29.3-24.2.1-14.7 14.6-24.4 29.3-24.4zm-136.4 48.6c-14.5 0-29.3-9.7-29.3-24.2 0-14.8 14.8-24.4 29.3-24.4 14.8 0 24.4 9.7 24.4 24.4 0 14.6-9.6 24.2-24.4 24.2zM563 319.4c0-77.9-77.9-141.3-165.4-141.3-92.7 0-165.4 63.4-165.4 141.3S305 460.7 397.6 460.7c19.3 0 38.9-5.1 58.6-9.9l53.4 29.3-14.8-48.6C534 402.1 563 363.2 563 319.4zm-219.1-24.5c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.8 0 24.4 9.7 24.4 19.3 0 10-9.7 19.6-24.4 19.6zm107.1 0c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.5 0 24.4 9.7 24.4 19.3.1 10-9.9 19.6-24.4 19.6z"/>
                    </svg>
                  </button>
                <% end %>
              </div>
            </div>

            <p class="text-sm font-light text-gray-500 dark:text-gray-400 mt-4"> <%# Added mt-4 for spacing %>
                <%= t('auth.registrations.steps.verification.have_account') %> <%= link_to t('auth.sign_in'), new_session_path, class: "font-medium text-primary-600 hover:underline dark:text-primary-500", data: { turbo: false } %>
            </p>
        </div>
    </div>
</div>
</section>
