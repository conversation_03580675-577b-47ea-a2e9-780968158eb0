require 'rails_helper'

RSpec.describe Subscription, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:plan_type) }
    it { should validate_presence_of(:start_date) }
    it { should validate_presence_of(:status) }
  end

  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'enums' do
    it { should define_enum_for(:plan_type).with_values(weekly: 0, monthly: 1) }
    it { should define_enum_for(:status).with_values(active: 0, canceled: 1, expired: 2) }
  end

  describe 'callbacks' do
    describe '#set_end_date' do
      let(:start_date) { Time.current }

      context 'when plan_type is weekly' do
        it 'sets end_date to 1 week after start_date' do
          subscription = Subscription.create(
            user: create(:user),
            plan_type: :weekly,
            start_date: start_date,
            status: :active
          )
          expect(subscription.end_date).to be_within(1.second).of(start_date + 1.week)
        end
      end

      context 'when plan_type is monthly' do
        it 'sets end_date to 1 month after start_date' do
          subscription = Subscription.create(
            user: create(:user),
            plan_type: :monthly,
            start_date: start_date,
            status: :active
          )
          expect(subscription.end_date).to be_within(1.second).of(start_date + 1.month)
        end
      end
    end
  end

  describe 'scopes' do
    describe '.active' do
      it 'returns only active subscriptions' do
        active = create(:subscription, status: :active)
        create(:subscription, status: :canceled)
        create(:subscription, status: :expired)

        expect(Subscription.active).to eq([ active ])
      end
    end
  end
end
