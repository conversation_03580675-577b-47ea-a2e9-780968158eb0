# English translations for AI module
en:
  ai:
    common:
      loading: "Loading..."
      no_results: "No results found"
      copy: "Copy"
      copied: "Copied"
      unknown_type: "Unknown message type"
      error: "Error"
      retry: "Retry"
    assistants:
      processing_message: "Processing tool calls..." # Added for constant
      title: "My Assistants"
      new: "New Assistant"
      edit: "Edit Assistant"
      delete: "Delete Assistant"
      back_to_assistants: "Back to Assistants"
      no_assistants_found: "No assistants found. Create one!"
      form:
        name_label: "Name"
        name_placeholder: "Enter assistant name"
        instructions_label: "Instructions"
        instructions_placeholder: "Enter assistant instructions... (Enter / Use Command)"
        model_label: "AI Model"
        tool_usage_mode: "Tool Usage Mode"
        tool_choices:
          auto: "Auto - Smart tool usage based on context"
          none: "None - Pure conversation without tools"
          any: "Any - Full access to all available tools"
        available_tools: "Available Tools"
        select_all: "Select All Tools"
        save_button: "Save Assistant"
        create_button: "Create Assistant"
        update_button: "Update Assistant"
        cancel_button: "Cancel"
      show:
        title: "Assistant Details"
        info:
          title: "Assistant Information"
          name: "Name"
          instructions: "Instructions"
          no_instructions: "No instructions provided"
          model: "Model"
          tool_choice: "Tool Choice"
          available_tools: "Available Tools"
          no_tools: "No tools configured for this assistant"
        time_info:
          title: "Time Information"
          conversations_count: "Conversations Count"
          messages_count: "Messages Count"
        actions:
          start_chat: "Start Chat"
        delete_confirm: "Are you sure you want to delete this assistant?"
      index:
        title: "My Assistants"
        new_button: "New Assistant"
        table_headers:
          name: "Name"
          model: "Model"
          tools: "Tools"
          conversations: "Chats"
          actions: "Actions"
        actions:
          chat: "Chat"
          edit: "Edit"
          delete: "Delete"
      fields:
        name: "Name"
        instructions: "Instructions"
        models: "AI Models"
        tools: "Available Tools"
      tools:
        web_search: "Web Search"
        knowledge_search: "Knowledge Search"
        file_upload: "File Upload"
      actions:
        create: "Create Assistant"
        update: "Update Assistant"
        delete_confirm: "Are you sure you want to delete this assistant?"
      tips:
        title: "Configuration Tips"
        basic:
          title: "Basic Configuration"
          name: "Choose a descriptive name for your assistant"
          instructions: "Define specific rules and preferences"
        prompts:
          title: "Using Prompts"
          select: "Type / in instructions to access prompt templates"
          combine: "Mix multiple prompts for enhanced capabilities"
          customize: "Modify prompt templates to fit your needs"
        tools:
          title: "Tool Selection"
          quick_setup: "Use the toggle switch to select/deselect all tools"
          custom: "Choose specific tools based on your needs"
          management: "Each tool provides unique capabilities"
        modes:
          title: "Tool Usage Modes"
          auto: "Assistant intelligently chooses appropriate tools"
          none: "Pure conversation without tool usage"
          any: "Assistant can freely use any selected tools"
        practices:
          title: "Best Practices"
          items:
            - "Select appropriate prompts for specialized capabilities"
            - "Start with essential tools and add more as needed"
            - "Use Auto mode for balanced tool usage"
            - "Provide clear instructions for optimal results"
    conversations:
      title: "AI Chat"
      new: "New Conversation"
      clear: "Clear Conversation"
      clear_confirm: "Are you sure you want to clear this conversation?"
      placeholder: "Type / for commands \nEnter to send, Shift + Enter for new line"
      send: "Send"
      stop_generating: "Stop Generating"
      regenerate: "Regenerate Response"
      loading: "AI is thinking..."
      error: "Error processing your request. Please try again."
      retry: "Retry"
      empty_state: "Start a new conversation by selecting an assistant and sending a message."
      chat:
        title: "Chat Settings"
        new_chat: "New Chat"
        assistant_selection: "Select Assistant"
        assistant_prompt: "Choose an AI assistant for this conversation"
        no_assistant_selected: "No Assistant Selected"
        create_assistant: "Create New Assistant"
        view_history: "View chat history"
        back_to_conversations: "Back to Conversations"
      messages:
        tool_calls: "Tool Calls"
        tool_call: "Tool Call"
        tool_results: "Tool Results"
        tool_result: "Result:"
        tool_status: "Status:"
        thinking: "Thinking..."
        error_occurred: "An error occurred."
      history:
        title: "Chat History"
        view_all: "View All Conversations"
        empty: "No conversation history yet."
      actions:
        start_new: "Start New Chat"
    messages:
      assistant: "Assistant"
      user: "User"
      tool: "Tool"
      system: "System"
      actions:
        copy: "Copy Message"
        edit: "Edit Message"
        delete: "Delete Message"
      tool_info:
        name: "Tool Name:"
        input: "Input:"
        output: "Output:"
        status: "Status:"
        error: "Error:"
    prompts:
      title: "Saved Prompts"
      new: "New Prompt"
      edit: "Edit Prompt"
      delete: "Delete Prompt"
      fields:
        title: "Title"
        content: "Content"
        tags: "Tags"
