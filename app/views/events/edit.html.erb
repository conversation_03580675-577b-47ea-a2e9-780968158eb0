<% content_for :title, t('events.edit.title') %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { 
          items: [
            [t('events.index.title'), events_path], 
            [truncate(@event.title, length: 30), event_path(@event)],
            [t('events.edit.title'), nil]
          ] 
        } %>

        <div class="mb-6">
          <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white transition-colors duration-200">
            <%= t('events.edit.title') %>
          </h1>
          <p class="mt-2 text-gray-600 dark:text-gray-400 transition-colors duration-200">
            <%= t('events.edit.subtitle', title: @event.title) %>
          </p>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-8 border border-gray-200 dark:border-gray-700 transition-all duration-200">
          <%= form_with model: @event, local: true, class: "space-y-8" do |f| %>
            <% if @event.errors.any? %>
              <div class="p-4 mb-6 text-sm text-red-800 dark:text-red-300 rounded-lg bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 transition-colors duration-200">
                <div class="flex items-center mb-1">
                  <svg class="w-5 h-5 mr-2 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                  <h2 class="font-medium"><%= t('events.form.error', count: @event.errors.count) %></h2>
                </div>
                <ul class="mt-1.5 list-disc list-inside ml-6">
                  <% @event.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <!-- 基本信息 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.basic_info') %>
              </h3>
              
              <div class="space-y-2">
                <%= f.label :title, t('events.fields.title'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                <%= f.text_field :title, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: t('events.form.title_placeholder') %>
              </div>

              <div class="space-y-2">
                <%= f.label :description, t('events.fields.description'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                <%= f.text_area :description, rows: 6, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: t('events.form.description_placeholder') %>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <%= f.label :event_category_id, t('events.fields.category'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.collection_select :event_category_id, EventCategory.all, :id, :name,
                      { prompt: t('events.form.select_category') },
                      class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200" %>
                </div>

                <div class="space-y-2">
                  <%= f.label :difficulty_level, t('events.fields.difficulty'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.select :difficulty_level, 
                      options_for_select(Event.difficulty_levels.map { |key, _| [t("events.difficulty_levels.#{key}"), key] }, @event.difficulty_level),
                      { prompt: t('events.form.select_difficulty') },
                      class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200" %>
                </div>
              </div>
            </div>

            <!-- 时间和地点 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.schedule_location') %>
              </h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <%= f.label :start_time, t('events.fields.start_time'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.datetime_local_field :start_time, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200" %>
                </div>

                <div class="space-y-2">
                  <%= f.label :end_time, t('events.fields.end_time'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.datetime_local_field :end_time, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200" %>
                </div>
              </div>

              <div class="space-y-2">
                <%= f.label :is_online, class: "flex items-center space-x-3 cursor-pointer" do %>
                  <%= f.check_box :is_online, class: "rounded border-gray-300 dark:border-gray-600 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:bg-gray-700 dark:checked:bg-primary-600 dark:checked:border-primary-600" %>
                  <span class="text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200">
                    <%= t('events.fields.is_online') %>
                  </span>
                <% end %>
              </div>

              <div class="space-y-2" data-controller="conditional-fields" data-conditional-fields-trigger-value="event_is_online">
                <%= f.label :meeting_link, t('events.fields.meeting_link'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                <%= f.url_field :meeting_link, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "https://zoom.us/j/..." %>
              </div>
            </div>

            <!-- 价格和容量 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.pricing_capacity') %>
              </h3>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="space-y-2">
                  <%= f.label :price, t('events.fields.price'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.number_field :price, step: 0.01, min: 0, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "0.00" %>
                </div>

                <div class="space-y-2">
                  <%= f.label :member_price, t('events.fields.member_price'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.number_field :member_price, step: 0.01, min: 0, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "0.00" %>
                </div>

                <div class="space-y-2">
                  <%= f.label :max_participants, t('events.fields.max_participants'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.number_field :max_participants, min: 1, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "20" %>
                </div>
              </div>
            </div>

            <!-- 可见性设置 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.visibility') %>
              </h3>
              
              <div class="space-y-4">
                <% Event.visibilities.each do |key, value| %>
                  <div class="relative">
                    <%= f.radio_button :visibility, key, class: "peer sr-only", id: "event_visibility_#{key}", checked: @event.visibility == key %>
                    <label for="event_visibility_<%= key %>" class="flex items-start p-4 border-2 border-gray-200 dark:border-gray-700 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200">
                      <div class="flex items-center h-5">
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 peer-checked:bg-primary-500 dark:peer-checked:bg-primary-400"></div>
                      </div>
                      <div class="ml-3">
                        <div class="text-base font-medium text-gray-900 dark:text-white transition-colors duration-200">
                          <%= t("events.visibility.#{key}.title") %>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 transition-colors duration-200">
                          <%= t("events.visibility.#{key}.description") %>
                        </div>
                      </div>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>

            <!-- 状态管理 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.status_management') %>
              </h3>
              
              <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 transition-colors duration-200">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white transition-colors duration-200">
                      <%= t('events.form.current_status') %>
                    </p>
                    <p class="text-sm text-amber-800 dark:text-amber-200 font-semibold">
                      <%= t("events.status.#{@event.status}") %>
                    </p>
                  </div>
                  
                  <div class="flex space-x-2">
                    <% if @event.draft? %>
                      <%= link_to publish_event_path(@event), method: :patch,
                          class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-bold rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-hidden focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200 shadow-md",
                          data: { 
                            confirm: t('events.confirmations.publish'),
                            turbo_method: :patch
                          } do %>
                        <%= t('events.actions.publish_event') %>
                      <% end %>
                    <% elsif @event.published? %>
                      <div class="bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-700 rounded-lg p-2">
                        <p class="text-xs text-red-800 dark:text-red-200 mb-2 font-medium"><%= t('events.edit.danger_operation') %></p>
                        <%= button_to cancel_event_path(@event), method: :patch,
                            class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-bold rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200 shadow-md",
                            data: { confirm: t('events.confirmations.cancel_with_warning') } do %>
                          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                          </svg>
                          <%= t('events.edit.cancel_this_event') %>
                        <% end %>
                      </div>
                    <% end %>
                  </div>
                </div>
                
                <% if @event.published? %>
                  <div class="mt-3 text-xs text-amber-700 dark:text-amber-300">
                    <%= t('events.edit.status_management_tip').html_safe %>
                  </div>
                <% end %>
              </div>
            </div>

            <div class="flex justify-between pt-6 mt-6 border-t border-gray-200 dark:border-gray-700">
              <div>
                <%= link_to t('events.actions.view_event'), @event,
                    class: "inline-flex items-center justify-center text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-base px-6 py-3.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 transition-all duration-200" %>
              </div>
              
              <div class="flex space-x-3">
                <%= link_to t('events.edit.discard_changes'), @event, 
                    class: "inline-flex items-center justify-center text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-base px-6 py-3.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 transition-all duration-200" %>
                <%= f.submit t('events.edit.save_event_info'), 
                    class: "inline-flex items-center justify-center text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:ring-4 focus:outline-hidden focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-base px-6 py-3.5 text-center shadow-xs hover:shadow-md transition-all duration-200" %>
              </div>
            </div>
            
            <div class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <p class="text-sm text-blue-800 dark:text-blue-200">
                <%= t('events.edit.form_explanation').html_safe %>
              </p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- 右侧信息 -->
      <div class="col-span-1 mt-4 xl:mt-0">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200 mb-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 transition-colors duration-200">
            <%= t('events.form.event_stats') %>
          </h3>
          <dl class="space-y-3">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 transition-colors duration-200">
                <%= t('events.stats.registrations') %>
              </dt>
              <dd class="text-2xl font-bold text-gray-900 dark:text-white transition-colors duration-200">
                <%= @event.registered_users.count %>
                <% if @event.max_participants %>
                  <span class="text-sm text-gray-500 dark:text-gray-400">/ <%= @event.max_participants %></span>
                <% end %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 transition-colors duration-200">
                <%= t('events.stats.created') %>
              </dt>
              <dd class="text-sm text-gray-900 dark:text-white transition-colors duration-200">
                <%= l(@event.created_at, format: :short) %>
              </dd>
            </div>
            
            <% if @event.published_at %>
              <div>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 transition-colors duration-200">
                  <%= t('events.stats.published') %>
                </dt>
                <dd class="text-sm text-gray-900 dark:text-white transition-colors duration-200">
                  <%= l(@event.published_at, format: :short) %>
                </dd>
              </div>
            <% end %>
          </dl>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 transition-colors duration-200">
            <%= t('events.form.quick_actions') %>
          </h3>
          <div class="space-y-3">
            <%= link_to registrations_event_path(@event), 
                class: "w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
              </svg>
              <%= t('events.actions.manage_registrations') %>
            <% end %>
            
            <%= link_to analytics_event_path(@event), 
                class: "w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              <%= t('events.actions.view_analytics') %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 