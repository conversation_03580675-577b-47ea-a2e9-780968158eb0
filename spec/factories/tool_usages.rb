FactoryBot.define do
  factory :tool_usage do
    message { association :message }
    function_name { "test_function" }
    arguments { { "param1" => "value1", "param2" => 42 } }
    result { { "status" => "ok", "data" => "Some result" } }
    status { "pending" }

    # 成功状态的工具使用
    trait :success do
      status { "success" }
      result { { "status" => "ok", "data" => "Success result" } }
    end

    # 失败状态的工具使用
    trait :failed do
      status { "failed" }
      result { { "status" => "error", "message" => "Error occurred" } }
    end

    # 待处理状态的工具使用
    trait :pending do
      status { "pending" }
      result { nil }
    end

    # 不同函数名的工具使用
    trait :weather do
      function_name { "get_weather" }
      arguments { { "location" => "Beijing", "unit" => "celsius" } }
    end

    trait :calculator do
      function_name { "calculate" }
      arguments { { "expression" => "2+2" } }
    end
  end
end
