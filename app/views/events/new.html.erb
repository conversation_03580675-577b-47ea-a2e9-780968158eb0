<% content_for :title, t('events.new.title') %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { 
          items: [
            [t('events.index.title'), events_path], 
            [t('events.new.title'), nil]
          ] 
        } %>

        <div class="mb-6">
          <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white transition-colors duration-200">
            <%= t('events.new.title') %>
          </h1>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-8 border border-gray-200 dark:border-gray-700 transition-all duration-200">
          <%= form_with model: @event, local: true, class: "space-y-8" do |f| %>
            <% if @event.errors.any? %>
              <div class="p-4 mb-6 text-sm text-red-800 dark:text-red-300 rounded-lg bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 transition-colors duration-200">
                <div class="flex items-center mb-1">
                  <svg class="w-5 h-5 mr-2 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                  <h2 class="font-medium"><%= t('events.form.error', count: @event.errors.count) %></h2>
                </div>
                <ul class="mt-1.5 list-disc list-inside ml-6">
                  <% @event.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <!-- 基本信息 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.basic_info') %>
              </h3>
              
              <div class="space-y-2">
                <%= f.label :title, t('events.fields.title'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                <%= f.text_field :title, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: t('events.form.title_placeholder') %>
              </div>

              <div class="space-y-2">
                <%= f.label :description, t('events.fields.description'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                <%= f.text_area :description, rows: 6, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: t('events.form.description_placeholder') %>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <%= f.label :event_category_id, t('events.fields.category'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.collection_select :event_category_id, EventCategory.all, :id, :name,
                      { prompt: t('events.form.select_category') },
                      class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200" %>
                </div>

                <div class="space-y-2">
                  <%= f.label :difficulty_level, t('events.fields.difficulty'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.select :difficulty_level, 
                      options_for_select(Event.difficulty_levels.map { |key, _| [t("events.difficulty_levels.#{key}"), key] }),
                      { prompt: t('events.form.select_difficulty') },
                      class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200" %>
                </div>
              </div>
            </div>

            <!-- 时间和地点 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.schedule_location') %>
              </h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                  <%= f.label :start_time, t('events.fields.start_time'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <div class="flex items-center space-x-3">
                    <%= f.hidden_field :start_time, id: "event_start_time" %>
                    <div id="start_time_display" class="flex-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg p-3.5 shadow-xs transition-colors duration-200 min-h-[52px] flex items-center">
                      <span class="text-gray-500 dark:text-gray-400"><%= t('events.datetime_picker.start_time_placeholder') %></span>
                    </div>
                    <%= render partial: 'shared/datetime_picker_modal', locals: { 
                        field_name: 'start_time', 
                        field_id: 'event_start_time', 
                        label: t('events.datetime_picker.start_time_button') 
                    } %>
                  </div>
                </div>

                <div class="space-y-2">
                  <%= f.label :end_time, t('events.fields.end_time'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <div class="flex items-center space-x-3">
                    <%= f.hidden_field :end_time, id: "event_end_time" %>
                    <div id="end_time_display" class="flex-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg p-3.5 shadow-xs transition-colors duration-200 min-h-[52px] flex items-center">
                      <span class="text-gray-500 dark:text-gray-400"><%= t('events.datetime_picker.end_time_placeholder') %></span>
                    </div>
                    <%= render partial: 'shared/datetime_picker_modal', locals: { 
                        field_name: 'end_time', 
                        field_id: 'event_end_time', 
                        label: t('events.datetime_picker.end_time_button') 
                    } %>
                  </div>
                </div>
              </div>

              <div class="space-y-2">
                <%= f.label :is_online, class: "flex items-center space-x-3 cursor-pointer" do %>
                  <%= f.check_box :is_online, class: "rounded border-gray-300 dark:border-gray-600 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50 dark:bg-gray-700 dark:checked:bg-primary-600 dark:checked:border-primary-600" %>
                  <span class="text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200">
                    <%= t('events.fields.is_online') %>
                  </span>
                <% end %>
              </div>

              <div class="space-y-2" data-controller="conditional-fields" data-conditional-fields-trigger-value="event_is_online">
                <%= f.label :meeting_link, t('events.fields.meeting_link'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                <%= f.url_field :meeting_link, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "https://zoom.us/j/..." %>
              </div>
            </div>

            <!-- 价格和容量 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.pricing_capacity') %>
              </h3>
              
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="space-y-2">
                  <%= f.label :price, t('events.fields.price'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.number_field :price, step: 0.01, min: 0, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "0.00" %>
                </div>

                <div class="space-y-2">
                  <%= f.label :member_price, t('events.fields.member_price'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.number_field :member_price, step: 0.01, min: 0, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "0.00" %>
                </div>

                <div class="space-y-2">
                  <%= f.label :max_participants, t('events.fields.max_participants'), class: "block text-base font-medium text-gray-900 dark:text-gray-100 transition-colors duration-200" %>
                  <%= f.number_field :max_participants, min: 1, class: "mt-1 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 rounded-lg focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-600 dark:focus:border-primary-500 block w-full p-3.5 shadow-xs transition-colors duration-200", placeholder: "20" %>
                </div>
              </div>
            </div>

            <!-- 可见性设置 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.visibility') %>
              </h3>
              
              <div class="space-y-4">
                <% Event.visibilities.each do |key, value| %>
                  <div class="relative">
                    <%= f.radio_button :visibility, key, class: "peer sr-only", id: "event_visibility_#{key}" %>
                    <label for="event_visibility_<%= key %>" class="flex items-start p-4 border-2 border-gray-200 dark:border-gray-700 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200">
                      <div class="flex items-center h-5">
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 peer-checked:bg-primary-500 dark:peer-checked:bg-primary-400"></div>
                      </div>
                      <div class="ml-3">
                        <div class="text-base font-medium text-gray-900 dark:text-white transition-colors duration-200">
                          <%= t("events.visibility.#{key}.title") %>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 transition-colors duration-200">
                          <%= t("events.visibility.#{key}.description") %>
                        </div>
                      </div>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>

            <!-- 发布状态设置 -->
            <div class="space-y-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white transition-colors duration-200">
                <%= t('events.form.status_management') %>
              </h3>
              
              <div class="space-y-4">
                <% Event.statuses.each do |key, value| %>
                  <div class="relative">
                    <%= f.radio_button :status, key, class: "peer sr-only", id: "event_status_#{key}", checked: key == 'draft' %>
                    <label for="event_status_<%= key %>" class="flex items-start p-4 border-2 border-gray-200 dark:border-gray-700 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200">
                      <div class="flex items-center h-5">
                        <div class="w-4 h-4 rounded-full border-2 border-gray-300 dark:border-gray-600 peer-checked:border-primary-500 dark:peer-checked:border-primary-400 peer-checked:bg-primary-500 dark:peer-checked:bg-primary-400"></div>
                      </div>
                      <div class="ml-3">
                        <div class="text-base font-medium text-gray-900 dark:text-white transition-colors duration-200">
                          <%= t("events.status.#{key}") %>
                        </div>
                        <div class="text-sm text-gray-600 dark:text-gray-300 transition-colors duration-200">
                          <%= t("events.status_descriptions.#{key}") %>
                        </div>
                      </div>
                    </label>
                  </div>
                <% end %>
              </div>

              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 transition-colors duration-200">
                <div class="flex">
                  <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                  <div class="text-sm text-blue-700 dark:text-blue-300 transition-colors duration-200">
                    <p class="font-medium mb-1">💡 <%= t('events.form.status_tip_title') %></p>
                    <p><%= t('events.form.status_tip_content') %></p>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end pt-4">
              <%= link_to t('common.actions.cancel'), events_path,
                  class: "mr-3 inline-flex items-center justify-center text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-base px-6 py-3.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 transition-all duration-200" %>
              <%= f.submit t('events.new.create'), 
                  class: "inline-flex items-center justify-center text-white bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 focus:ring-4 focus:outline-hidden focus:ring-primary-300 dark:focus:ring-primary-800 font-medium rounded-lg text-base px-6 py-3.5 text-center shadow-xs hover:shadow-md transition-all duration-200" %>
            </div>
          <% end %>
        </div>
      </div>

      <!-- 右侧提示 -->
      <div class="col-span-1 mt-4 xl:mt-0">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-xl dark:shadow-gray-900/30 p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 transition-colors duration-200">
            <%= t('events.form.tips.title') %>
          </h3>
          <ul class="space-y-3 text-gray-600 dark:text-gray-300 transition-colors duration-200">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('events.form.tips.clear_title') %></span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('events.form.tips.detailed_description') %></span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('events.form.tips.appropriate_capacity') %></span>
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-primary-500 mr-2 mt-0.5 shrink-0" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span><%= t('events.form.tips.clear_schedule') %></span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div> 