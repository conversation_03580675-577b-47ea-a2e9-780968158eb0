require 'rails_helper'

RSpec.describe 'AI Chat Feature', type: :feature do
  let(:user) { create(:user) } # 工厂已经设置密码为 'password123'
  let(:assistant) { create(:assistant, user: user, name: "助手1", instructions: "这是测试用的助手指令。") } # 明确命名并添加指令
  let(:conversation) { create(:conversation, user: user, assistant: assistant) }
  let!(:system_message) { create(:message, :system, messageable: conversation, content: "System instructions") } # 这个系统消息不会在界面显示

  # --- Authentication & Authorization ---
  describe "访问控制" do
    it "未登录用户访问会话页面应重定向到登录" do
      visit ai_conversation_path(conversation)
      expect(current_path).to eq(new_session_path), "页面未重定向到登录页面"
      expect(page).to have_content("Sign In")
    end

    it "未登录用户访问会话列表应重定向到登录" do
      visit ai_conversations_path
      expect(current_path).to eq(new_session_path)
    end

    it "未登录用户访问新建会话页面应重定向到登录" do
      visit new_ai_conversation_path
      expect(current_path).to eq(new_session_path)
    end

    it "用户不能访问其他用户的会话" do
      other_user = create(:user)
      other_assistant = create(:assistant, user: other_user)
      other_conversation = create(:conversation, user: other_user, assistant: other_assistant)
      sign_in(user)
      visit ai_conversation_path(other_conversation)
      # 检查是否重定向到根路径
      expect(page).to have_current_path(root_path, ignore_query: true)
      # 检查 flash 消息
      expect(page).to have_css('div[data-type="error"] div.font-medium', text: "You are not authorized", wait: 10)
    end
  end

  # --- Conversation Management ---
  describe "会话管理" do
    before do
      sign_in(user)
    end

    context "列表页面 (/ai/conversations)" do
      before do
        create_list(:conversation, 3, user: user, assistant: assistant)
        visit ai_conversations_path
      end

      it "显示会话列表" do
        expect(page).to have_css('.grid.grid-cols-1 .bg-white', minimum: 3) # Expect at least 3 cards
      end

      it "会话卡片显示标题和时间" do
        within(first('.grid.grid-cols-1 .bg-white')) do
          expect(page).to have_css('h3')
          expect(page).to have_text(/ago/)
        end
      end

      it "包含指向会话详情的链接" do
         expect(page).to have_link(href: %r{/ai/conversations/\d+})
      end

      it "包含新建会话按钮并能导航" do
        click_link "New Conversation"
        expect(page).to have_current_path(new_ai_conversation_path)
      end
    end

    context "新建会话页面 (/ai/conversations/new)" do
       before do
         visit new_ai_conversation_path
       end

      it "显示助手列表供选择" do
        create_list(:assistant, 2, user: user) # Create more assistants
        visit new_ai_conversation_path # Re-visit after creating assistants
        # Verify all user's assistants are listed
        user.assistants.each do |asst|
          expect(page).to have_css("label[for='conversation_assistant_id_#{asst.id}']", text: asst.name)
        end
      end

      it "成功创建会话并重定向" do
        fill_in 'conversation[title]', with: '测试新会话'
        first('label[for^="conversation_assistant_id_"]').click # Select the first assistant
        click_button 'New Conversation'
        expect(page).to have_current_path(%r{/ai/conversations/\d+})
        expect(page).to have_content('测试新会话')
      end
    end

    context "特定会话页面 (/ai/conversations/:id)" do
       before do
         visit ai_conversation_path(conversation)
       end

      it "显示正确的会话标题" do
        expect(page).to have_content(conversation.title)
      end

      it "显示当前选择的助手名称 (头部)" do
         within('div[data-controller="ai-chat"] > div:first-child') do # More specific selector for header
           expect(page).to have_content(assistant.name)
         end
      end

      it "空会话时显示欢迎信息和助手指令" do
         # Ensure the conversation is empty for this test
         conversation.messages.where.not(role: :system).destroy_all
         visit ai_conversation_path(conversation) # Re-visit after clearing messages

         expect(page).to have_content("Start a new conversation by selecting an assistant and sending a message.") # Match the actual empty state text
         expect(page).to have_css('h4', text: "Instructions:") # Match the actual text rendered (translation + colon)
         expect(page).to have_content(assistant.instructions)
         expect(page).not_to have_content("System instructions") # System message content shouldn't be visible
      end

      it "包含新建会话按钮并能导航 (头部按钮)" do
        find('a', text: 'New Chat').click
        expect(page).to have_current_path(new_ai_conversation_path)
      end

      describe "助手切换功能", js: true do
        let!(:another_assistant) { create(:assistant, user: user, name: "另一个助手") }

        before do
          # Ensure user is signed in for JS tests within this context
          # sign_in(user) # Already signed in the outer context
          visit ai_conversation_path(conversation)
          expect(page).to have_css('#assistantDropdownButton', wait: 10) # Wait for button
        end

        it "可以打开助手选择下拉菜单" do
          find('#assistantDropdownButton').click
          expect(page).to have_css('#assistantDropdown', visible: true, wait: 5) # Check visibility
          expect(page).to have_content("Select Assistant")
        end

        it "可以成功切换助手" do
          find('#assistantDropdownButton').click
          within('#assistantDropdown') do
            click_on another_assistant.name
          end
          expect(page).to have_css('#assistantDropdownButton', text: another_assistant.name, wait: 5)
          expect(conversation.reload.assistant).to eq(another_assistant)
        end

        it "点击创建新助手链接能导航" do
          find('#assistantDropdownButton').click
          within('#assistantDropdown') do
            click_link "Create New Assistant"
          end
          expect(page).to have_current_path(new_ai_assistant_path)
          # Check for the actual H1 title on the new assistant page
          expect(page).to have_css('h1', text: 'New Assistant')
        end
      end

      describe "聊天历史侧边栏", js: true do
        let(:history_button_selector) { 'button[data-drawer-toggle="history-drawer"]' }
        let(:history_drawer_selector) { '#history-drawer' }
        let!(:other_conv) { create(:conversation, user: user, assistant: assistant, title: "历史会话") }

        before do
          visit ai_conversation_path(conversation)
          expect(page).to have_css(history_button_selector, wait: 10)
        end

        def open_history_drawer
          # 1. 点击按钮
          find(history_button_selector).click
          drawer = find(history_drawer_selector)

          # 2. 等待动画完成 - drawer需要变为可见且动画类被移除
          expect(page).to have_no_css("#{history_drawer_selector}.translate-x-full", wait: 10)
          expect(drawer).to be_visible

          # 3. 等待内容加载 - 确保标题和至少"View All Conversations"链接出现
          expect(page).to have_content("Chat History", wait: 5)
          expect(page).to have_link("View All Conversations", wait: 5)
        end

        it "按钮有正确的 title" do
          expect(find(history_button_selector)['title']).to eq('View chat history')
        end

        it "点击按钮能打开/关闭侧边栏" do
          # 打开侧边栏
          open_history_drawer
          expect(page).to have_content("Chat History")

          # 关闭侧边栏
          find("#{history_drawer_selector} button[data-drawer-hide='history-drawer']").click
          # 等待关闭动画完成
          expect(page).to have_css("#{history_drawer_selector}.translate-x-full", wait: 5)
          expect(page).not_to have_selector("#{history_drawer_selector}", visible: true)
        end

        it "侧边栏显示最近的会话列表" do
          open_history_drawer
          # 等待内容加载
          within(history_drawer_selector) do
            expect(page).to have_content(conversation.title)
            expect(page).to have_content(other_conv.title)
          end
        end

        it "点击侧边栏中的会话能导航" do
          open_history_drawer
          # 等待内容加载并点击
          within(history_drawer_selector) do
            click_link other_conv.title
          end
          # 等待页面加载
          expect(page).to have_current_path(ai_conversation_path(other_conv))
          expect(page).to have_content(other_conv.title)
        end

        it "点击 'View All Conversations' 能导航到列表页" do
          open_history_drawer
          # 等待内容加载并点击
          within(history_drawer_selector) do
            click_link "View All Conversations"
          end
          # 等待页面加载
          expect(page).to have_current_path(ai_conversations_path)
          # 验证页面标题是否为 "AI Chat" (根据 locales 文件)
          expect(page).to have_css('h1', text: "AI Chat")
        end
      end

      describe "清除会话功能", js: true do
        let!(:conversation_with_messages) do
          conv = create(:conversation, user: user, assistant: assistant)
          create(:message, :system, messageable: conv, content: "System Prompt")
          create(:message, :user, messageable: conv, content: "User Question 1")
          create(:message, :assistant, messageable: conv, content: "Assistant Answer 1")
          conv
        end

        before do
          # sign_in(user) # Already signed in
          visit ai_conversation_path(conversation_with_messages)
          # 找到正确的表单并验证其中包含清除按钮
          @clear_form = find("form[action='#{clear_conversation_ai_conversation_path(conversation_with_messages)}'][method='post']")
          expect(@clear_form).to have_button(title: 'Clear Conversation')
        end

        it "显示清除按钮" do
          # 验证清除按钮的存在性和属性
          expect(@clear_form).to have_button(title: 'Clear Conversation')
        end

        it "点击清除并取消，消息应保留" do
          initial_message_count = conversation_with_messages.messages.count
          expect(page).to have_content("User Question 1")

          # 先点击按钮触发确认框
          @clear_form.find('button[title="Clear Conversation"]').click
          # 然后告诉 Capybara 拒绝确认框 (增加等待时间)
          page.dismiss_confirm(wait: 5)

          # 检查内容是否保留
          expect(page).to have_content("User Question 1") # Still visible
          expect(conversation_with_messages.messages.reload.count).to eq(initial_message_count)
        end

        it "点击清除并确认，应删除非系统消息并显示提示" do
          expect(page).to have_content("User Question 1")

          # 先点击按钮触发确认框
          @clear_form.find('button[title="Clear Conversation"]').click
          # 然后告诉 Capybara 接受确认框 (增加等待时间)
          page.accept_confirm(wait: 5)

          # 等待后续操作完成 (路径检查和 flash 消息)
          using_wait_time(10) do
            expect(page).to have_current_path(ai_conversation_path(conversation_with_messages))
            expect(page).to have_css('#flash .font-medium', text: "Conversation cleared successfully.")
          end
          expect(page).not_to have_content("User Question 1")
          expect(conversation_with_messages.messages.reload.count).to eq(1)
          expect(conversation_with_messages.messages.first.role).to eq("system")
        end
      end
    end

    context "最新会话路由 (/ai/conversations/latest)" do
      it "有会话时重定向到最新的会话页面" do
        # Ensure conversation exists (created in outer scope)
        visit latest_ai_conversations_path
        expect(page).to have_current_path(ai_conversation_path(conversation)) # Assumes 'conversation' is the latest
      end

      it "无会话时重定向到新建会话页面" do
        user.conversations.destroy_all
        visit latest_ai_conversations_path
        expect(page).to have_current_path(new_ai_conversation_path)
      end
    end
  end

  # --- Chat Interaction & Message Features ---
  describe "聊天交互与消息功能", js: true do
     before do
       sign_in(user)
       visit ai_conversation_path(conversation)
       # Removed potentially flaky wait from shared before block
       # expect(page).to have_css('#chat_form', wait: 5)
     end

    context "发送消息" do
      it "成功发送消息后显示用户消息和思考状态" do
        within('#chat_form') do
          find('textarea[name="message[content]"]').set("Hello, Assistant!")
          find('button[data-ai-chat-target="submit"]').click
        end
        expect(page).to have_content("Hello, Assistant!")
        # Assuming the backend immediately adds a processing message or updates status
        # This might need adjustment based on actual implementation (e.g., waiting for Turbo Stream)
        expect(page).to have_css('.bg-yellow-100', text: 'processing', wait: 5) # Check for processing status indicator
      end

      it "发送后输入框应清空并可继续输入", js: true do
        # 等待页面和输入框加载完成
        using_wait_time(10) do
          expect(page).to have_field("message[content]", wait: 10) # 等待输入框而不是整个表单
        end

        # 输入并发送消息
        within("#chat_form") do
          fill_in "message[content]", with: "Test message"
          click_button "Send"
        end

        # 等待消息显示和状态更新
        expect(page).to have_text("Test message", wait: 5)
        expect(page).to have_css(".bg-yellow-100", text: "processing", wait: 5)

        # 等待输入框重置并验证
        expect(page).to have_field("message[content]", with: "", wait: 5)

        # 验证可以继续输入
        fill_in "message[content]", with: "New message"
        expect(find_field("message[content]").value).to eq("New message")
      end

      it "不允许发送空消息" do
         initial_message_count = conversation.messages.count
         within('#chat_form') do
           find('textarea[name="message[content]"]').set("   ") # Whitespace only
           find('button[data-ai-chat-target="submit"]').click
         end
         # Give a brief moment for potential (unwanted) submission
         sleep 0.1
         expect(conversation.messages.reload.count).to eq(initial_message_count)
         expect(page).not_to have_css('.bg-yellow-100', text: 'processing') # No processing indicator
      end
    end

    context "消息显示" do
      let!(:user_message) { create(:message, :user, messageable: conversation, content: "用户测试消息") }
      let!(:assistant_message) { create(:message, :assistant, messageable: conversation, content: "助手回复测试", status: :completed) }

      # Removed redundant before block, page is already visited in the parent describe block
      # before do
      #   visit ai_conversation_path(conversation) # Re-visit to see messages
      # end

      # User message timestamp is part of the user message partial, which wasn't provided, assuming it exists
      # it "用户消息应包含时间戳" do
      #   within("#message_#{user_message.id}") do
      #     expect(page).to have_css('span.text-xs.font-normal') # Check for timestamp element
      #   end
      # end

      it "助手消息应包含状态和时间戳 (底部)", js: true do
        visit ai_conversation_path(conversation) # 确保重新访问页面
        expect(page).to have_css("#message_#{assistant_message.id}", wait: 5) # 等待消息元素出现

        within("#message_#{assistant_message.id}") do
          # Check footer div
          within('div.flex.items-center.justify-between.mt-1') do
            expect(page).to have_css('span.text-xs.font-medium', text: 'completed') # Status
            expect(page).to have_css('span.text-xs.font-normal') # Timestamp
          end
        end
      end

      # Add more specific tests for Markdown, Code Highlighting, and Plugins if needed
      # Example for basic code block check:
      # it "代码块应进行高亮 (基础检查)" do
      #   code_message = create(:message, :assistant, messageable: conversation, content: "```ruby\ndef hello\n  puts 'world'\nend\n```")
      #   visit ai_conversation_path(conversation)
      #   within("#message_#{code_message.id}") do
      #     expect(page).to have_css('pre code.language-ruby')
      #     expect(page).to have_css('pre.line-numbers') # Check if line numbers class is added
      #     expect(page).to have_css('.code-toolbar .toolbar-item span', text: 'Ruby') # Check show language
      #   end
      # end
    end

    # --- New Tests Added Below ---

    context "消息复制 (自定义按钮)" do
      let!(:simple_message) { create(:message, :assistant, messageable: conversation, content: "这是一条简单消息。") }
      let!(:code_message) { create(:message, :assistant, messageable: conversation, content: "这是带代码的消息：\n```ruby\ndef test\n  puts 'Hello'\nend\n```\n结束。") }

      before do
        visit ai_conversation_path(conversation)
      end

      it "按钮存在于助手消息中，且隐藏的源 Markdown 内容正确", js: true do
        sign_in(user)
        visit ai_conversation_path(conversation)

        # 等待页面加载完成并重新加载消息
        expect(page).to have_css("#message_#{simple_message.id}", wait: 10)

        # 验证简单消息
        within("#message_#{simple_message.id}") do
          # 首先确认复制按钮存在
          expect(page).to have_button("Copy")

          # 使用 visible: false 来查找隐藏元素
          hidden_content = find("div[data-clipboard-content]", visible: false)
          expect(hidden_content.text(:all)).to eq(simple_message.content)
        end

        # 验证带代码的消息
        within("#message_#{code_message.id}") do
          expect(page).to have_button("Copy")
          expected_markdown = code_message.content # 直接使用原始内容进行比较
          # 获取隐藏元素的原始内容
          hidden_content = find("div[data-clipboard-content]", visible: false)
          # 规范化换行符，移除多余空格
          actual_content = hidden_content.text(:all).gsub(/\s+/, " ").strip
          expected_content = expected_markdown.gsub(/\s+/, " ").strip
          expect(actual_content).to eq(expected_content)
        end
      end

      # 注意：直接测试剪贴板内容在 Capybara 中比较困难且不可靠。
      # 我们通过测试按钮存在、源数据正确以及点击后的状态反馈来间接验证。
      it "点击按钮后短暂显示 'Copied' 状态", js: true do
        sign_in(user)
        visit ai_conversation_path(conversation)

        expect(page).to have_css("#message_#{code_message.id}", wait: 10)

        within("#message_#{code_message.id}") do
          # 检查初始状态
          default_message = find('[data-clipboard-target="defaultMessage"]')
          success_message = find('[data-clipboard-target="successMessage"]', visible: :all)
          expect(default_message).to be_visible
          expect(success_message[:class]).to include('hidden')

          # 点击复制按钮
          find('button[data-clipboard-target="trigger"]').click

          # 等待状态变化并检查
          sleep 0.5 # 等待状态切换
          expect(default_message[:class]).to include('hidden')
          expect(find('[data-clipboard-target="successMessage"]')).to be_visible

          # 等待状态恢复并检查
          sleep 2.5 # 等待略长于控制器中的 2000ms
          expect(default_message).to be_visible
          expect(success_message[:class]).to include('hidden')
        end
      end
    end

    context "自动滚动" do
       let!(:long_conversation) do
         conv = create(:conversation, user: user, assistant: assistant)
         # Manually create user/assistant message pairs
         15.times do |i|
           create(:message, :user, messageable: conv, content: "User message #{i+1}")
           create(:message, :assistant, messageable: conv, content: "Assistant reply #{i+1}")
         end
         conv
       end

       it "页面加载/刷新时应滚动到底部" do
        visit ai_conversation_path(long_conversation)
        # 等待 JS 执行 (包括 connect 中的 setTimeout)
        sleep 0.5 # 如果不稳定可以适当增加
        messages_container = find('div[data-ai-chat-target="messages"]')
        # 检查滚动条是否接近底部 (允许几个像素的误差)
        is_scrolled_to_bottom = page.evaluate_script(
          "Math.abs(arguments[0].scrollTop + arguments[0].clientHeight - arguments[0].scrollHeight) < 5",
          messages_container
        )
        expect(is_scrolled_to_bottom).to be true
       end

       it "发送消息后应滚动到底部" do
         # 确保会话有足够内容产生滚动条
         # Manually create user/assistant message pairs
         5.times do |i|
           create(:message, :user, messageable: conversation, content: "Initial user message #{i+1}")
           create(:message, :assistant, messageable: conversation, content: "Initial assistant reply #{i+1}")
         end
         visit ai_conversation_path(conversation)
         sleep 0.5 # 等待初始滚动

        # 发送新消息
        within('#chat_form') do
          find('textarea[name="message[content]"]').set("测试发送后滚动")
          find('button[data-ai-chat-target="submit"]').click
        end

        # 等待消息通过 Turbo Stream 添加并触发滚动
        expect(page).to have_content("测试发送后滚动")
        sleep 0.5 # 等待 MutationObserver 和 scrollIntoView
        messages_container = find('div[data-ai-chat-target="messages"]')
        is_scrolled_to_bottom = page.evaluate_script(
          "Math.abs(arguments[0].scrollTop + arguments[0].clientHeight - arguments[0].scrollHeight) < 5",
          messages_container
        )
        expect(is_scrolled_to_bottom).to be true
      end

      # 接收助手消息后的滚动由 MutationObserver 处理，已包含在发送测试的逻辑中
    end

    context "Tool Calls 显示" do
      let(:tool_call_id) { "call_#{SecureRandom.hex(8)}" }
      # 确保证实参是有效的 JSON 字符串
      let(:tool_arguments) { { location: "San Francisco", unit: "celsius" }.to_json }
      let!(:assistant_message_with_tool_call) do
         # Create tool_calls data as a JSON array
         tool_calls_data = [ {
           "id" => tool_call_id,
           "type" => "function",
           "function" => {
             "name" => "get_current_weather",
             "arguments" => tool_arguments
           }
         } ]
         create(:message, :assistant, messageable: conversation, content: "正在查询天气...", tool_calls: tool_calls_data, status: :processing)
      end
       let!(:tool_message_result) do
         # 模拟成功的工具执行结果消息
         msg = create(:message, :tool, messageable: conversation, tool_call_id: tool_call_id, content: "旧金山当前温度 18°C。")
         # 创建关联的成功 ToolUsage 记录
         create(:tool_usage, :success, message: msg, function_name: "get_current_weather")
         # assistant_message_with_tool_call.update(status: :completed) # 如果需要更新助手消息状态
         msg # 返回创建的消息对象
       end
        let!(:failed_tool_call_message) do
         failed_call_id = "call_fail_#{SecureRandom.hex(8)}"
         failed_args = { "symbol" => "INVALID" }.to_json
         # Create tool_calls data as a JSON array
         tool_calls_data = [ {
           "id" => failed_call_id,
           "type" => "function",
           "function" => {
             "name" => "get_stock_price",
             "arguments" => failed_args
           }
         } ]
         create(:message, :assistant, messageable: conversation, content: "查询股价...", tool_calls: tool_calls_data, status: :processing)
       end
        let!(:failed_tool_result) do
         # 模拟失败的工具执行结果消息
         msg = create(:message, :tool, messageable: conversation, tool_call_id: failed_tool_call_message.tool_calls.first["id"], content: "错误：股票代码无效。")
         # 创建关联的失败 ToolUsage 记录
         create(:tool_usage, :failed, message: msg, function_name: "get_stock_price")
         # failed_tool_call_message.update(status: :failed) # 如果需要更新助手消息状态
         msg # 返回创建的消息对象
        end

      # Visit path inside each test to ensure fresh state and message loading
      # before do
      #   visit ai_conversation_path(conversation)
      # end

      it "当助手消息包含 tool_calls 时，应显示 Tool Calls 部分、名称和参数" do
        visit ai_conversation_path(conversation)
        within("#message_#{assistant_message_with_tool_call.id}") do
          expect(page).to have_content("Tool Calls")
          # 检查特定的工具调用块
          within('.bg-gray-50', text: /get_current_weather/) do
            expect(page).to have_content("get_current_weather") # 工具名称
            # 检查 <pre> 标签中的参数 (允许 JSON 格式化后的空格)
            expect(page).to have_css("pre", text: /"location":\s*"San Francisco"/)
            expect(page).to have_css("pre", text: /"unit":\s*"celsius"/)
          end
        end
      end

      it "如果存在对应的 tool role 消息，应显示其结果 (成功)" do
        visit ai_conversation_path(conversation) # Visit inside the test
        # Reload the assistant message to ensure associations are fresh
        assistant_message_with_tool_call.reload
        # Add a small wait for potential Turbo Stream updates / DOM rendering
        sleep 0.2
        within("#message_#{assistant_message_with_tool_call.id}") do
          within('.bg-gray-50', text: /get_current_weather/) do
            # Verify the status label (should be "Result:" because ToolUsage is :success)
            expect(page).to have_content("Result:")
            # Verify the result content from the tool message
            expect(page).to have_content("旧金山当前温度 18°C。")
          end
        end
      end

      it "如果对应的 tool role 消息表示失败，应显示其内容" do
        visit ai_conversation_path(conversation) # Visit inside the test
        # Reload the tool message itself to ensure its associations are loaded
        failed_tool_result.reload
        # Reload the assistant message as well, just in case
        failed_tool_call_message.reload
        # Add a small wait for potential Turbo Stream updates / DOM rendering
        sleep 0.2
        within("#message_#{failed_tool_call_message.id}") do
          within('.bg-gray-50', text: /get_stock_price/) do
            # Verify the status label (should be "Status:" because ToolUsage is :failed)
            expect(page).to have_content("Status:")
            # Verify the error content from the tool message
            expect(page).to have_content("错误：股票代码无效。")
          end
        end
      end
    end
  end
end
