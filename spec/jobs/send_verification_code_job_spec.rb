require 'rails_helper'

RSpec.describe SendVerificationCodeJob, type: :job do
  include ActiveJob::TestHelper

  # 使用 E.164 格式的手机号
  let(:valid_e164_phone) { '+8613812345678' }
  let(:logger) { instance_double(ActiveSupport::Logger) }

  before do
    allow(Rails).to receive(:logger).and_return(logger)
    allow(logger).to receive(:info)
    allow(logger).to receive(:error)
    # 清除队列，确保测试隔离
    clear_enqueued_jobs
    clear_performed_jobs
  end

  describe '#perform' do
    context 'with valid E.164 phone number' do
      it 'calls SmsService.send_sms_code with the phone number' do
        # 验证 SmsService.send_sms_code 被调用
        expect(SmsService).to receive(:send_sms_code).with(valid_e164_phone).and_return('123456')

        # 直接执行 Job 的 perform 方法
        SendVerificationCodeJob.new.perform(valid_e164_phone)
      end

      # Job 本身不再记录 info 日志，移除相关测试
      # it 'logs success message' do ... end
    end

    context 'when SmsService.send_sms_code fails' do
      let(:error_message) { "SMS service error" }

      before do
        # 模拟 SmsService 抛出错误
        allow(SmsService).to receive(:send_sms_code).with(valid_e164_phone).and_raise(StandardError, error_message)
      end

      it 'logs the error and re-raises the exception for retry' do
        # 验证错误被记录
        expect(logger).to receive(:error).with("Error sending verification code to #{valid_e164_phone}: #{error_message}")

        # 验证原始错误被重新抛出，以便 ActiveJob 重试机制生效
        expect {
          SendVerificationCodeJob.new.perform(valid_e164_phone)
        }.to raise_error(StandardError, error_message)
      end
    end

    context 'when phone number is blank or nil' do
      it 'returns early without calling SmsService for nil' do
        expect(SmsService).not_to receive(:send_sms_code)
        SendVerificationCodeJob.new.perform(nil)
      end

      it 'returns early without calling SmsService for blank string' do
        expect(SmsService).not_to receive(:send_sms_code)
        SendVerificationCodeJob.new.perform('')
      end
    end
  end

  describe 'queueing' do
    it 'enqueues the job on the default queue' do
      expect {
        SendVerificationCodeJob.perform_later(valid_e164_phone)
      }.to have_enqueued_job(SendVerificationCodeJob).on_queue('default').with(valid_e164_phone)
    end

    it 'executes job asynchronously' do
      ActiveJob::Base.queue_adapter = :test
      expect {
        SendVerificationCodeJob.perform_later(valid_e164_phone)
      }.to have_enqueued_job
    end
  end

  describe 'retry behavior' do
    it 'is configured to retry on StandardError' do
      # 检查 Job 是否继承自 ApplicationJob (通常配置在此) 或直接检查 retry_on 配置
      # 注意：直接检查 retry_on 的内部配置比较困难且脆弱，通常验证行为即可
      job_instance = SendVerificationCodeJob.new

      # 模拟 perform 失败
      allow(SmsService).to receive(:send_sms_code).and_raise(StandardError, "Temporary failure")
      allow(logger).to receive(:error) # Mock logger to avoid actual logging during test

      # 尝试执行并捕获重试错误
      expect {
        job_instance.perform(valid_e164_phone)
      }.to raise_error(StandardError, "Temporary failure")

      # 验证 ActiveJob 的重试机制
      # 使用 SolidQueue 作为队列适配器
      # 注意：SolidQueue 遵循 ActiveJob 的标准重试机制
      # 此处我们验证基本行为，确保错误正确传播以便 SolidQueue 可以进行重试处理
      # 对于更详细的重试测试，可以考虑检查 SolidJob 记录或使用集成测试
      expect(job_instance).to respond_to(:retry_job) # 确认方法存在
    end
  end
end
