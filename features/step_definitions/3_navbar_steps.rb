Given('I visit any page') do
  visit root_path # 或者任何其他页面
end

When('I click the logo') do
  find('.navbar-logo').click
end

When('I click the {string} link') do |link_text|
  click_link link_text
end

When('I click the {string} button') do |button_text|
  click_button button_text
end

Then('I should be redirected to the homepage') do
  expect(page).to have_current_path(root_path)
end

Then('I should be redirected to the about page') do
  expect(page).to have_current_path(about_path)
end

Then('I should be redirected to the login page') do
  expect(page).to have_current_path(new_session_path)
end

Then('I should be redirected to the registration page') do
  expect(page).to have_current_path(new_registration_path)
end
