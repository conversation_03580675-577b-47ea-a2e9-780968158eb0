class KnowledgeBasePolicy < ApplicationPolicy
  # 前置检查优化
  pre_check :check_authenticated!
  pre_check :check_ownership!, only: %i[update destroy transfer_ownership configure_team add_team remove_team]

  # 定义作用域过滤规则
  relation_scope do |relation|
    # 管理员可以看到所有知识库
    return relation.where(deleted_at: nil) if user&.admin?
    next relation.none unless user

    # 普通用户可见范围：
    relation.where(visibility: :public_visibility) # 使用枚举定义
            .or(relation.where(owner_id: user.id))
            .or(relation.where(visibility: :team_visibility)
                        .where(id: TeamResourceAccess.for_knowledge_bases
                                                     .where(team_member: user.team_members)
                                                     .select(:resource_id)))
            .where(deleted_at: nil)
            .distinct
  end

  # 初始化时预加载用户角色
  def initialize(record, user:)
    super(record, user: user)
    return unless record.present?

    @user_roles = user.team_members
                      .joins(:team_resource_accesses)
                      .where(team_resource_accesses: { resource: record })
                      .merge(TeamResourceAccess.where(resource_kind: :knowledge_base))
                      .pluck("team_resource_accesses.role")
  end

  # 权限方法重构
  def viewer?
    return true if admin?
    return true if record.public?
    record.team_visibility? && has_role?(%w[viewer editor maintainer])
  end

  def editor?
    admin? || has_role?(%w[editor maintainer])
  end

  def maintainer?
    admin? || has_role?("maintainer")
  end

  # 权限规则映射
  def show? = admin? || viewer?
  def update? = admin? || editor?
  def manage? = admin? || maintainer?
  def configure_team? = admin? || maintainer?

  # 删除权限单独处理，因为已归档的知识库不能删除
  def destroy?
    return true if admin?
    return false if record.reload.archived?
    false if record.team_visibility?
  end

  # 基础权限
  def index? = true
  def create? = admin? || authenticated?
  def transfer_ownership? = admin? || owner?

  alias_rule :add_team?, :remove_team?, to: :configure_team?

  # 归档相关权限
  alias_rule :archive?, :unarchive?, :bulk_archive?, :bulk_unarchive?, to: :update?

  def allowed_to?(action, *args)
    return false if record.nil? && !%i[index create].include?(action)
    if action.to_sym == :destroy
      destroy?
    else
      method_name = "#{action}?"
      return send(method_name) if respond_to?(method_name, true)
      super
    end
  end

  # 权限检查辅助方法
  private

  def has_role?(roles)
    return false if @user_roles.blank?
    Array(roles).any? { |r| @user_roles.include?(r) }
  end

  def has_team_dependencies?
    record.team_visibility? && record.team_resource_accesses.exists?
  end

  def team_visible?
    record.team_visibility? && record.has_team_member?(user)
  end

  def create_base_unit?
    # 确保用户是知识库维护者或拥有者
    record.maintainers.include?(user)
  end

  def create_post?
    record.maintainers.include?(user)
  end

  def posts?
    record.maintainers.include?(user)
  end

  def update_post_association?
    record.maintainers.include?(user)
  end
end
