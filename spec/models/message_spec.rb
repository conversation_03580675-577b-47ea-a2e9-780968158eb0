require 'rails_helper'

RSpec.describe Message, type: :model do
  let(:message) { build(:message) }

  describe '关联关系' do
    it { is_expected.to belong_to(:messageable) }
    it { is_expected.to have_many(:tool_usages).dependent(:destroy) }
  end

  describe '回调' do
    it '创建后广播消息' do
      message = build(:message)
      expect(Ai::MessageService).to receive(:broadcast).with(message)
      message.save!
    end

    it '更新后广播消息' do
      message = create(:message)
      expect(Ai::MessageService).to receive(:broadcast).with(message)
      message.update(content: "Updated content")
    end
  end

  describe '验证' do
    context '普通消息' do
      it { is_expected.to validate_presence_of(:content) }
    end

    context '带工具调用的助手消息' do
      let(:message) { build(:message, :with_tool_calls) }

      it '允许内容为空' do
        expect(message).to be_valid
      end
    end
  end

  describe '角色枚举' do
    it '定义了正确的枚举值' do
      expect(Message.roles).to eq(
        'system' => 'system',
        'tool' => 'tool',
        'user' => 'user',
        'assistant' => 'assistant'
      )
    end

    it '有正确的枚举方法' do
      expect(message).to define_enum_for(:role)
        .with_values(system: 'system', tool: 'tool', user: 'user', assistant: 'assistant')
        .backed_by_column_of_type(:string)
    end

    it '生成了判断方法' do
      expect(message).to respond_to(:system?)
      expect(message).to respond_to(:tool?)
      expect(message).to respond_to(:user?)
      expect(message).to respond_to(:assistant?)
    end
  end

  describe '作用域' do
    it '有ordered作用域' do
      expect(Message).to respond_to(:ordered)
    end

    it 'ordered作用域按创建时间升序排序' do
      first = create(:message, created_at: 1.hour.ago)
      second = create(:message, created_at: Time.current)

      expect(Message.ordered).to eq([ first, second ])
    end
  end

  describe '#assistant_msg_with_tool_calling?' do
    context '助手消息带工具调用' do
      let(:message) { build(:message, :with_tool_calls) }

      it '返回true' do
        expect(message.assistant_msg_with_tool_calling?).to be true
      end
    end

    context '助手消息无工具调用' do
      let(:message) { build(:message, :assistant) }

      it '返回false' do
        expect(message.assistant_msg_with_tool_calling?).to be false
      end
    end

    context '非助手消息' do
      let(:message) { build(:message, role: :user) }

      it '返回false' do
        expect(message.assistant_msg_with_tool_calling?).to be false
      end
    end
  end

  describe '#status' do
    context '设置了状态' do
      let(:message) { build(:message, status: :processing) }

      it '返回设置的状态' do
        expect(message.status).to eq(:processing)
      end
    end

    context '内容为nil' do
      let(:message) { build(:message, content: nil) }

      it '返回:processing' do
        expect(message.status).to eq(:processing)
      end
    end

    context '内容存在' do
      let(:message) { build(:message, content: 'test') }

      it '返回:completed' do
        expect(message.status).to eq(:completed)
      end
    end
  end

  describe '#status=' do
    it '将状态转换为Symbol' do
      message.status = 'failed'
      expect(message.status).to eq(:failed)
    end
  end
end
