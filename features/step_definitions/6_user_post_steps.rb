# frozen_string_literal: true

Given('I am logged in') do
  @user = FactoryBot.create(:user)
  visit new_session_path
  fill_in 'user_email_address', with: @user.email_address
  fill_in 'user_password', with: @user.password
  click_button 'Sign in'
  # expect(page).to have_content('Signed in successfully')
end


Given('I am a registered user') do
  @user = FactoryBot.create(:user)
end

When('I visit the new post page') do
  visit new_post_path
end

When('I fill in the post title with {string}') do |title|
  fill_in 'post[title]', with: title
end

When('I fill in the post content with {string}') do |content|
  find('trix-editor').click.set(content)
end

# When('I select {string} from {string}') do |value, field|
#   select value, from: "post_#{field.downcase}"
# end

When(/^I click the "([^"]*)" button in posts$/) do |button|
  click_button button
end

When(/^I click the "([^"]*)" link in posts$/) do |link|
  click_link link
end

Then('I should see {string}') do |text|
  expect(page).to have_content(text)
end

Then('I should not see {string}') do |text|
  expect(page).not_to have_content(text)
end

Given('I have a draft post titled {string}') do |title|
  @post = FactoryBot.create(:post, title: title, status: 'draft', user: @user)
end

Given('I have a post titled {string}') do |title|
  @post = FactoryBot.create(:post, title: title, status: 'published', user: @user)
end

When('I visit the post page') do
  visit post_path(@post)
end

When('I visit the posts page') do
  visit posts_path
end

Given('I have the following posts:') do |table|
  table.hashes.each do |row|
    FactoryBot.create(:post, title: row['title'], status: row['status'], user: @user)
  end
end
