class KnowledgeBasesController < ApplicationController
  before_action :set_knowledge_base, only: [ :show, :edit, :update, :destroy, :archive, :unarchive, :configure_team, :update_team, :manage_teams, :add_team, :remove_team ]

  def index
    @knowledge_bases = authorized(KnowledgeBase.all)
                      .includes(:owner, :team_resource_accesses)
                      .not_archived # 只显示未归档的
                      .search_by_name_or_description(params[:search])
                      .filter_by_visibility(params[:visibility])
                      .order(created_at: :desc)
                      .page(params[:page])
                      .per(10)

    respond_to do |format|
      format.html
      format.json { render json: @knowledge_bases }
    end
  end

  def archived
    @knowledge_bases = authorized(KnowledgeBase.all)
                      .includes(:owner, :team_resource_accesses)
                      .archived  # 只显示已归档的
                      .search_by_name_or_description(params[:search])
                      .filter_by_visibility(params[:visibility])
                      .order(created_at: :desc)
                      .page(params[:page])
                      .per(10)
    render :index
  end

  def show
    authorize! @knowledge_base
  end

  def new
    @knowledge_base = KnowledgeBase.new
    authorize! @knowledge_base
    load_teams_for_form  # 预加载团队数据
  end

  def create
    # 从参数中移除 team_ids
    kb_params = knowledge_base_params.except(:team_ids)
    @knowledge_base = KnowledgeBase.new(kb_params)
    @knowledge_base.owner = current_user
    authorize! @knowledge_base

    # 验证可见性和团队的一致性
    # Use reject(&:blank?).empty? to correctly handle [""] from multi-select
    if @knowledge_base.team_visibility? && params.dig(:knowledge_base, :team_ids)&.reject(&:blank?)&.empty?
      @knowledge_base.errors.add(:base, "Team visibility requires at least one team")
      load_teams_for_form
      render :new, status: :unprocessable_entity
      return
    end

    begin
      ActiveRecord::Base.transaction do
        # 如果有团队，先创建团队资源访问记录
        if params[:knowledge_base][:team_ids].present?
          team_ids = params[:knowledge_base][:team_ids].reject(&:blank?)
          accessible_teams = Team.accessible_to(current_user).where(id: team_ids)

          # 验证是否找到了所有团队
          if accessible_teams.count != team_ids.count
            raise ActiveRecord::RecordNotFound, "Some teams are not accessible"
          end

          # 保存知识库
          @knowledge_base.save!

          # 创建团队资源访问记录
          accessible_teams.each do |team|
            team_member = TeamMember.find_by!(team: team, user: current_user)
            @knowledge_base.team_resource_accesses.create!(
              team_member: team_member,
              role: :maintainer,
              resource_kind: TeamResourceAccess::RESOURCE_KINDS[:knowledge_base][:enum]
            )
          end
        else
          # 如果没有团队，直接保存知识库
          @knowledge_base.save!
        end
      end

      redirect_to @knowledge_base, notice: "Knowledge base #{@knowledge_base.name} created successfully"
    rescue ActiveRecord::RecordInvalid, ActiveRecord::RecordNotFound => e
      load_teams_for_form
      flash.now[:alert] = e.message
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    authorize! @knowledge_base
    @teams = Team.accessible_to(current_user).active
  end

  def update
    authorize! @knowledge_base

    # 从参数中移除 team_ids
    kb_params = knowledge_base_params.except(:team_ids)

    # 验证可见性和团队的一致性
    # Use reject(&:blank?).empty? to correctly handle [""] from multi-select
    selected_visibility = params.dig(:knowledge_base, :visibility) || @knowledge_base.visibility # Use new or existing visibility
    team_ids_present = !params.dig(:knowledge_base, :team_ids)&.reject(&:blank?)&.empty?

    if selected_visibility == "team_visibility" && !team_ids_present
      @knowledge_base.errors.add(:base, "Team visibility requires at least one team")
      load_teams_for_form
      render :edit, status: :unprocessable_entity
      return
    end

    begin
      ActiveRecord::Base.transaction do
        # 1. Handle visibility change *away* from team
        if selected_visibility != "team_visibility" && @knowledge_base.team_visibility?
          @knowledge_base.team_resource_accesses.destroy_all
        end

        # 2. Update KB attributes (this applies the new visibility)
        @knowledge_base.update!(kb_params)

        # 3. Handle team associations based on the *final* visibility state of @knowledge_base
        if @knowledge_base.team_visibility? # Check the updated visibility on the record
          if params[:knowledge_base][:team_ids].present?
            team_ids = params[:knowledge_base][:team_ids].reject(&:blank?)
            accessible_teams = Team.accessible_to(current_user).where(id: team_ids)

            if accessible_teams.count != team_ids.count
              raise ActiveRecord::RecordNotFound, "Some teams are not accessible"
            end

            # Ensure old accesses are gone before creating new ones (might be redundant, but safe)
            @knowledge_base.team_resource_accesses.destroy_all unless @knowledge_base.team_resource_accesses.empty?

            accessible_teams.each do |team|
              team_member = TeamMember.find_by!(team: team, user: current_user)
              @knowledge_base.team_resource_accesses.create!(
                team_member: team_member,
                role: :maintainer,
                resource_kind: TeamResourceAccess::RESOURCE_KINDS[:knowledge_base][:enum]
              )
            end
          else
             # This case is prevented by validation, but if reached, ensure no accesses
             @knowledge_base.team_resource_accesses.destroy_all unless @knowledge_base.team_resource_accesses.empty?
          end
        else # Final visibility is NOT team
          # Ensure accesses are destroyed (might be redundant due to step 1, but safe)
          @knowledge_base.team_resource_accesses.destroy_all unless @knowledge_base.team_resource_accesses.empty?
        end
      end # End transaction

      redirect_to @knowledge_base, notice: "Knowledge base updated successfully"
    rescue ActiveRecord::RecordInvalid, ActiveRecord::RecordNotFound => e
      load_teams_for_form
      flash.now[:alert] = e.message
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    authorize! @knowledge_base
    if @knowledge_base.teams.any?
      redirect_to knowledge_base_path(@knowledge_base),
        alert: "Cannot delete knowledge base with active team members"
    else
      @knowledge_base.update!(deleted_at: Time.current)
      redirect_to knowledge_bases_path,
        notice: "Successfully soft-deleted"
    end
  end

  ## 归档相关操作
  def archive
    authorize! @knowledge_base
    if @knowledge_base.archive
      redirect_to knowledge_bases_path, notice: "Knowledge base has been archived successfully"
    else
      redirect_to knowledge_bases_path, alert: "Failed to archive knowledge base"
    end
  end

  def unarchive
    authorize! @knowledge_base
    if @knowledge_base.unarchive
      redirect_to knowledge_bases_path, notice: "Knowledge base has been unarchived successfully"
    else
      redirect_to knowledge_bases_path, alert: "Failed to unarchive knowledge base"
    end
  end

  def bulk_archive
    return redirect_to knowledge_bases_path, alert: "No items selected" if params[:knowledge_base_ids].blank?

    @knowledge_bases = authorized(KnowledgeBase.not_archived)
                      .where(id: params[:knowledge_base_ids])
    count = @knowledge_bases.count

    if @knowledge_bases.update_all(archived: true)
      redirect_to knowledge_bases_path,
        notice: "Successfully archived #{count} knowledge bases"
    else
      redirect_to knowledge_bases_path,
        alert: "Failed to archive knowledge bases"
    end
  end

  def bulk_unarchive
    return redirect_to archived_knowledge_bases_path, alert: "No items selected" if params[:knowledge_base_ids].blank?

    @knowledge_bases = authorized(KnowledgeBase.archived)
                      .where(id: params[:knowledge_base_ids])
    count = @knowledge_bases.count

    if @knowledge_bases.update_all(archived: false)
      redirect_to archived_knowledge_bases_path,
        notice: "Successfully unarchived #{count} knowledge bases"
    else
      redirect_to archived_knowledge_bases_path,
        alert: "Failed to unarchive knowledge bases"
    end
  end

  ## 团队相关操作

  def manage_teams
    authorize! @knowledge_base, to: :manage_teams?
    @teams = Team.accessible_to(current_user)
    @associated_teams = @knowledge_base.teams
  end

  def add_team
    authorize! @knowledge_base, to: :manage_teams?
    team = Team.find(params[:team_id])
    team_member = TeamMember.find_by!(team: team, user: current_user)

    begin
      # Use find_or_create_by! to avoid validation error if access already exists
      @knowledge_base.team_resource_accesses.find_or_create_by!(
        team_member: team_member,
        resource_kind: TeamResourceAccess::RESOURCE_KINDS[:knowledge_base][:enum]
      ) do |access|
        # Set role only if creating a new record
        access.role = :maintainer
      end # The find_or_create_by! call ends here
      # Removed the extra parenthesis that was here
      redirect_to teams_knowledge_base_path(@knowledge_base), notice: "Team added successfully"
    rescue ActiveRecord::RecordInvalid => e
      redirect_to teams_knowledge_base_path(@knowledge_base), alert: e.message
    end
  end

  def remove_team
    authorize! @knowledge_base, to: :manage_teams?
    team = Team.find(params[:team_id])

    # 找到并删除相关的 team_resource_access 记录
    @knowledge_base.team_resource_accesses
      .joins(:team_member)
      .where(team_members: { team_id: team.id })
      .destroy_all

    redirect_to teams_knowledge_base_path(@knowledge_base), notice: "Team removed successfully"
  end

  def configure_team
    authorize! @knowledge_base, to: :configure_team?

    @teams = Team.accessible_to(current_user)
    # 此处可以加载知识库已有的团队关联信息
  end

  def update_team
    authorize! @knowledge_base, to: :manage_team_access?
    # 假设传来的参数有 team_ids 或其他团队配置参数
    if @knowledge_base.update(knowledge_base_params)
      redirect_to @knowledge_base, notice: "Team settings for knowledge base updated successfully."
    else
      @teams = Team.accessible_to(current_user)
      render :configure_team, status: :unprocessable_entity
    end
  end

  def create_base_unit
    @knowledge_base = authorized(KnowledgeBase.all).find(params[:id])
    authorize! @knowledge_base, to: :create_base_unit?
    base_unit_params = params.permit(
      :name, :content_type, :file_size, :checksum, :status, :metadata,
      :file # 允许直接通过params传递文件
    )

    @base_unit = current_user.base_units.new(base_unit_params)
    # 将文件处理逻辑移到模型
    @base_unit.file.attach(params[:file]) if params[:file].present?
    @base_unit.save

    post = @base_unit.convert_to_markdown!
    post.update!(status: "published", base_unit: @base_unit)
    KnowledgeBasePost.create!(
      knowledge_base: @knowledge_base,
      post: post
    )
    # FIXME: flash提示失败
    render json: { success: true, message: "The file was successfully uploaded and converted. Procedure" }
  rescue => e
    logger.error "Upload failure: #{e.message}"
    render json: {
      success: false,
      message: "Upload failure: #{e.message}",
      error_code: "KBUPL#{Time.now.to_i}"
    }, status: :unprocessable_entity
  end

  def create_post
    @knowledge_base = authorized(KnowledgeBase.all).find(params[:id])
    authorize! @knowledge_base, to: :create_post?
    post_params = params.permit(:content)
    @post = @knowledge_base.posts.new(post_params)
    @post.status = "published"
    @post.user = current_user
    # TODO: content需要提取出title
    @post.title = params[:content].to_s.split("\n").first.presence || "Untitled"
    @post.save!
    KnowledgeBasePost.create!(
      knowledge_base: @knowledge_base,
      post: @post
    )
    redirect_to @knowledge_base, notice: "Post created successfully"
  rescue => e
    redirect_to @knowledge_base, alert: "Failed to create post: #{e.message}"
  end

  def posts
    @knowledge_base = authorized(KnowledgeBase.all).find(params[:id])
    authorize! @knowledge_base, to: :posts?
    @posts = @knowledge_base.posts
    @available_posts = Post.where.not(id: @knowledge_base.posts.pluck(:id))
  end

  def update_post_association
    begin
      Rails.logger.info("update_post_association: #{params.inspect}")
      @knowledge_base = authorized(KnowledgeBase.all).find(params[:id])
      authorize! @knowledge_base, to: :update_post_association?

      case params[:action_type]
      when "attach"
        @post = Post.find(params[:post_id])
        @knowledge_base.posts << @post
        message = "Post added successfully"
      when "detach"
        @knowledge_base.knowledge_base_posts
                      .where(post_id: params[:post_id])
                      .delete_all
        message = "Post removed successfully"
      end

      respond_to do |format|
        format.html do
          redirect_to posts_knowledge_base_path(@knowledge_base),
                      status: :see_other,
                      notice: message
        end
        format.turbo_stream do
          @posts = @knowledge_base.posts.reload
          @available_posts = Post.where.not(id: @posts.pluck(:id))
          render :update_post_association
        end
      end
    rescue ActiveRecord::RecordNotFound => e
      redirect_to knowledge_bases_path, alert: "Knowledge base does not exist"
    rescue => e
      Rails.logger.error "Error in update_post_association: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      redirect_back fallback_location: knowledge_bases_path,
                    alert: "Operation failure: #{e.message}"
    end
  end

  private

  def set_knowledge_base
    @knowledge_base = authorized(KnowledgeBase.all).find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to knowledge_bases_path, alert: "Knowledge base not found"
  end

  def knowledge_base_params
    params.require(:knowledge_base).permit(:name, :description, :visibility, team_ids: [])
  end

  def load_teams_for_form
    @teams = Team.accessible_to(current_user).active.order(:name)
  end
end
