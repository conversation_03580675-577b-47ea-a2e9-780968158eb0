require 'rails_helper'

RSpec.describe Events::CancellationService, type: :service do
  let(:user) { create(:user) }
  let(:event) { create(:event, :published, :upcoming) }
  let(:registration) { create(:event_registration, user: user, event: event, status: 'confirmed') }
  let(:service) { described_class.new(registration) }

  describe '#call' do
    context 'successful cancellation' do
      it 'cancels the registration' do
        result = service.call

        expect(result).to be_success
        expect(registration.reload.status).to eq('cancelled')
      end

      it 'returns the cancelled registration' do
        result = service.call

        expect(result.data).to eq(registration)
      end

      xit 'logs cancellation' do
        expect(Rails.logger).to receive(:info).with(/Event registration cancelled/)
        service.call
      end

      it 'sends cancellation notification email' do
        expect(EventCancellationMailer).to receive(:notification)
          .with(user, event)
          .and_return(double(deliver_later: true))

        service.call
      end
    end

    context 'already cancelled registration' do
      before do
        registration.update!(status: 'cancelled')
      end

      it 'returns failure result' do
        result = service.call

        expect(result).to be_failure
        expect(result.error).to include('already cancelled')
      end

      it 'does not change registration status' do
        expect {
          service.call
        }.not_to change { registration.reload.status }
      end

      it 'does not send notification email' do
        expect(EventCancellationMailer).not_to receive(:notification)
        service.call
      end
    end

    context 'database error during cancellation' do
      before do
        allow(registration).to receive(:update!).and_raise(ActiveRecord::RecordInvalid)
      end

      it 'returns failure result' do
        result = service.call

        expect(result).to be_failure
        expect(result.error).to include('System error')
      end

      it 'does not send notification email' do
        expect(EventCancellationMailer).not_to receive(:notification)
        service.call
      end
    end

    context 'email delivery failure' do
      before do
        allow(EventCancellationMailer).to receive(:notification)
          .and_raise(StandardError, 'Email service unavailable')
      end

      it 'still cancels the registration' do
        result = service.call

        expect(result).to be_success
        expect(registration.reload.status).to eq('cancelled')
      end

      it 'logs the email error' do
        expect(Rails.logger).to receive(:error).with(/Failed to send cancellation notification/)
        service.call
      end
    end
  end

  describe 'private methods' do
    describe '#send_cancellation_notification' do
      it 'queues cancellation email' do
        expect(EventCancellationMailer).to receive(:notification)
          .with(user, event)
          .and_return(double(deliver_later: true))

        service.send(:send_cancellation_notification, event, user)
      end
    end

    describe '#track_cancellation_event' do
      it 'logs cancellation event' do
        expect(Rails.logger).to receive(:info)
          .with("Event registration cancelled: User #{user.id} cancelled registration for Event #{event.id}")

        service.send(:track_cancellation_event, event, user)
      end
    end
  end
end
