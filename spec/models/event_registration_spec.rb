require 'rails_helper'

RSpec.describe EventRegistration, type: :model do
  let(:user) { create(:user) }
  let(:event) { create(:event, :published, :upcoming, max_participants: 10) }

  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:event) }
  end

  describe 'validations' do
    subject { build(:event_registration, user: user, event: event) }

    it { should validate_presence_of(:status) }
    it { should validate_numericality_of(:amount_paid).is_greater_than_or_equal_to(0) }
    it { should validate_uniqueness_of(:user_id).scoped_to(:event_id).with_message('is already registered for this event') }

    describe 'amount_paid validation' do
      it 'allows zero amount' do
        registration = build(:event_registration, amount_paid: 0, user: user, event: event)
        expect(registration).to be_valid
      end

      it 'allows positive amount' do
        registration = build(:event_registration, amount_paid: 50.0, user: user, event: event)
        expect(registration).to be_valid
      end

      it 'rejects negative amount' do
        registration = build(:event_registration, amount_paid: -10.0, user: user, event: event)
        expect(registration).not_to be_valid
        expect(registration.errors[:amount_paid]).to include('must be greater than or equal to 0')
      end
    end

    describe 'uniqueness validation' do
      it 'prevents duplicate registrations for same user and event' do
        create(:event_registration, user: user, event: event)
        duplicate_registration = build(:event_registration, user: user, event: event)

        expect(duplicate_registration).not_to be_valid
        expect(duplicate_registration.errors[:user_id]).to include('is already registered for this event')
      end

      it 'allows same user to register for different events' do
        other_event = create(:event, :published, :upcoming)
        create(:event_registration, user: user, event: event)

        other_registration = build(:event_registration, user: user, event: other_event)
        expect(other_registration).to be_valid
      end

      it 'allows different users to register for same event' do
        other_user = create(:user)
        create(:event_registration, user: user, event: event)

        other_registration = build(:event_registration, user: other_user, event: event)
        expect(other_registration).to be_valid
      end
    end
  end

  describe 'enums' do
    it 'defines status enum with correct values' do
      expect(EventRegistration.statuses.keys).to match_array(['pending', 'confirmed', 'cancelled', 'attended', 'no_show'])
    end

    it 'allows setting status values' do
      registration = create(:event_registration, user: user, event: event)

      registration.pending!
      expect(registration.status).to eq('pending')

      registration.confirmed!
      expect(registration.status).to eq('confirmed')

      registration.cancelled!
      expect(registration.status).to eq('cancelled')

      registration.attended!
      expect(registration.status).to eq('attended')

      registration.no_show!
      expect(registration.status).to eq('no_show')
    end
  end

  describe 'scopes' do
    let!(:confirmed_registration) { create(:event_registration, :confirmed, user: user, event: event) }
    let!(:pending_registration) { create(:event_registration, :pending, event: event) }
    let!(:cancelled_registration) { create(:event_registration, :cancelled, event: event) }
    let!(:attended_registration) { create(:event_registration, :attended, event: event) }

    describe '.confirmed' do
      it 'returns only confirmed registrations' do
        expect(EventRegistration.confirmed).to include(confirmed_registration)
        expect(EventRegistration.confirmed).not_to include(pending_registration)
        expect(EventRegistration.confirmed).not_to include(cancelled_registration)
      end
    end

    describe '.attended' do
      it 'returns only attended registrations' do
        expect(EventRegistration.attended).to include(attended_registration)
        expect(EventRegistration.attended).not_to include(confirmed_registration)
        expect(EventRegistration.attended).not_to include(pending_registration)
      end
    end

    describe '.for_upcoming_events' do
      let!(:past_event) { create(:event, :upcoming) } # 先创建为未来事件
      let!(:past_registration) do
        # 先创建注册，然后将事件改为过去
        reg = create(:event_registration, event: past_event, user: create(:user))
        past_event.update(start_time: 1.week.ago, end_time: 1.week.ago + 2.hours)
        reg
      end

      it 'returns only registrations for upcoming events' do
        expect(EventRegistration.for_upcoming_events).to include(confirmed_registration)
        expect(EventRegistration.for_upcoming_events).not_to include(past_registration)
      end
    end

    describe '.recent' do
      let(:test_event) { create(:event, :upcoming) }
      let!(:old_registration) { create(:event_registration, registered_at: 1.week.ago, event: test_event, user: create(:user)) }
      let!(:new_registration) { create(:event_registration, registered_at: 1.hour.ago, event: test_event, user: create(:user)) }

      it 'returns registrations ordered by registered_at desc' do
        recent_registrations = EventRegistration.where(event: test_event).recent
        expect(recent_registrations.first).to eq(new_registration)
        expect(recent_registrations.last).to eq(old_registration)
      end
    end
  end

  describe 'callbacks' do
    describe 'before_create :set_registered_at' do
      it 'sets registered_at when creating registration' do
        registration = build(:event_registration, user: user, event: event, registered_at: nil)
        expect(registration.registered_at).to be_nil

        registration.save!
        expect(registration.registered_at).to be_present
        expect(registration.registered_at).to be_within(1.second).of(Time.current)
      end

      it 'does not override existing registered_at' do
        specific_time = 1.day.ago
        registration = build(:event_registration, user: user, event: event, registered_at: specific_time)

        registration.save!
        expect(registration.registered_at).to eq(specific_time)
      end
    end

    describe 'after_create :update_event_cache' do
      it 'touches the associated event to update cache' do
        # 使用 allow 而不是 expect，因为 touch 可能被多次调用
        allow(event).to receive(:touch)
        create(:event_registration, user: user, event: event)
        expect(event).to have_received(:touch)
      end
    end

    describe 'after_destroy :update_event_cache' do
      it 'touches the associated event when registration is destroyed' do
        registration = create(:event_registration, user: user, event: event)
        allow(event).to receive(:touch)
        registration.destroy
        expect(event).to have_received(:touch)
      end
    end
  end

  describe 'business logic validations' do
    describe 'event_not_full validation' do
      context 'when event has max_participants limit' do
        let(:limited_event) { create(:event, :published, :upcoming, max_participants: 2) }

        it 'allows registration when event is not full' do
          registration = build(:event_registration, user: user, event: limited_event)
          expect(registration).to be_valid
        end

        it 'prevents registration when event is full' do
          # Fill the event to capacity
          create_list(:event_registration, 2, event: limited_event)

          new_registration = build(:event_registration, user: user, event: limited_event)
          expect(new_registration).not_to be_valid
          expect(new_registration.errors[:base]).to include('Event is already full')
        end
      end

      context 'when event has no participant limit' do
        let(:unlimited_event) { create(:event, :published, :upcoming, max_participants: nil) }

        it 'allows registration regardless of existing registrations' do
          create_list(:event_registration, 100, event: unlimited_event)

          registration = build(:event_registration, user: user, event: unlimited_event)
          expect(registration).to be_valid
        end
      end
    end

    describe 'event_not_cancelled validation' do
      let(:cancelled_event) { create(:event, :cancelled) }

      it 'prevents registration for cancelled events' do
        registration = build(:event_registration, user: user, event: cancelled_event)
        expect(registration).not_to be_valid
        expect(registration.errors[:base]).to include('Cannot register for cancelled event')
      end

      it 'allows registration for non-cancelled events' do
        registration = build(:event_registration, user: user, event: event)
        expect(registration).to be_valid
      end

      it 'allows updating existing registration even if event is cancelled' do
        registration = create(:event_registration, user: user, event: event)
        event.update(status: 'cancelled')

        registration.notes = 'Updated notes'
        expect(registration.save).to be true
      end
    end

    describe 'event_not_past validation' do
      let(:past_event) { create(:event, :past) }

      it 'prevents registration for past events on create' do
        registration = build(:event_registration, user: user, event: past_event)
        expect(registration).not_to be_valid
        expect(registration.errors[:base]).to include('Cannot register for past event')
      end

      it 'allows registration for future events' do
        registration = build(:event_registration, user: user, event: event)
        expect(registration).to be_valid
      end

      it 'allows updating existing registration even if event is now past' do
        registration = create(:event_registration, user: user, event: event)

        # Simulate event becoming past
        event.update(start_time: 1.day.ago, end_time: 1.day.ago + 2.hours)

        registration.notes = 'Updated notes'
        expect(registration.save).to be true
      end
    end
  end

  describe 'edge cases and error handling' do
    it 'handles nil event gracefully in validations' do
      registration = build(:event_registration, user: user, event: nil)
      expect(registration).not_to be_valid
      expect(registration.errors[:event]).to include('must exist')
    end

    it 'handles nil user gracefully' do
      registration = build(:event_registration, user: nil, event: event)
      expect(registration).not_to be_valid
      expect(registration.errors[:user]).to include('must exist')
    end

    it 'sets default status if not provided' do
      registration = EventRegistration.new(user: user, event: event)
      registration.save!
      expect(registration.status).to eq('confirmed') # Based on factory default
    end

    it 'allows decimal amounts for amount_paid' do
      registration = build(:event_registration, user: user, event: event, amount_paid: 99.99)
      expect(registration).to be_valid
      expect(registration.amount_paid).to eq(99.99)
    end
  end

  describe 'factory validations' do
    it 'creates valid registration with factory' do
      registration = build(:event_registration, user: user, event: event)
      expect(registration).to be_valid
    end

    it 'creates valid registration with traits' do
      pending_registration = build(:event_registration, :pending, user: user, event: event)
      expect(pending_registration).to be_valid
      expect(pending_registration.status).to eq('pending')

      confirmed_registration = build(:event_registration, :confirmed, user: user, event: event)
      expect(confirmed_registration).to be_valid
      expect(confirmed_registration.status).to eq('confirmed')

      cancelled_registration = build(:event_registration, :cancelled, user: user, event: event)
      expect(cancelled_registration).to be_valid
      expect(cancelled_registration.status).to eq('cancelled')
    end
  end
end
