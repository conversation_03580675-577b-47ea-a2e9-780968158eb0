FactoryBot.define do
  factory :event_location do
    sequence(:name) { |n| "Location #{n}" }
    address { "123 Main Street" }
    city { "Toronto" }
    province { "Ontario" }
    country { "Canada" }
    latitude { 43.6532 }
    longitude { -79.3832 }
    directions { "Take the elevator to the 5th floor" }

    trait :online do
      name { "Online Event" }
      address { nil }
      city { nil }
      province { nil }
      country { nil }
      latitude { nil }
      longitude { nil }
      directions { "Meeting link will be provided before the event" }
    end

    trait :vancouver do
      name { "Vancouver Convention Center" }
      address { "1055 Canada Pl" }
      city { "Vancouver" }
      province { "British Columbia" }
      country { "Canada" }
      latitude { 49.2884 }
      longitude { -123.1103 }
    end

    trait :without_coordinates do
      latitude { nil }
      longitude { nil }
    end

    trait :with_events do
      after(:create) do |location|
        create_list(:event, 2, event_location: location)
      end
    end
  end
end
