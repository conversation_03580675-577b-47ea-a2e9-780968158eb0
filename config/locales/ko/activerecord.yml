# ActiveRecord 모델 및 속성 한국어 번역
ko:
  activerecord:
    models:
      user: "사용자"
      team: "팀"
      team_member: "팀 멤버"
      knowledge_base: "지식 베이스"
      post: "게시물"
      assistant: "어시스턴트"
      conversation: "대화"
      message: "메시지"
      base_unit: "기본 단위"
      subscription: "구독"
    attributes:
      user:
        name: "이름"
        email: "이메일"
        password: "비밀번호"
        bio: "소개"
        preferences: "환경 설정"
      team:
        name: "이름"
        description: "설명"
    errors:
      messages:
        blank: "비워둘 수 없습니다"
        taken: "이미 사용 중입니다"
        invalid: "유효하지 않습니다"
        too_short: "너무 짧습니다 (최소 %{count}자)"
        too_long: "너무 깁니다 (최대 %{count}자)"
        confirmation: "확인과 일치하지 않습니다"
