<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <!-- 面包屑导航 -->
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render 'shared/breadcrumb', items: { t('notifications.breadcrumbs.account') => settings_users_path, t('notifications.breadcrumbs.notifications') => notifications_path, t('notifications.breadcrumbs.notification_detail') => nil } %>

        <!-- 页面标题 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white"><%= t('notifications.show.title') %></h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= t('notifications.show.subtitle') %></p>
          </div>
          <div class="mt-4 md:mt-0">
            <a href="<%= notifications_path %>" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
              <%= t('notifications.show.back_to_all') %>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- 通知详情 -->
    <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-xs">
      <div class="p-6">
      <% 
        # 基于通知类型获取相关信息和图标
        icon_bg_color = 'bg-primary-700'
        
        if @notification.event.type == 'SystemNotifier'
          message = @notification.event.params[:message]
          icon_svg = '<path d="M8.707 7.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l2-2a1 1 0 00-1.414-1.414L11 7.586V3a1 1 0 10-2 0v4.586l-.293-.293z"></path><path d="M3 5a2 2 0 712-2h1a1 1 0 010 2H5v7h2l1 2h4l1-2h2V5h-1a1 1 0 110-2h1a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"></path>'
          notification_type = t('notifications.show.types.system')
          avatar_user = nil
          related_url = nil
        elsif @notification.event.type == 'EventReminderNotifier'
          event = Event.find(@notification.event.params[:event]) rescue nil
          message_parts = case @notification.event.params[:reminder_type]
                    when 'day_before'
                      { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.day_before') }
                    when 'hour_before'
                      { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.hour_before') }
                    when 'starting_soon'
                      { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.starting_soon') }
                    else
                      { bold: event&.title || t('notifications.show.event_messages.default_event'), text: " " + t('notifications.show.event_messages.reminder') }
                    end
          message = "<span class=\"font-semibold text-gray-900 dark:text-white\">#{message_parts[:bold]}</span>#{message_parts[:text]}"
          icon_svg = '<path d="M2 6a2 2 0 012-2h6a2 2 0 712 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>'
          notification_type = t('notifications.show.types.event_reminder')
          avatar_user = event&.user
          related_url = event ? event_path(event) : nil
        else
          message = t('notifications.show.types.notification')
          icon_svg = '<path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"</path>'
          notification_type = t('notifications.show.types.notification')
          avatar_user = nil
          related_url = nil
        end
      %>
      
      <div class="flex items-start space-x-4">
        <div class="flex-shrink-0 relative">
          <% if avatar_user %>
            <%= render 'shared/avatar', user: avatar_user, size: 16, alt_text: "User avatar", css_class: "rounded-full w-16 h-16" %>
          <% else %>
            <div class="inline-flex items-center justify-center w-16 h-16 overflow-hidden bg-gray-100 rounded-full dark:bg-gray-600">
              <span class="text-xl font-medium text-gray-600 dark:text-gray-300">SYS</span>
            </div>
          <% end %>
          <div class="absolute flex items-center justify-center w-6 h-6 -mt-6 ml-10 border-2 border-white rounded-full <%= icon_bg_color %> dark:border-gray-700">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><%= icon_svg.html_safe %></svg>
          </div>
        </div>
        
        <div class="flex-1 min-w-0">
          <div class="flex items-center justify-between">
            <p class="text-sm font-medium text-gray-900 dark:text-white"><%= notification_type %></p>
            <div class="flex items-center space-x-2">
              <% if @notification.read? %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                  <%= t('notifications.show.status.read') %>
                </span>
              <% else %>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                  <%= t('notifications.show.status.unread') %>
                </span>
              <% end %>
              <p class="text-sm text-gray-500 dark:text-gray-400"><%= t('common.time_ago', time: time_ago_in_words(@notification.created_at)) %></p>
            </div>
          </div>
          
          <div class="mt-4">
            <div class="text-gray-700 dark:text-gray-300 text-base leading-relaxed">
              <%= message.html_safe %>
            </div>
          </div>
          
          <% if related_url %>
            <div class="mt-6">
              <a href="<%= related_url %>" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <%= t('notifications.show.view_related_content') %>
                <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
          <% end %>
        </div>
      </div>
      
      <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <p><strong><%= t('notifications.show.notification_time') %></strong> <%= l(@notification.created_at, format: :notification) %></p>
          <% if @notification.read? %>
            <p class="mt-1"><strong><%= t('notifications.show.read_time') %></strong> <%= l(@notification.read_at, format: :notification) %></p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>
