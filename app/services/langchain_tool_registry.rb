class LangchainToolRegistry
  def available_tools
    available_tool_infos.map do |tool_info|
      begin
        klass = Object.const_get(tool_info[:name])
        klass.new
      rescue ArgumentError, NameError => e
        Rails.logger.error("Failed to initialize tool: #{tool_info[:name]}, error: #{e.message}")
        nil
      end
    end.compact
  end

  def available_tool_infos
    [
      {
        name: "Langchain::Tool::Calculator",
        title: "Calculator",
        description: "Perform mathematical calculations with precision"
      },
      {
        name: "Langchain::Tool::Wikipedia",
        title: "Wikipedia",
        description: "Look up information from Wikipedia encyclopedic entries"
      }
      # 可以根据需要添加更多工具
    ]
  end
end
