# Admin 功能开发计划

## 项目概述

本文档详细说明了为应用添加系统级 admin 功能的完整开发计划。第三阶段将使用 Administrate gem 替代 Rails Admin，提供更现代化和可定制的管理界面。

## 当前状态分析

### 现有 Admin 相关功能

1. **User 模型**：存在`admin?`方法但目前返回 false
2. **Team 系统**：有团队级别的 admin 角色，但非系统级别
3. **权限系统**：使用 action_policy gem，多个 policy 中有 admin 检查
4. **Admin 控制器**：`app/controllers/admin`目录存在但为空
5. **Routes**：有 admin namespace 但功能有限

### 需要实现的功能

1. 给 User 表添加 admin 字段
2. 实现设置用户为 admin 的功能
3. 集成 Administrate gem
4. 配置权限控制和安全性

## 第一阶段：数据库和模型层改进

### 1.1 添加 Admin 字段到 User 模型

```ruby
# db/migrate/add_admin_to_users.rb
class AddAdminToUsers < ActiveRecord::Migration[8.0]
  def change
    add_column :users, :admin, :boolean, default: false, null: false
    add_index :users, :admin
  end
end
```

### 1.2 更新 User 模型

```ruby
# app/models/user.rb
class User < ApplicationRecord
  # ... 现有代码 ...

  # 管理员权限检查
  def admin?
    admin == true
  end

  # 设置为管理员
  def make_admin!
    update!(admin: true)
  end

  # 取消管理员权限
  def remove_admin!
    update!(admin: false)
  end

  # 切换管理员状态
  def toggle_admin!
    update!(admin: !admin?)
  end

  # 作用域
  scope :admins, -> { where(admin: true) }
  scope :non_admins, -> { where(admin: false) }
end
```

### 1.3 更新 Seeds 文件

```ruby
# db/seeds.rb
# 创建初始admin用户
admin = User.find_or_create_by!(email_address: '<EMAIL>') do |user|
  user.username = 'admin'
  user.password_digest = BCrypt::Password.create('password123')
  user.phone_number = '+1234567890'
  user.phone_verified = true
  user.gender = 'other'
  user.location = 'San Francisco'
  user.hobbies = ['coding', 'reading']
  user.admin = true # 设置为管理员
end
```

## 第二阶段：Admin 配置和 Policy 更改

### 2.1 完成 Admin 配置

确保 User 模型的 admin 功能正常工作，并完成基础配置：

```ruby
# 验证admin功能
rails console
user = User.first
user.make_admin!
user.admin? # 应该返回true
```

### 2.2 更新所有 Policy 文件

在完成 admin 配置后，需要更新所有现有的 policy 文件以支持 admin 权限检查。

### 2.3 更新 Application Policy

```ruby
# app/policies/application_policy.rb
class ApplicationPolicy < ActionPolicy::Base
  authorize :user, allow_nil: true

  default_rule :manage?
  alias_rule :create?, :update?, :destroy?, to: :manage?

  protected

  def authenticated?
    user.present?
  end

  def admin?
    user&.admin?
  end

  def owner?
    return false unless user

    if record.respond_to?(:owner)
      record.owner == user
    elsif record.respond_to?(:user)
      record.user == user
    else
      false
    end
  end

  def check_authenticated!
    allow! if authenticated?
  end

  def check_admin!
    allow! if admin?
  end

  def check_ownership!
    allow! if owner?
  end
end
```

### 2.4 更新其他 Policy 文件

在所有现有的 policy 文件中添加 admin 权限检查：

```ruby
# 更新所有policy文件，添加admin检查
# 例如在 KnowledgeBasePolicy, EventPolicy, PostPolicy 等中：

def index?
  admin? || authenticated?
end

def show?
  admin? || (authenticated? && accessible?)
end

def create?
  admin? || authenticated?
end

def update?
  admin? || owner? || authorized_team_member?
end

def destroy?
  admin? || owner?
end
```

## 第三阶段：集成 Administrate 管理界面

### 3.1 添加 Administrate Gem

```ruby
# Gemfile
gem 'administrate', '~> 1.0.0.beta3'
```

### 3.2 安装和配置 Administrate

```bash
# 安装gem
bundle install

# 生成Administrate配置
rails generate administrate:install

# 为主要模型生成dashboards
rails generate administrate:dashboard User
rails generate administrate:dashboard Event
rails generate administrate:dashboard Post
rails generate administrate:dashboard KnowledgeBase
rails generate administrate:dashboard Team
```

### 3.3 配置 Admin 控制器

```ruby
# app/controllers/admin/application_controller.rb
class Admin::ApplicationController < Administrate::ApplicationController
  before_action :authenticate_user!
  before_action :ensure_admin!

  private

  def ensure_admin!
    redirect_to root_path, alert: '需要管理员权限' unless current_user.admin?
  end

  def current_user
    super
  end
end
```

### 3.4 自定义 User Dashboard

```ruby
# app/dashboards/user_dashboard.rb
require "administrate/base_dashboard"

class UserDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    username: Field::String,
    email_address: Field::Email,
    admin: Field::Boolean,
    phone_number: Field::String,
    phone_verified: Field::Boolean,
    gender: Field::Select.with_options(
      collection: User.genders.keys
    ),
    location: Field::String,
    hobbies: Field::Array,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    id
    username
    email_address
    admin
    created_at
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    username
    email_address
    admin
    phone_number
    phone_verified
    gender
    location
    hobbies
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    username
    email_address
    admin
    phone_number
    gender
    location
  ].freeze

  COLLECTION_FILTERS = {
    admin: ->(resources) { resources.where(admin: true) },
    regular: ->(resources) { resources.where(admin: false) },
    verified: ->(resources) { resources.where(phone_verified: true) }
  }.freeze

  def display_resource(user)
    user.username
  end
end
```

### 3.5 更新路由

```ruby
# config/routes.rb
Rails.application.routes.draw do
  # Administrate 路由
  namespace :admin do
    resources :users
    resources :events
    resources :posts
    resources :knowledge_bases
    resources :teams

    root to: "users#index"
  end

  # ... 现有路由 ...
end
```

### 3.6 自定义 Event Dashboard

```ruby
# app/dashboards/event_dashboard.rb
require "administrate/base_dashboard"

class EventDashboard < Administrate::BaseDashboard
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    title: Field::String,
    description: Field::Text,
    start_time: Field::DateTime,
    end_time: Field::DateTime,
    location: Field::String,
    max_participants: Field::Number,
    status: Field::Select.with_options(
      collection: Event.statuses.keys
    ),
    visibility: Field::Select.with_options(
      collection: Event.visibilities.keys
    ),
    published: Field::Boolean,
    archived: Field::Boolean,
    user: Field::BelongsTo,
    event_category: Field::BelongsTo,
    created_at: Field::DateTime,
    updated_at: Field::DateTime,
  }.freeze

  COLLECTION_ATTRIBUTES = %i[
    id
    title
    start_time
    status
    visibility
    user
    created_at
  ].freeze

  SHOW_PAGE_ATTRIBUTES = %i[
    id
    title
    description
    start_time
    end_time
    location
    max_participants
    status
    visibility
    published
    archived
    user
    event_category
    created_at
    updated_at
  ].freeze

  FORM_ATTRIBUTES = %i[
    title
    description
    start_time
    end_time
    location
    max_participants
    status
    visibility
    published
    archived
    event_category
  ].freeze

  COLLECTION_FILTERS = {
    published: ->(resources) { resources.where(published: true) },
    draft: ->(resources) { resources.where(published: false) },
    upcoming: ->(resources) { resources.where('start_time > ?', Time.current) },
    past: ->(resources) { resources.where('end_time < ?', Time.current) }
  }.freeze

  def display_resource(event)
    event.title
  end
end
```

## 第四阶段：管理员功能界面

### 4.1 创建自定义 Admin 视图

```erb
<!-- app/views/admin/application/_navigation.html.erb -->
<nav class="navigation" role="navigation">
  <div class="navigation__brand">
    <%= link_to "AI Pro CGC Admin", admin_root_path, class: "navigation__link" %>
  </div>

  <div class="navigation__links">
    <%= link_to "用户管理", admin_users_path, class: "navigation__link" %>
    <%= link_to "活动管理", admin_events_path, class: "navigation__link" %>
    <%= link_to "文章管理", admin_posts_path, class: "navigation__link" %>
    <%= link_to "知识库", admin_knowledge_bases_path, class: "navigation__link" %>
    <%= link_to "团队管理", admin_teams_path, class: "navigation__link" %>
    <%= link_to "返回前台", root_path, class: "navigation__link" %>
    <%= link_to "退出", sign_out_path, method: :delete, class: "navigation__link" %>
  </div>
</nav>
```

### 4.2 添加管理员操作

```ruby
# app/controllers/admin/users_controller.rb
class Admin::UsersController < Admin::ApplicationController
  def toggle_admin
    user = User.find(params[:id])
    if user == current_user
      redirect_back(fallback_location: admin_users_path, alert: '不能修改自己的管理员状态')
    else
      user.toggle_admin!
      redirect_back(fallback_location: admin_users_path,
                   notice: "用户#{user.admin? ? '设置为' : '取消'}管理员成功")
    end
  end

  private

  def resource_params
    params.require(:user).permit(:username, :email_address, :admin, :phone_number, :gender, :location)
  end
end
```

### 4.3 添加自定义路由动作

```ruby
# config/routes.rb (更新admin部分)
namespace :admin do
  resources :users do
    member do
      patch :toggle_admin
    end
  end
  resources :events
  resources :posts
  resources :knowledge_bases
  resources :teams

  root to: "users#index"
end
```

## 第五阶段：安全性和测试

### 5.1 安全性配置

```ruby
# config/environments/production.rb
# 确保在生产环境中Admin只能通过HTTPS访问
config.force_ssl = true

# 配置Content Security Policy
config.content_security_policy do |policy|
  policy.default_src :self, :https
  policy.script_src  :self, :https, :unsafe_inline
  policy.style_src   :self, :https, :unsafe_inline
end
```

### 5.2 创建测试

```ruby
# spec/models/user_spec.rb
RSpec.describe User, type: :model do
  describe '#admin?' do
    it 'returns true when admin field is true' do
      user = create(:user, admin: true)
      expect(user.admin?).to be true
    end

    it 'returns false when admin field is false' do
      user = create(:user, admin: false)
      expect(user.admin?).to be false
    end
  end

  describe '#make_admin!' do
    it 'sets admin to true' do
      user = create(:user, admin: false)
      user.make_admin!
      expect(user.admin?).to be true
    end
  end

  describe '#remove_admin!' do
    it 'sets admin to false' do
      user = create(:user, admin: true)
      user.remove_admin!
      expect(user.admin?).to be false
    end
  end
end
```

### 5.3 创建 Feature 测试

```ruby
# spec/features/admin/user_management_spec.rb
require 'rails_helper'

RSpec.feature 'Admin User Management', type: :feature do
  let(:admin) { create(:user, :admin) }
  let(:regular_user) { create(:user) }

  before do
    sign_in admin
  end

  scenario 'Admin can view users list' do
    visit admin_users_path
    expect(page).to have_content('用户管理')
    expect(page).to have_content(admin.username)
    expect(page).to have_content(regular_user.username)
  end

  scenario 'Admin can make a user admin' do
    visit admin_users_path
    within("tr[data-user-id='#{regular_user.id}']") do
      click_link '设置为管理员'
    end
    expect(page).to have_content('用户设置为管理员成功')
    expect(regular_user.reload.admin?).to be true
  end

  scenario 'Admin cannot modify their own admin status' do
    visit admin_user_path(admin)
    expect(page).not_to have_link('取消管理员')
  end

  scenario 'Regular user cannot access admin panel' do
    sign_out
    sign_in regular_user
    visit admin_users_path
    expect(page).to have_content('需要管理员权限')
    expect(current_path).to eq(root_path)
  end
end
```

## 实施时间表

### 第一周：数据库和模型改进

- [ ] 创建 admin 字段迁移
- [ ] 更新 User 模型
- [ ] 更新 seeds 文件
- [ ] 测试基本 admin 功能

### 第二周：Admin 配置和 Policy 更改

- [ ] 完成 admin 配置验证
- [ ] 更新 Application Policy
- [ ] 更新所有现有 Policy 文件
- [ ] 测试权限控制

### 第三周：Administrate 集成

- [ ] 添加 Administrate gem
- [ ] 生成基础配置
- [ ] 自定义 User 和 Event dashboards
- [ ] 配置路由和控制器

### 第四周：管理界面开发

- [ ] 创建自定义 Admin 视图
- [ ] 实现管理员操作功能
- [ ] 添加导航和界面优化
- [ ] 测试管理界面

### 第五周：安全性和测试

- [ ] 配置安全性设置
- [ ] 编写单元测试
- [ ] 编写 feature 测试
- [ ] 部署和最终测试

## 注意事项

1. **安全性**：确保只有 admin 用户才能访问管理功能
2. **数据迁移**：小心处理现有数据，确保 admin 字段正确设置
3. **权限检查**：在所有相关的 controller 和 policy 中添加 admin 检查
4. **Administrate 优势**：相比 Rails Admin 更现代化、可定制性更强
5. **性能**：对于大量数据，考虑添加索引和分页
6. **备份**：在部署前确保数据库备份

## Administrate vs Rails Admin

### 选择 Administrate 的原因

1. **现代化设计**：更好的用户界面和用户体验
2. **高度可定制**：Dashboard 文件提供更灵活的配置
3. **Rails 8 兼容性**：更好的 Rails 8 支持
4. **维护活跃**：社区更活跃，更新更频繁
5. **性能更好**：更轻量级，加载速度更快

### 迁移优势

- 更好的移动端支持
- 更直观的管理界面
- 更容易自定义样式
- 更好的搜索和过滤功能
- 更强的扩展性

## 后续扩展

1. **角色系统**：可以考虑实现更复杂的角色权限系统
2. **审计日志**：添加 admin 操作的审计记录
3. **批量操作**：在 Administrate 中添加批量操作功能
4. **仪表板**：扩展 admin 仪表板的功能和统计信息
5. **API 管理**：为 admin 功能添加 API 接口

这个更新后的计划提供了一个完整的 admin 功能实现路径，重点是在第二阶段完成 policy 更改，第三阶段使用现代化的 Administrate gem 替代 Rails Admin。
