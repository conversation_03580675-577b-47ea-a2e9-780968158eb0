Rails.application.config.middleware.use OmniAuth::Builder do
  # 开发环境使用
  provider :developer if Rails.env.development?

  # 微信登录
  provider :wechat,
    Rails.application.credentials.dig(:oauth, :wechat, :app_id),
    Rails.application.credentials.dig(:oauth, :wechat, :app_secret),
    scope: "snsapi_login"

  # GitHub登录
  provider :github,
    Rails.application.credentials.dig(:oauth, :github, :client_id),
    Rails.application.credentials.dig(:oauth, :github, :client_secret),
    scope: "user:email"

  # Google登录
  provider :google_oauth2,
    Rails.application.credentials.dig(:oauth, :google, :client_id),
    Rails.application.credentials.dig(:oauth, :google, :client_secret)
end

# 配置错误处理
OmniAuth.config.on_failure = Proc.new { |env|
  OmniAuth::FailureEndpoint.new(env).redirect_to_failure
}
