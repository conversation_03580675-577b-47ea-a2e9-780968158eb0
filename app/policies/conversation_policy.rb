class ConversationPolicy < ApplicationPolicy
  pre_check :check_authenticated!
  # Apply owner check only to manage? via pre_check if needed,
  # or handle ownership within manage? rule itself.
  # Let's remove the pre_check for owner? for now and handle it explicitly.
  # pre_check :owner?, only: %i[manage?] # Example if needed for manage?

  # Explicit rule for showing a conversation
  def show?
    # Admin can view all conversations, otherwise check ownership
    return true if admin?
    # Explicitly deny if not owner, otherwise implicitly allow
    deny! unless owner?
    # If deny! is not called, authorization passes.
    # No explicit 'allow!' or 'true' needed here if deny! covers the failure case.
  end

  def create?
    admin? || authenticated?
  end

  def update?
    admin? || owner?
  end

  def destroy?
    admin? || owner?
  end

  # 关联范围：用户只能访问自己的对话
  relation_scope do |relation|
    return relation if user&.admin?  # Admin可以看到所有对话
    next relation.none unless user
    relation.where(user_id: user.id)
  end

  private

  def owner?
    # Ensure owner? returns true/false
    record.user == user
  end
end
