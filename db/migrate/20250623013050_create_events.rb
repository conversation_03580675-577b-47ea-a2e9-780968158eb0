class CreateEvents < ActiveRecord::Migration[8.0]
  def change
    create_table :events do |t|
      t.string :title, null: false
      t.text :description
      t.text :prerequisites
      t.datetime :start_time, null: false
      t.datetime :end_time, null: false
      t.decimal :price, precision: 8, scale: 2, default: 0
      t.decimal :member_price, precision: 8, scale: 2
      t.integer :max_participants
      t.integer :min_participants
      t.string :status, default: 'draft'
      t.integer :visibility, default: 2  # 使用整数匹配 Visibility concern: 0=private, 1=team, 2=public
      t.string :difficulty_level
      t.text :what_to_bring
      t.text :what_included
      t.boolean :is_online, default: false
      t.string :meeting_link
      t.boolean :archived, default: false
      t.datetime :published_at
      t.references :user, null: false, foreign_key: true
      t.references :event_category, foreign_key: true
      t.references :event_location, foreign_key: true

      t.timestamps
    end

    add_index :events, :status
    add_index :events, :visibility
    add_index :events, :archived
    add_index :events, :start_time
    add_index :events, [ :start_time, :end_time ]
    add_index :events, [ :status, :visibility, :archived ]
  end
end
