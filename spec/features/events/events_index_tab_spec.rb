require 'rails_helper'

RSpec.feature 'Events Index Tabs', type: :feature do
  let(:user) { create(:user) }
  let(:event_creator) { create(:user) }

  # 创建测试数据
  let!(:upcoming_event) { create(:event, :published, :upcoming, user: event_creator, title: 'Upcoming Workshop') }
  let!(:past_event) { create(:event, :published, :past, user: event_creator, title: 'Past Conference') }
  let!(:draft_event) { create(:event, :draft, user: event_creator, title: 'Draft Event') }

  feature 'Tab Navigation' do
    scenario 'shows tabs navigation' do
      visit events_path

      expect(page).to have_selector('ul[role="tablist"]')
      expect(page).to have_link(I18n.t('events.index.tabs.upcoming'))
      expect(page).to have_link(I18n.t('events.index.tabs.past'))
      expect(page).to have_link(I18n.t('events.index.tabs.all'))
    end

    scenario 'defaults to upcoming events tab' do
      visit events_path

      expect(page).to have_content(upcoming_event.title)
      expect(page).not_to have_content(past_event.title)
      expect(page).not_to have_content(draft_event.title)

      # Check active tab styling
      expect(page).to have_selector('a.text-primary-600', text: I18n.t('events.index.tabs.upcoming'))
    end

    scenario 'switches to past events tab' do
      visit events_path(tab: 'past')

      expect(page).to have_content(past_event.title)
      expect(page).not_to have_content(upcoming_event.title)
      expect(page).not_to have_content(draft_event.title)

      # Check active tab styling
      expect(page).to have_selector('a.text-primary-600', text: I18n.t('events.index.tabs.past'))
    end

    scenario 'switches to all events tab' do
      visit events_path(tab: 'all')

      expect(page).to have_content(upcoming_event.title)
      expect(page).to have_content(past_event.title)
      expect(page).not_to have_content(draft_event.title) # Draft events still hidden

      # Check active tab styling
      expect(page).to have_selector('a.text-primary-600', text: I18n.t('events.index.tabs.all'))
    end

    scenario 'clicking tab links navigates correctly' do
      visit events_path

      click_link I18n.t('events.index.tabs.past')

      expect(current_url).to include('tab=past')
      expect(page).to have_content(past_event.title)
      expect(page).not_to have_content(upcoming_event.title)

      click_link I18n.t('events.index.tabs.all')

      expect(current_url).to include('tab=all')
      expect(page).to have_content(upcoming_event.title)
      expect(page).to have_content(past_event.title)
    end
  end

  feature 'Search with Tabs' do
    scenario 'preserves current tab when searching' do
      visit events_path(tab: 'past')

      fill_in 'search', with: past_event.title
      click_button I18n.t('events.search.filters')

      expect(current_url).to include('tab=past')
      expect(page).to have_content(past_event.title)
      expect(page).not_to have_content(upcoming_event.title)
    end

    scenario 'filters within current tab' do
      # Create events with same category but different times
      category = create(:event_category, name: 'Technology')
      create(:event, :published, :upcoming, title: 'Future Tech Talk', event_category: category)
      create(:event, :published, :past, title: 'Past Tech Talk', event_category: category)

      # Visit past tab and filter by category
      visit events_path(tab: 'past')
      select 'Technology', from: 'category_id'
      click_button I18n.t('events.search.filters')

      expect(page).to have_content('Past Tech Talk')
      expect(page).not_to have_content('Future Tech Talk')
      expect(current_url).to include('tab=past')
    end
  end

  feature 'Empty States' do
    background do
      # Remove all events to test empty states
      Event.destroy_all
    end

    scenario 'shows correct empty state for upcoming tab' do
      visit events_path(tab: 'upcoming')

      expect(page).to have_content(I18n.t('events.empty_states.no_upcoming'))
    end

    scenario 'shows correct empty state for past tab' do
      visit events_path(tab: 'past')

      expect(page).to have_content(I18n.t('events.empty_states.no_past'))
    end

    scenario 'shows correct empty state for all tab' do
      visit events_path(tab: 'all')

      expect(page).to have_content(I18n.t('events.empty_states.no_all'))
    end
  end

  feature 'Accessibility' do
    scenario 'tab navigation is accessible' do
      visit events_path

      expect(page).to have_selector('ul[role="tablist"]')
      expect(page).to have_selector('a[role="tab"]', count: 3)
    end
  end
end
