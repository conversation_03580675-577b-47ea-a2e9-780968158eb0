require 'rails_helper'

RSpec.describe EventLocation, type: :model do
  let(:location) { build(:event_location) }

  describe 'associations' do
    it { should have_many(:events).dependent(:destroy) }
  end

  describe 'validations' do
    subject { build(:event_location) }

    it { should validate_presence_of(:name) }
    it { should validate_length_of(:name).is_at_most(255) }

    describe 'name validation' do
      it 'is valid with a proper name' do
        location.name = 'Convention Center'
        expect(location).to be_valid
      end

      it 'is invalid without a name' do
        location.name = nil
        expect(location).not_to be_valid
        expect(location.errors[:name]).to include("can't be blank")
      end

      it 'is invalid with a name longer than 255 characters' do
        location.name = 'a' * 256
        expect(location).not_to be_valid
        expect(location.errors[:name]).to include('is too long (maximum is 255 characters)')
      end
    end

    describe 'latitude validation' do
      it 'is valid with latitude within valid range' do
        location.latitude = 45.5
        expect(location).to be_valid
      end

      it 'is valid with nil latitude' do
        location.latitude = nil
        expect(location).to be_valid
      end

      it 'is invalid with latitude above 90' do
        location.latitude = 91.0
        expect(location).not_to be_valid
        expect(location.errors[:latitude]).to include('must be in -90.0..90.0')
      end

      it 'is invalid with latitude below -90' do
        location.latitude = -91.0
        expect(location).not_to be_valid
        expect(location.errors[:latitude]).to include('must be in -90.0..90.0')
      end

      it 'is valid with latitude at boundaries' do
        location.latitude = 90.0
        expect(location).to be_valid

        location.latitude = -90.0
        expect(location).to be_valid
      end
    end

    describe 'longitude validation' do
      it 'is valid with longitude within valid range' do
        location.longitude = -79.5
        expect(location).to be_valid
      end

      it 'is valid with nil longitude' do
        location.longitude = nil
        expect(location).to be_valid
      end

      it 'is invalid with longitude above 180' do
        location.longitude = 181.0
        expect(location).not_to be_valid
        expect(location.errors[:longitude]).to include('must be in -180.0..180.0')
      end

      it 'is invalid with longitude below -180' do
        location.longitude = -181.0
        expect(location).not_to be_valid
        expect(location.errors[:longitude]).to include('must be in -180.0..180.0')
      end

      it 'is valid with longitude at boundaries' do
        location.longitude = 180.0
        expect(location).to be_valid

        location.longitude = -180.0
        expect(location).to be_valid
      end
    end
  end

  describe 'scopes' do
    let!(:toronto_location) { create(:event_location, city: 'Toronto', country: 'Canada') }
    let!(:vancouver_location) { create(:event_location, :vancouver) }
    let!(:online_location) { create(:event_location, :online) }
    let!(:location_with_coords) { create(:event_location, latitude: 45.0, longitude: -75.0) }
    let!(:location_without_coords) { create(:event_location, :without_coordinates) }

    describe '.by_city' do
      it 'returns locations in specified city' do
        toronto_locations = EventLocation.by_city('Toronto')
        expect(toronto_locations).to include(toronto_location)
        expect(toronto_locations).not_to include(vancouver_location)
      end

      it 'returns empty result for non-existent city' do
        expect(EventLocation.by_city('Non-existent City')).to be_empty
      end
    end

    describe '.by_country' do
      it 'returns locations in specified country' do
        canada_locations = EventLocation.by_country('Canada')
        expect(canada_locations).to include(toronto_location)
        expect(canada_locations).to include(vancouver_location)
        expect(canada_locations).not_to include(online_location)
      end

      it 'returns empty result for non-existent country' do
        expect(EventLocation.by_country('Non-existent Country')).to be_empty
      end
    end

    describe '.with_coordinates' do
      it 'returns only locations with both latitude and longitude' do
        locations_with_coords = EventLocation.with_coordinates
        expect(locations_with_coords).to include(toronto_location)
        expect(locations_with_coords).to include(vancouver_location)
        expect(locations_with_coords).to include(location_with_coords)
        expect(locations_with_coords).not_to include(online_location)
        expect(locations_with_coords).not_to include(location_without_coords)
      end
    end
  end

  describe 'instance methods' do
    describe '#full_address' do
      it 'returns complete address when all fields are present' do
        location.address = '123 Main St'
        location.city = 'Toronto'
        location.province = 'Ontario'
        location.country = 'Canada'

        expect(location.full_address).to eq('123 Main St, Toronto, Ontario, Canada')
      end

      it 'handles missing address components gracefully' do
        location.address = '123 Main St'
        location.city = 'Toronto'
        location.province = nil
        location.country = 'Canada'

        expect(location.full_address).to eq('123 Main St, Toronto, Canada')
      end

      it 'returns empty string when all components are nil' do
        location.address = nil
        location.city = nil
        location.province = nil
        location.country = nil

        expect(location.full_address).to eq('')
      end

      it 'handles only city and country' do
        location.address = nil
        location.city = 'Vancouver'
        location.province = nil
        location.country = 'Canada'

        expect(location.full_address).to eq('Vancouver, Canada')
      end
    end

    describe '#has_coordinates?' do
      it 'returns true when both latitude and longitude are present' do
        location.latitude = 43.6532
        location.longitude = -79.3832
        expect(location.has_coordinates?).to be true
      end

      it 'returns false when latitude is missing' do
        location.latitude = nil
        location.longitude = -79.3832
        expect(location.has_coordinates?).to be false
      end

      it 'returns false when longitude is missing' do
        location.latitude = 43.6532
        location.longitude = nil
        expect(location.has_coordinates?).to be false
      end

      it 'returns false when both coordinates are missing' do
        location.latitude = nil
        location.longitude = nil
        expect(location.has_coordinates?).to be false
      end

      it 'returns false when coordinates are zero' do
        location.latitude = 0
        location.longitude = 0
        expect(location.has_coordinates?).to be true # 0,0 is a valid coordinate
      end
    end

    describe '#to_coordinates' do
      it 'returns array of coordinates when both are present' do
        location.latitude = 43.6532
        location.longitude = -79.3832
        expect(location.to_coordinates).to eq([43.6532, -79.3832])
      end

      it 'returns nil when coordinates are missing' do
        location.latitude = nil
        location.longitude = -79.3832
        expect(location.to_coordinates).to be_nil
      end

      it 'converts string coordinates to float' do
        location.latitude = '43.6532'
        location.longitude = '-79.3832'
        expect(location.to_coordinates).to eq([43.6532, -79.3832])
      end

      it 'handles zero coordinates' do
        location.latitude = 0
        location.longitude = 0
        expect(location.to_coordinates).to eq([0.0, 0.0])
      end
    end
  end

  describe 'factory validations' do
    it 'creates valid location with factory' do
      location = build(:event_location)
      expect(location).to be_valid
    end

    it 'creates valid location with traits' do
      online_location = build(:event_location, :online)
      expect(online_location).to be_valid
      expect(online_location.name).to eq('Online Event')

      vancouver_location = build(:event_location, :vancouver)
      expect(vancouver_location).to be_valid
      expect(vancouver_location.city).to eq('Vancouver')

      location_without_coords = build(:event_location, :without_coordinates)
      expect(location_without_coords).to be_valid
      expect(location_without_coords.has_coordinates?).to be false
    end
  end

  describe 'edge cases' do
    it 'handles very precise coordinates' do
      location.latitude = 43.653226
      location.longitude = -79.383184
      expect(location).to be_valid
      expect(location.to_coordinates).to eq([43.653226, -79.383184])
    end

    it 'handles unicode characters in address fields' do
      location.address = '北京市朝阳区'
      location.city = '北京'
      location.country = '中国'
      expect(location).to be_valid
      expect(location.full_address).to include('北京市朝阳区')
    end

    it 'handles empty strings vs nil for address components' do
      location.address = ''
      location.city = 'Toronto'
      location.province = ''
      location.country = 'Canada'

      # Empty strings should be treated as nil by compact
      expect(location.full_address).to eq('Toronto, Canada')
    end
  end

  describe 'dependent destroy' do
    it 'destroys associated events when location is destroyed' do
      location = create(:event_location, :with_events)
      expect { location.destroy }.to change(Event, :count).by(-2)
    end
  end
end
