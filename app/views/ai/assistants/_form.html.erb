<div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-xs dark:border-gray-700 sm:p-6 dark:bg-gray-800">
  <%= form_with(model: [:ai, assistant], class: "space-y-6", data: { controller: "assistant-form" }) do |form| %>
    <% if assistant.errors.any? %>
      <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400" role="alert">
        <div class="font-medium"><%= t('shared.error_messages.form_error', count: assistant.errors.count) %></div>
        <ul class="mt-1.5 list-disc list-inside">
          <% assistant.errors.each do |error| %>
            <li><%= error.full_message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <!-- 名称输入 -->
    <div>
      <%= form.label :name, t('ai.assistants.form.name_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <%= form.text_field :name, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500", placeholder: t('ai.assistants.form.name_placeholder'), required: true %>
    </div>

    <!-- 指令输入 -->
    <div data-controller="slash-commands" data-slash-commands-providers-value='["prompt"]' data-visual-prompts="true">
      <%= form.label :instructions, t('ai.assistants.form.instructions_label'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <div class="relative">
        <%= form.text_area :instructions, rows: 6, data: { slash_commands_target: "input" },
                       class: "block p-2.5 w-full text-sm text-gray-900 bg-gray-50 rounded-lg border border-gray-300 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
                       placeholder: t('ai.assistants.form.instructions_placeholder') %>
        <div data-slash-commands-target="suggestions" class="hidden absolute z-10 w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg mt-1 shadow-lg max-h-60 overflow-y-auto"></div>
      </div>
      <div class="visual-prompts-container mt-2"></div>
    </div>

    <!-- 工具使用模式 - 移到工具选择区域之前 -->
    <div>
      <%= form.label :tool_choice, t('ai.assistants.form.tool_usage_mode'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
      <%= form.select :tool_choice,
          options_for_select([
            [t('ai.assistants.form.tool_choices.auto'), "auto"],
            [t('ai.assistants.form.tool_choices.none'), "none"],
            [t('ai.assistants.form.tool_choices.any'), "any"]
          ], assistant.tool_choice),
          {},
          {
            class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-800 dark:border-gray-700 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500",
            data: {
              assistant_form_target: "toolChoice",
              action: "change->assistant-form#toggleToolsSection"
            }
          } %>
    </div>

    <!-- 工具选择区域 -->
    <div id="tools-section"
         style="<%= 'display: none;' if assistant.tool_choice == 'none' %>"
         class="p-4 border border-gray-200 rounded-lg dark:border-gray-700 bg-gray-50 dark:bg-gray-900"
         data-assistant-form-target="toolsSection">
      <!-- Label -->
      <label class="block mb-4 text-sm font-medium text-gray-900 dark:text-white"><%= t('ai.assistants.form.available_tools') %></label>

      <!-- Flowbite Toggle -->
      <div class="mb-4">
        <label class="inline-flex items-center cursor-pointer">
          <input type="checkbox"
                  id="select_all_tools"
                  data-action="change->assistant-form#selectAllTools"
                  class="sr-only peer">
          <div class="relative w-11 h-6 bg-gray-200 rounded-full peer dark:bg-gray-700 dark:border-gray-600 peer-focus:outline-hidden peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:after:bg-white dark:after:border-gray-600"></div>
          <span class="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300"><%= t('ai.assistants.form.select_all') %></span>
        </label>
      </div>

      <!-- 工具复选框组 -->
      <div class="space-y-2">
        <% @tool_infos.each do |tool_info| %>
          <div class="flex p-3 border border-gray-200 rounded-lg shadow-xs dark:bg-gray-800 dark:border-gray-700">
            <div class="flex h-5 items-center">
              <input type="checkbox"
                     id="tool_<%= tool_info[:name] %>"
                     name="assistant[tools][]"
                     value="<%= tool_info[:name] %>"
                     class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 checked:bg-blue-600 dark:checked:bg-blue-600"
                     data-assistant-form-target="toolCheckbox"
                     <%= 'checked' if assistant.tools&.include?(tool_info[:name]) %>>
            </div>
            <div class="ms-3 text-sm">
              <label for="tool_<%= tool_info[:name] %>" class="font-medium text-gray-900 dark:text-white">
                <%= tool_info[:title] %>
              </label>
              <p class="text-xs font-normal text-gray-500 dark:text-gray-400">
                <%= tool_info[:description] %>
              </p>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- 表单按钮 -->
    <div class="flex items-center space-x-4">
      <%= form.submit (assistant.persisted? ? t('ai.assistants.form.update_button') : t('ai.assistants.form.create_button')), class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" %>
      <%= link_to t('ai.assistants.form.cancel_button'), ai_assistants_path, class: "text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-gray-600 dark:text-white dark:hover:bg-gray-700 dark:focus:ring-gray-800" %>
    </div>
  <% end %>
</div>
