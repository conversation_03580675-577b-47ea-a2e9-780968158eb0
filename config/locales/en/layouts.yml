# English translations for layouts and navigation components
en:
  layouts:
    public: # For application.html.erb, _navbar.html.erb, _footer.html.erb
      navbar:
        courses: "Courses"
        tutorials: "Tutorials"
        blog: "Blog"
        # About/Contact links use static_pages keys
      footer:
        description: "Empowering women through digital education, creating more possibilities through coding."
        download:
          title: "Download"
          ios: "iOS App"
          android: "Android App"
        learning_resources: "Learning Resources"
        resources:
          courses: "Courses"
          tutorials: "Tutorials"
        legal: "Legal"
        blog: "Blog"
        about_us: "About Us" # Footer specific title
        contact: "Contact" # Added contact key
    dashboard: # For user_dashboard.html.erb, _navbar_dashboard.html.erb, _footer_dashboard.html.erb, _sidebar.html.erb
      navbar:
        toggle_sidebar: "Toggle sidebar"
        search:
          sr_label: "Search"
          placeholder: "Search"
        notifications:
          sr_label: "View notifications"
        apps:
          sr_label: "View apps"
        theme:
          toggle_tooltip: "Toggle theme mode"
        user_menu:
          sr_label: "Open user menu"
          avatar_alt: "User avatar"
          guest: "Guest"
          not_logged_in: "Not logged in"
          dashboard: "Dashboard" # Reuse sidebar.dashboard
          settings: "Settings" # Reuse users.settings.title
          # Sign Out uses auth.sign_out
      sidebar: # Merged sidebar keys
        search:
          placeholder: "Search"
          mobile_label: "Mobile search"
          sr_label: "Search" # Reuse dashboard.navbar.search.sr_label
        dashboard: "Dashboard"
        # Main menu items reuse functional module keys (e.g., posts.index.title)
        ai:
          title: "AI" # Title for the collapsible section
          # assistants: Reuse ai.assistants.title
          # chat: Reuse ai.conversations.title
        knowledge_bases:
          title: "Knowledge Bases" # Title for the collapsible section
          # all: Reuse knowledge_bases.index.title
          # archived: Reuse knowledge_bases.index.archived_title
          # new: Reuse knowledge_bases.actions.create
        account_settings: # Renamed from settings
          title: "Account Settings" # Title for the collapsible section
          # profile: Reuse users.profile.title
          # subscriptions: Reuse users.subscriptions.title
        my_post: "My Posts"
        base_units: "Base Units"
        language_selector: "Select Language"
      footer:
        # Links reuse static_pages and contact keys
        contact: "Contact" # Added contact key
