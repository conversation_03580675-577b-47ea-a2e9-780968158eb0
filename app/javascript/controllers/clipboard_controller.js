import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["defaultMessage", "successMessage"]

  connect() {
    // Find the button and the hidden content source within this controller's element
    const triggerButton = this.element.querySelector('button[data-clipboard-target="trigger"]');
    const contentSource = this.element.querySelector('[data-clipboard-content]'); // Target the hidden div

    if (!triggerButton) {
      console.error("Clipboard controller could not find the trigger button.");
      return;
    }
    if (!contentSource) {
      console.error("Clipboard controller could not find the [data-clipboard-content] element.");
      return;
    }

    // Initialize CopyClipboard library instance
    // Assuming CopyClipboard is available globally or via import
    // Note: The original commit 2639a0f1 implicitly assumed CopyClipboard exists.
    // We add a check here for robustness.
    if (typeof CopyClipboard !== 'undefined') {
      this.clipboard = new CopyClipboard(
        triggerButton,
        contentSource,
        {
          contentType: 'textContent', // Copy the text content of the hidden div
          onCopy: () => this.handleCopy() // Callback on success
        }
      );
    } else {
      console.error("CopyClipboard library is not defined. Cannot initialize clipboard functionality.");
      // Optionally disable the button or provide visual feedback
      // triggerButton.disabled = true;
    }
  }

  // Optional: disconnect logic if CopyClipboard needs cleanup
  // disconnect() {
  //   if (this.clipboard && typeof this.clipboard.destroy === 'function') {
  //     this.clipboard.destroy();
  //   }
  // }

  handleCopy() {
    // Show success feedback
    if (this.hasDefaultMessageTarget && this.hasSuccessMessageTarget) {
      this.defaultMessageTarget.classList.add('hidden');
      this.successMessageTarget.classList.remove('hidden');

      // Reset feedback after a delay
      setTimeout(() => {
        if (this.hasDefaultMessageTarget && this.hasSuccessMessageTarget) {
          this.defaultMessageTarget.classList.remove('hidden');
          this.successMessageTarget.classList.add('hidden');
        }
      }, 2000); // 2 seconds delay
    } else {
      console.warn("Clipboard feedback targets not found.");
    }
  }
}
