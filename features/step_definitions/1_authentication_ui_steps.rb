Given('I visit the login page') do
  visit new_session_path
end

Given('I visit the password reset page') do
  visit new_password_path
end

Then('I should see a shadcn styled email input') do
  expect(page).to have_field('user_email_address')
end

Then('I should see a shadcn styled password input') do
  expect(page).to have_field('user_password')
end

Then('I should see a shadcn styled submit button') do
  expect(page).to have_css('button[type="submit"]')
end
