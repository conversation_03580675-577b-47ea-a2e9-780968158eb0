# English translations for ActiveRecord models and attributes
en:
  activerecord:
    models:
      user: "User"
      team: "Team"
      team_member: "Team Member"
      knowledge_base: "Knowledge Base"
      post: "Post"
      assistant: "Assistant"
      conversation: "Conversation"
      message: "Message"
      base_unit: "Base Unit"
      subscription: "Subscription"
    attributes:
      user:
        name: "Name"
        email: "Email"
        password: "Password"
        bio: "Bio"
        preferences: "Preferences"
      team:
        name: "Name"
        description: "Description"
    errors:
      messages:
        blank: "can't be blank"
        taken: "has already been taken"
        invalid: "is invalid"
        too_short: "is too short (minimum is %{count} characters)"
        too_long: "is too long (maximum is %{count} characters)"
        confirmation: "doesn't match confirmation"
