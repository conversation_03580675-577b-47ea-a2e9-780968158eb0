<% if @user_message.persisted? %>
  <%= turbo_stream.append "conversation_messages" do %>
    <%= render "ai/messages/message_user", message: @user_message %>
    <%= render "ai/messages/message_assistant", message: @assistant_message %>
  <% end %>
<% else %>
  <%= turbo_stream.replace "flash" do %>
    <%= render partial: "layouts/flash", locals: { message: "Message cannot be blank", type: :error } %>
  <% end %>
<% end %>

<%= turbo_stream.update "chat_form" do %>
  <%= render partial: "ai/conversations/chat_form",
    locals: { conversation: @messageable } %>
<% end %>
