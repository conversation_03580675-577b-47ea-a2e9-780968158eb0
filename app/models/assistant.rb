class Assistant < ApplicationRecord
  # 常量定义 (ERROR_PREFIXES 仍然用于逻辑判断，保留)
  ERROR_PREFIXES = [ "Tool execution error:", "Error:" ]

  # 关联
  belongs_to :user                                 # 助手属于一个用户
  has_many :conversations, dependent: :destroy     # 助手有多个对话，删除助手时级联删除对话
  has_many :messages, as: :messageable             # 多态关联，助手可以有多个消息

  # 验证
  validates :name, presence: true, length: { maximum: 100 }  # 名称必须存在且不超过100个字符
  validates :instructions, presence: true                    # 指令必须存在
  validates :tool_choice, presence: true, inclusion: {       # 工具选择必须在指定值中
    in: %w[auto none any],
    message: "%{value} is not a valid tool choice"
  }
  # 修改工具存在性的验证条件：只有当tool_choice不是"none"时才验证
  validates :tools, presence: true, if: -> { tool_choice != "none" }

  # 获取当前助手启用的工具实例
  # @return [Array<Langchain::Tool>] 工具实例数组
  def enabled_tools
    return [] if tool_choice == "none"

    @enabled_tools ||= tools.map do |tool_name|
      begin
        Object.const_get(tool_name).new
      rescue NameError => e
        Rails.logger.error("Failed to initialize tool: #{tool_name}, error: #{e.message}")
        nil
      end
    end.compact
  end

  # 获取大语言模型实例
  # 返回通过AI服务创建的语言模型
  def llm
    @llm ||= Ai::LLMService.create_llm
  end

  # 设置Langchain助手
  # @param assistant_message [Message] 助手消息对象
  # @param user_message_id [Integer] 当前处理的用户消息ID，用于避免在缓存未命中时重复加载
  # 初始化Langchain助手并设置相关回调
  # 从缓存加载基于 messageable_context 的消息
  def set_langchain_assistant(assistant_message, user_message_id:)
    @assistant_message = assistant_message
    @messageable_context = assistant_message.messageable
    @current_tool_usages = {}

    @langchain_assistant = create_new_langchain_assistant

    load_cached_langchain_assistant_messages(user_message_id: user_message_id)
  end

  # 创建新的Langchain助手实例
  # @return [Langchain::Assistant] 新创建的助手实例
  def create_new_langchain_assistant
    Langchain::Assistant.new(
      id: id,
      llm: llm,
      instructions: instructions,
      tools: enabled_tools,
      tool_choice: tool_choice,
      add_message_callback: method(:handle_new_message),
      tool_execution_callback: method(:handle_tool_execution)
    )
  end

  # 处理新消息的回调
  # @param message [Langchain::Assistant::Messages::OpenAIMessage] Langchain消息对象
  # 根据消息角色更新助手消息状态，并将所有通过回调添加的消息追加到缓存
  def handle_new_message(message)
    case message.role
    when "user"
      # 用户消息通常由 add_message_and_run! 添加，如果回调被触发，我们只缓存它
      # 不需要更新数据库消息，因为它已经创建
      # 用户消息缓存 (由 add_message_and_run! 触发)
      append_new_message_to_cache(message) # 保持缓存用户消息
    when "assistant"
      if message.tool_calls.present? && message.content.blank?
        # 工具调用状态：保存 tool_calls 并设置处理中状态
        # 使用 update! 触发回调以更新 UI (保留 tool_calls)
        @assistant_message.status = :processing # 更新状态为处理中
        @assistant_message.update!(
          tool_calls: message.tool_calls, # 保存 tool_calls
          content: I18n.t("ai.assistants.processing_message")
        )
        # 注意：此处不调用 append_new_message_to_cache，将在下方统一处理
      else
        # 最终回复状态：保留之前的 tool_calls，只更新内容
        # 关键修改：使用 update! 而非 update_columns，确保触发回调
        # 同时，仅当工具调用不存在时才更新 tool_calls
        update_attrs = { content: message.content }

        # 如果当前消息没有 tool_calls 但数据库中已有，则保留数据库中的
        if message.tool_calls.blank? && @assistant_message.tool_calls.present?
          # 不更新 tool_calls，保留现有值
        else
          # 否则，更新为新的 tool_calls（可能为 nil）
          update_attrs[:tool_calls] = message.tool_calls
        end
        @assistant_message.status = :completed # 更新状态为已完成
        @assistant_message.update!(update_attrs) # 使用 update! 触发回调
      end
      # 统一在 assistant 消息处理结束时缓存
      append_new_message_to_cache(message)
    when "tool"
      # 工具响应消息会在 handle_tool_response 中更新数据库状态
      handle_tool_response(message) if message.tool_call_id.present?
      # 缓存工具消息
      append_new_message_to_cache(message)
    end
    # 移除之前多余的注释
  end

  # 工具执行回调
  # @param tool_call_id [String] 工具调用ID
  # @param tool_name [String] 工具名称
  # @param method_name [String] 方法名称
  # @param arguments [Hash] 参数
  # 处理工具执行，创建相关记录
  def handle_tool_execution(tool_call_id, tool_name, method_name, arguments)
    function_name = "#{tool_name}##{method_name}"

    # 使用 create_or_find_by! 创建工具消息
    tool_message = @messageable_context.messages.create_or_find_by!(
      role: "tool",
      tool_call_id: tool_call_id
    ) do |message|
      message.content = format_tool_execution_message(function_name, arguments)
    end

    # 创建和更新工具使用记录
    @current_tool_usage = tool_message.tool_usages.create_or_find_by!(function_name: function_name) do |usage|
      usage.arguments = arguments
      usage.started_at = Time.current
      usage.status = :pending
    end

    # 存储到哈希表
    @current_tool_usages[tool_call_id] = @current_tool_usage

    # 不需要修改参数，直接返回原始参数
    arguments
  end

  # 将新的 Langchain 消息对象追加到缓存中
  # @param new_message_object [Langchain::Assistant::Messages::OpenAIMessage] 要追加的消息对象
  def append_new_message_to_cache(new_message_object)
    return unless @messageable_context && new_message_object

    cache_key = get_langchain_messages_cache_key

    # 使用 fetch 进行原子性读写操作，移除 force: true 以正确读取现有缓存
    Rails.cache.fetch(cache_key, expires_in: 30.minutes) do |cached_messages|
      # 如果缓存为空或不是数组，则初始化为空数组
      messages_array = cached_messages.is_a?(Array) ? cached_messages.dup : [] # 使用 dup 避免修改原始缓存对象

      # 追加新消息
      messages_array << new_message_object

      # 返回更新后的数组以写入缓存
      messages_array
    end
  rescue => e
    Rails.logger.error "Failed to append message to cache #{cache_key}: #{e.message}\n#{e.backtrace.join("\n")}"
    # 考虑是否需要清除缓存或采取其他错误处理措施
  end

  private

  # 从缓存加载 Langchain 消息，如果缓存不存在则从数据库构建并存入缓存
  # @param user_message_id [Integer] 当前处理的用户消息ID
  def load_cached_langchain_assistant_messages(user_message_id:)
    return unless @messageable_context.respond_to?(:messages)

    cache_key = get_langchain_messages_cache_key

    cached_messages = Rails.cache.fetch(cache_key, expires_in: 30.minutes) do
      # 如果缓存不存在，从数据库加载历史消息并构建 Langchain::Assistant::Messages::OpenAIMessage 对象数组
      # 注意：get_messageable_history 返回的是哈希数组，不是 Message 对象
      # 直接调用 MessageService 的方法
      history = Ai::MessageService.get_messageable_history(@messageable_context)
      # 在构建时排除当前用户消息 ID
      build_langchain_messages_from_history(history, exclude_message_id: user_message_id)
    end

    # 将加载或构建的消息设置到 Langchain Assistant 实例
    # 确保 @langchain_assistant.messages 是一个 Langchain::Assistant::Messages 实例
    # Langchain::Assistant 初始化时会创建这个对象，我们直接替换其内部数组
    @langchain_assistant.messages.clear
    cached_messages.each { |msg| @langchain_assistant.messages << msg }
  end

  # 从历史记录构建 Langchain 消息对象数组
  # @param history [Array<Hash>] 消息历史记录哈希数组 (包含 :id)
  # @param exclude_message_id [Integer, nil] 要排除的消息 ID
  # @return [Array<Langchain::Assistant::Messages::OpenAIMessage>] Langchain 消息对象数组
  def build_langchain_messages_from_history(history, exclude_message_id: nil)
    history.map do |msg|
      # 排除指定的消息 ID
      next if exclude_message_id && msg[:id] == exclude_message_id
      # 确保不处理 system 消息
      next if msg[:role] == "system"

      # 使用 adapter 构建消息对象
      # 对于助手消息，忽略历史的 tool_calls，只传递 content
      # 对于工具消息，需要 tool_call_id 和 content
      # 对于用户消息，只需要 content
      message_params = {
        role: msg[:role],
        content: msg[:content]
      }
      if msg[:role] == "tool"
        message_params[:tool_call_id] = msg[:tool_call_id]
        # elsif msg[:role] == "assistant"
        # 明确不传递 tool_calls: msg[:tool_calls]
      end

      langchain_message = @langchain_assistant.llm_adapter.build_message(**message_params)

      # 如果原始消息有 ID，可以考虑将其附加到 Langchain 消息对象上（如果需要追踪）
      langchain_message.instance_variable_set(:@id, msg[:id]) if msg[:id]
      langchain_message
    end.compact # compact 用于移除 map 中因 next 产生的 nil
  end

  # 获取 Langchain 消息缓存的键名
  def get_langchain_messages_cache_key
    "langchain_messages: assistant_#{id}/#{@messageable_context.class.name}_#{@messageable_context.id}"
  end

  # 处理工具响应
  # @param message [Langchain::Message] 工具消息
  # 更新工具使用状态和内容
  def handle_tool_response(message)
    tool_usage = @current_tool_usages[message.tool_call_id]
    return unless tool_usage

    # 检查是否是错误消息
    is_error = message.content.to_s.start_with?(*ERROR_PREFIXES)

    # 更新工具状态
    tool_usage.update!(
      status: is_error ? :failed : :success,
      result: message.content,
      completed_at: Time.current
    )

    # 对应的工具消息也需要更新
    tool_usage.message.update!(
      content: message.content
    )

    # 处理完后移除
    @current_tool_usages.delete(message.tool_call_id)
  end

  # 格式化工具执行消息
  # 优化参数处理，确保即使不是 JSON 字符串也能正确显示
  # @param function_name [String] 函数名称
  # @param arguments [Hash, String] 参数
  # @return [String] 格式化后的消息
  def format_tool_execution_message(function_name, arguments)
    args_display = case arguments
    when String
      begin
        JSON.pretty_generate(JSON.parse(arguments))
      rescue JSON::ParserError
        arguments
      end
    else
      JSON.pretty_generate(arguments)
    end

    "Executing #{function_name} with parameters: #{args_display}"
  end
end
