require 'rails_helper'

RSpec.describe 'Post Management', type: :feature do
  let(:user) { create(:user) }

  before do
    sign_in(user)
  end

  describe 'Creating a new post' do
    it 'creates a new draft post' do
      visit new_post_path

      fill_in 'post[title]', with: 'My Posts Title'
      # 正确填充 Trix 编辑器的方法：找到隐藏的 input 并设置其 value
      find(:xpath, "//input[@id='post_content_trix_input_post']", visible: :hidden).set("This is a draft post")

      click_button 'Save post' # Correct button text from _form.html.erb

      expect(page).to have_content('Post created') # Match actual flash message
      expect(page).to have_content('My Posts Title')
      expect(page).to have_content('Draft') # Correct status text (no "Status:")
    end
  end

  describe 'Publishing a draft post' do
    let!(:draft_post) do
      create(:post,
        title: 'Draft Post',
        status: 'draft',
        user: user
      )
    end

    it 'publishes a draft post' do
      visit post_path(draft_post)
      click_link 'Publish'

      expect(page).to have_content('Post published') # Match actual flash message
      expect(page).to have_content('Have released') # Correct status text (no "Status:")
      expect(page).to have_content('Posted on:') # Check for published date label
    end
  end

  describe 'Editing a post' do
    let!(:post) do
      create(:post,
        title: 'Original Title',
        status: 'published',
        user: user
      )
    end

    it 'updates an existing post' do
      visit post_path(post)
      click_link 'Edit'
      fill_in 'post[title]', with: 'Updated Title'
      click_button 'Save post' # Correct button text from _form.html.erb

      expect(page).to have_content('Post has been updated') # Match actual flash message
      expect(page).to have_content('Updated Title')
    end
  end

  describe 'Deleting a post' do
    let!(:post) do
      create(:post,
        title: 'Post to Delete',
        user: user
      )
    end

    # Add js: true because it uses turbo_confirm
    it 'deletes a post', js: true do
      visit posts_path

      expect(page).to have_content('Post to Delete')
      # Find the specific post container and click delete within it
      within(:xpath, "//h2[contains(text(), 'Post to Delete')]/ancestor::div[contains(@class, 'p-6')]") do
        accept_alert do # Try using accept_alert instead of accept_confirm
          click_link 'Delete'
        end
      end

      expect(page).to have_content('Post deleted')
      expect(page).not_to have_content(post.title)
    end
  end

  describe 'Viewing posts list' do
    before do
      create(:post, title: 'First Post', status: 'published', user: user)
      create(:post, title: 'Second Post', status: 'draft', user: user)
      create(:post, title: 'Third Post', status: 'published', user: user)
    end

    it 'displays all posts' do
      visit posts_path

      expect(page).to have_content('First Post')
      expect(page).to have_content('Second Post')
      expect(page).to have_content('Third Post')
    end
  end
end
