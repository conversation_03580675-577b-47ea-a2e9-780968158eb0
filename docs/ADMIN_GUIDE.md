# Admin Interface Guide

本文档介绍如何使用 AI Pro CGC 的管理员界面，该界面基于 Administrate gem 构建。

## 访问管理员界面

### 前提条件
- 必须拥有管理员权限的用户账户
- 默认管理员账户：`<EMAIL>`

### 访问方式
1. **通过导航栏**：登录后，管理员用户会在导航栏右上角看到红色的 "Admin" 按钮
2. **直接访问**：访问 `/admin` 路径

## 管理员功能

### 用户管理 (`/admin/users`)
- **查看用户列表**：显示用户名、邮箱、管理员状态、手机验证状态等
- **用户详情**：查看用户的完整信息，包括团队、知识库、助手等
- **编辑用户**：修改用户基本信息和管理员权限
- **过滤功能**：
  - `admin:` - 仅显示管理员用户
  - `regular:` - 仅显示普通用户
  - `verified:` - 仅显示已验证手机的用户

### 事件管理 (`/admin/events`)
- **事件列表**：显示事件标题、状态、开始时间、创建者等
- **事件详情**：查看事件的完整信息
- **编辑事件**：修改事件信息
- **过滤功能**：
  - `published:` - 已发布的事件
  - `draft:` - 草稿状态的事件
  - `upcoming:` - 即将开始的事件
  - `past:` - 已结束的事件
  - `online:` - 线上事件
  - `offline:` - 线下事件

### 团队管理 (`/admin/teams`)
- **团队列表**：显示团队信息
- **团队详情**：查看团队成员和资源
- **编辑团队**：修改团队信息

### 知识库管理 (`/admin/knowledge_bases`)
- **知识库列表**：显示所有知识库
- **知识库详情**：查看知识库内容和权限
- **编辑知识库**：修改知识库设置

### AI 助手管理 (`/admin/assistants`)
- **助手列表**：显示所有 AI 助手
- **助手详情**：查看助手配置和工具
- **编辑助手**：修改助手设置

### 帖子管理 (`/admin/posts`)
- **帖子列表**：显示所有帖子
- **帖子详情**：查看帖子内容
- **编辑帖子**：修改帖子信息

## 使用技巧

### 搜索功能
- 在每个管理页面的搜索框中输入关键词进行搜索
- 使用过滤器快速筛选数据，格式：`filter_name:`

### 批量操作
- 在列表页面可以选择多个项目进行批量操作
- 支持批量删除、批量更新等操作

### 分页
- 每页默认显示 20 条记录
- 可以通过 URL 参数 `per_page` 调整每页显示数量

## 权限说明

### 管理员权限
- 管理员可以访问所有管理功能
- 可以查看、编辑、删除所有资源
- 可以设置其他用户为管理员

### 安全注意事项
- 管理员权限非常强大，请谨慎使用
- 不要将管理员权限授予不可信的用户
- 定期检查管理员用户列表

## 技术细节

### 基于 Administrate
- 使用 Administrate 1.0.0.beta3 构建
- 支持 Rails 8.0
- 集成了 ActionPolicy 进行权限控制

### 自定义配置
- Dashboard 文件位于 `app/dashboards/`
- 控制器文件位于 `app/controllers/admin/`
- 可以通过修改这些文件来自定义管理界面

### 认证和授权
- 使用现有的用户认证系统
- 通过 `user.admin?` 检查管理员权限
- 集成了 ActionPolicy 进行细粒度权限控制

## 故障排除

### 常见问题
1. **无法访问管理界面**
   - 确认用户已登录
   - 确认用户具有管理员权限

2. **页面显示错误**
   - 检查服务器日志
   - 确认数据库连接正常

3. **权限错误**
   - 确认 ActionPolicy 配置正确
   - 检查用户权限设置

### 联系支持
如果遇到问题，请联系技术支持团队。
