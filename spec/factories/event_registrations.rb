FactoryBot.define do
  factory :event_registration do
    status { 'confirmed' }
    registered_at { Time.current }
    amount_paid { 0 }

    association :user
    association :event

    trait :pending do
      status { 'pending' }
    end

    trait :confirmed do
      status { 'confirmed' }
    end

    trait :cancelled do
      status { 'cancelled' }
    end

    trait :attended do
      status { 'attended' }
    end

    trait :no_show do
      status { 'no_show' }
    end
  end
end
