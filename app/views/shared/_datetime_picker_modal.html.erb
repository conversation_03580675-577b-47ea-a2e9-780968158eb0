<%# 时间和日期选择器模态框 %>
<%# 接受参数: field_name, field_id, label %>

<% field_name ||= "datetime" %>
<% field_id ||= "datetime-input" %>
<% label ||= "Select Date and Time" %>
<% modal_id = "timepicker-modal-#{field_name}" %>

<!-- 触发按钮 -->
<button type="button" 
        data-modal-target="<%= modal_id %>" 
        data-modal-toggle="<%= modal_id %>" 
        class="text-gray-900 bg-white hover:bg-gray-100 border border-gray-200 focus:ring-4 focus:outline-none focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:focus:ring-gray-600 dark:bg-gray-800 dark:border-gray-700 dark:text-white dark:hover:bg-gray-700 transition-colors duration-200">
  <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
    <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clip-rule="evenodd"/>
  </svg>
  <%= label %>
</button>

<!-- 主模态框 -->
<div id="<%= modal_id %>" 
     tabindex="-1" 
     aria-hidden="true" 
     class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full">
  <div class="relative p-4 w-full max-w-[28rem] max-h-full">
    <!-- 模态框内容 -->
    <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-800">
      <!-- 模态框头部 -->
      <div class="flex items-center justify-between p-4 border-b rounded-t dark:border-gray-700 border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          <%= t('events.datetime_picker.modal_title') %>
        </h3>
        <button type="button" 
                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm h-8 w-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white" 
                data-modal-toggle="<%= modal_id %>">
          <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
          </svg>
          <span class="sr-only">Close modal</span>
        </button>
      </div>
      
      <!-- 模态框主体 -->
      <div class="p-4 pt-0">
                 <!-- 日期和时间选择器容器 -->
         <div data-controller="datetime-picker" 
              data-datetime-picker-field-id-value="<%= field_id %>"
              data-datetime-picker-alert-message-value="<%= t('events.datetime_picker.select_datetime_alert') %>"
              class="mx-auto sm:mx-0">
           
           <!-- 日期选择器 -->
           <div class="flex justify-center my-5">
             <div id="inline-datepicker-<%= field_name %>" 
                  inline-datepicker 
                  datepicker-autoselect-today 
                  class="[&>div>div]:shadow-none [&>div>div]:bg-gray-50 [&_div>button]:bg-gray-50 dark:[&>div>div]:bg-gray-700 dark:[&_div>button]:bg-gray-700">
             </div>
           </div>
        
        <!-- 时间选择器 -->
        <div class="grid grid-cols-2 gap-4 mb-5">
          <!-- 小时选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
              <%= t('events.datetime_picker.hour') %>
            </label>
            <select id="hour-select-<%= field_name %>" 
                    data-action="change->datetime-picker#timeChanged"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
              <% (0..23).each do |hour| %>
                <option value="<%= sprintf('%02d', hour) %>"><%= sprintf('%02d', hour) %></option>
              <% end %>
            </select>
          </div>
          
          <!-- 分钟选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">
              <%= t('events.datetime_picker.minute') %>
            </label>
            <select id="minute-select-<%= field_name %>" 
                    data-action="change->datetime-picker#timeChanged"
                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500">
              <% (0..59).each do |minute| %>
                <option value="<%= sprintf('%02d', minute) %>"><%= sprintf('%02d', minute) %></option>
              <% end %>
            </select>
          </div>
        </div>
        
                   <!-- 保存和取消按钮 -->
           <div class="grid grid-cols-2 gap-2">
             <button type="button" 
                     data-action="click->datetime-picker#save"
                     class="text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 mb-2 dark:bg-primary-600 dark:hover:bg-primary-700 focus:outline-none dark:focus:ring-primary-800 transition-colors duration-200">
               <%= t('common.actions.save') %>
             </button>
             <button type="button" 
                     data-modal-hide="<%= modal_id %>" 
                     class="py-2.5 px-5 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 transition-colors duration-200">
               <%= t('common.actions.cancel') %>
             </button>
           </div>
         </div>
      </div>
    </div>
  </div>
</div> 