require "administrate/base_dashboard"

class ActiveStorageAttachmentDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    name: Field::String,
    record_type: Field::String,
    record_id: Field::Number,
    blob_id: Field::Number,
    blob: Field::BelongsTo.with_options(class_name: "ActiveStorage::Blob"),
    record: Field::Polymorphic,
    created_at: Field::DateTime,
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  COLLECTION_ATTRIBUTES = %i[
    id
    name
    record_type
    record_id
    blob
    created_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    name
    record_type
    record_id
    blob_id
    blob
    record
    created_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    name
    record_type
    record_id
    blob_id
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how attachments are displayed
  # across all pages of the admin dashboard.
  def display_resource(attachment)
    "#{attachment.name} (#{attachment.record_type})"
  end
end
