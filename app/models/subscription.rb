class Subscription < ApplicationRecord
  belongs_to :user

  validates :plan_type, presence: true
  validates :start_date, presence: true
  validates :status, presence: true

  enum :plan_type, { weekly: 0, monthly: 1 }
  enum :status, { active: 0, canceled: 1, expired: 2 }

  scope :active, -> { where(status: :active) }

  before_create :set_end_date

  private

  def set_end_date
    self.end_date = case plan_type.to_sym
    when :weekly
                      start_date + 1.week
    when :monthly
                      start_date + 1.month
    end
  end
end
