class TeamResourceAccess < ApplicationRecord
  # 合并后的资源类型定义（同时包含枚举值和对应类名）
  RESOURCE_KINDS = {
    knowledge_base: {
      enum: "knowledge_base",
      class_name: "KnowledgeBase"
    },
    learning_atlas_garden: {
      enum: "learning_atlas_garden",
      class_name: "LearningAtlasGarden"
    },
    event: {
      enum: "event",
      class_name: "Event"
    }
  }.freeze

  # 合并后的角色定义（包含数值映射和自动生成枚举）
  ROLES = {
    knowledge_base: {
      viewer: 0,
      editor: 1,
      maintainer: 2
    },
    learning_atlas_garden: {
      student: 10,
      teaching_assistant: 11,
      instructor: 12
    },
    event: {
      viewer: 20,
      member: 21,
      moderator: 22
    }
  }.freeze

  # 自动生成枚举值
  RESOURCE_KIND_ENUM = RESOURCE_KINDS.transform_values { |v| v[:enum] }.freeze
  ROLE_ENUM = ROLES.values.flat_map { |h| h.keys }.index_with { |k| k.to_s }.freeze

  belongs_to :team_member
  belongs_to :resource, polymorphic: true

  # 使用 resource_kind 替代 resource_type 更明确
  enum :resource_kind, RESOURCE_KIND_ENUM
  enum :role, ROLE_ENUM

  validates :resource_kind, presence: true
  validates :role, presence: true

  # 确保一个团队成员对同一个资源只能有一个访问权限
  validates :team_member_id, uniqueness: {
    scope: [ :resource_type, :resource_id ],
    message: "already has access to this resource"
  }

  # 确保 resource_kind 与实际的 resource_type 匹配
  validate :resource_kind_matches_resource_type

  # 验证角色是否与资源类型匹配
  validate :role_matches_resource_kind

  # 添加作用域提升可读性
  scope :for_knowledge_bases, -> { where(resource_kind: :knowledge_base) }
  scope :for_learning_atlas_gardens, -> { where(resource_kind: :learning_atlas_garden) }
  scope :for_events, -> { where(resource_kind: :event) }

  # 添加便捷查询方法
  def self.valid_roles_for(resource_kind)
    ROLES[resource_kind.to_sym]&.keys || []
  end

  def self.role_value_for(resource_kind, role)
    ROLES.dig(resource_kind.to_sym, role.to_sym)
  end

  private

  def role_matches_resource_kind
    return if role.blank? || resource_kind.blank?

    valid_roles = ROLES[resource_kind.to_sym]&.keys || []
    return if valid_roles.map(&:to_s).include?(role)

    errors.add(:role, "does not match resource kind")
  end

  def resource_kind_matches_resource_type
    return if resource.blank? || resource_kind.blank?

    expected_class = RESOURCE_KINDS.dig(resource_kind.to_sym, :class_name)
    unless resource.class.name == expected_class
      errors.add(:resource_kind, "does not match resource type")
    end
  end
end
