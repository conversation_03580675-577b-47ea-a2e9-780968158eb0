class SendVerificationCodeJob < ApplicationJob
  queue_as :default

  retry_on StandardError, wait: 5.seconds, attempts: 3

  # 执行发送验证码的任务
  def perform(phone_number)
    # 如果手机号为空则直接返回
    return unless phone_number.present?

    begin
      # Removed phone number validation check, SmsService will handle it internally
      # or rely on SendCloud API feedback.
      # Expects phone_number in E.164 format.
      SmsService.send_sms_code(phone_number)
    rescue => e
      # Log the error and re-raise to allow retries
      Rails.logger.error "Error sending verification code to #{phone_number}: #{e.message}"
      raise e
    end
  end
end
