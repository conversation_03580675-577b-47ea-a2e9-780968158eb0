## [Unreleased]

### 2025-01 第三周 (01/13 - 01/19)

#### 知识库功能
- 实现知识库核心功能 (01/19)
  - 添加知识库 CRUD 操作
  - 实现知识库权限控制 (私有/团队/公开)
  - 添加软删除和归档功能
  - 集成缓存机制
  - 添加文档数量限制验证

#### 数据库和模型
- 完成数据库迁移，添加知识库和基础单元关联表 (01/19)
- 优化 BaseUnit 和 Post 之间的多对多关系 (01/14)
  - 创建 BaseUnitPost 关联模型
  - 更新关联关系和迁移文件
  - 优化数据完整性约束

#### 文件处理系统
- 优化文件转换和响应处理 (01/16)
  - 统一接口响应格式
  - 添加重复转换检查
  - 使用事务确保数据一致性
- 改进 BaseUnit 功能 (01/14)
  - 优化创建流程和 R2 配置
  - 完善异常处理机制
  - 优化文件处理流程

#### UI/UX 改进
- 添加知识库管理界面 (01/19)
  - 实现知识库列表、创建、编辑和删除页面
  - 添加知识库搜索功能
  - 优化用户权限控制显示
- 优化 Tailwind CSS 配置，移除废弃的 container 设置 (01/19)
- 完成 Flowbite UI 框架迁移 (01/16-01/17)
  - 移除 Shadcn UI
  - 重新设计静态页面
  - 优化响应式布局
  - 支持暗色模式
  - 统一设计风格

#### 系统优化
- 添加 Action Policy 授权系统 (01/18)
  - 配置默认授权上下文
  - 实现知识库访问控制策略
  - 集成控制器授权检查
- 优化数据库性能 (01/17)
  - 添加知识库相关索引
  - 优化查询性能
  - 添加数据完整性约束

#### 用户认证
- 优化 SMS 验证流程 (01/18)
  - 添加 SendVerificationCodeJob
  - 实现 SMS 服务用于验证码生成和验证
  - 更新 SendCloud SMS 配置
  - 优化错误处理和用户反馈

### 2025-01 第二周 (01/06 - 01/12)

#### AI 集成
- 集成 LangchainRB (01/10-01/12)
  - 替换 OpenAI LLM 为 Doubao LLM
  - 配置 Qdrant 向量数据库
  - 实现文档向量化搜索
  - 添加 AI 助手能力

#### 内容管理
- 实现 Post 功能 (01/06-01/07)
  - 创建 Post 模型和控制器
  - 添加 Post 视图
  - 集成 Action Text
  - 配置路由系统

### 2025-01 第一周 (01/01 - 01/05)

#### 团队功能
- 实现团队系统 (01/01-01/03)
  - 创建 Team 和 TeamMember 模型
  - 建立用户和团队关联
  - 添加相关测试用例

#### 用户注册
- 优化用户注册流程 (01/02-01/03)
  - 改进手机验证 UX
  - 更新注册控制器
  - 优化路由配置
  - 添加注册步骤验证

### 2024-12 (12/23 - 12/31)

#### 基础架构
- 初始化项目配置 (12/23-12/25)
  - 配置 Shadcn UI
  - 设置测试环境 (RSpec, Cucumber)
  - 添加基础用户认证
  - 创建订阅模型

#### 用户界面
- 实现用户仪表板 (12/30-12/31)
  - 创建仪表板布局
  - 添加侧边栏组件
  - 实现用户菜单
- 完善静态页面 (12/30-12/31)
  - 添加页脚组件
  - 优化导航栏样式
  - 实现响应式布局