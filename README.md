# AI Pro CGC

一个基于 Rails 8.0 构建的智能知识管理和 AI 助手平台，专为学习社区和团队协作设计。

## 项目简介

AI Pro CGC 是一个集成了人工智能功能的现代化 Web 应用，主要功能包括：

- 🤖 **AI 助手系统**：支持自定义 AI 助手，集成多种工具和知识库
- 📚 **知识库管理**：支持个人、团队和公共知识库的创建和管理
- 💬 **智能对话**：基于 RAG（检索增强生成）的对话系统
- 👥 **团队协作**：完整的权限管理和团队协作功能
- 📄 **文档处理**：支持多种格式文档的上传、转换和管理
- 🔐 **多方式认证**：支持邮箱、手机号和第三方（微信、GitHub）登录
- 📱 **微信集成**：完整的微信公众号和服务号集成
- ⚙️ **管理员界面**：基于 Administrate 的现代化管理后台

## 快速开始

### 环境要求

- Ruby 3.2+
- Rails 8.0+
- Node.js 18+
- PostgreSQL/SQLite
- Redis (生产环境推荐)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-org/ai_pro_cgc.git
   cd ai_pro_cgc
   ```

2. **安装依赖**
   ```bash
   # 安装 Ruby 依赖
   bundle install

   # 安装 JavaScript 依赖（如需要）
   npm install
   ```

3. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp config/credentials.yml.enc.example config/credentials.yml.enc

   # 编辑凭据（需要主密钥）
   rails credentials:edit
   ```

4. **数据库设置**
   ```bash
   # 创建并迁移数据库
   rails db:create
   rails db:migrate

   # 加载种子数据（可选）
   rails db:seed
   ```

5. **启动开发服务器**
   ```bash
   # 使用 Procfile.dev 启动所有服务
   bin/dev

   # 或者单独启动 Rails 服务器
   rails server
   ```

访问 http://localhost:3000 开始使用应用。

## 技术栈

### 后端技术
- **Rails 8.0** - Web 框架
- **Ruby 3.2** - 编程语言
- **SQLite/PostgreSQL** - 数据库
- **Solid Queue** - 后台任务处理
- **Solid Cache** - 缓存系统
- **Action Cable** - WebSocket 支持

### 前端技术
- **Hotwire** (Turbo + Stimulus) - 现代化前端交互
- **Tailwind CSS v4** - CSS 框架
- **Importmap** - JavaScript 模块管理
- **Flowbite** - UI 组件库

### AI 相关
- **Langchain.rb** - AI 应用框架
- **OpenAI/Doubao** - 大语言模型
- **Qdrant** - 向量数据库
- **RAG** - 检索增强生成

### 第三方集成
- **微信** - 公众号/服务号集成
- **OmniAuth** - 第三方认证
- **AWS S3** - 文件存储
- **Action Policy** - 权限管理
- **Administrate** - 管理员界面

## 项目结构

```
ai_pro_cgc/
├── app/
│   ├── controllers/          # 控制器
│   │   ├── ai/              # AI 相关控制器
│   │   ├── wechat/          # 微信相关控制器
│   │   └── ...
│   ├── models/              # 模型
│   │   ├── concerns/        # 共享模块
│   │   └── ...
│   ├── services/            # 服务对象
│   │   ├── ai/              # AI 服务
│   │   └── ...
│   ├── jobs/                # 后台任务
│   ├── policies/            # 权限策略
│   ├── views/               # 视图模板
│   └── javascript/          # 前端 JavaScript
├── config/                  # 配置文件
├── db/                      # 数据库相关
├── docs/                    # 项目文档
├── lib/                     # 库文件
├── spec/                    # 测试文件
└── features/                # 集成测试
```

## 核心功能模块

### 1. 用户认证与管理
- 多种登录方式（邮箱、手机、第三方）
- 用户偏好设置
- 订阅管理

### 2. AI 助手系统
- 自定义 AI 助手
- 工具集成
- 对话管理
- 消息处理

### 3. 知识库系统
- 个人/团队/公共知识库
- 文档上传与转换
- 版本控制
- 权限管理

### 4. 团队协作
- 团队创建与管理
- 角色权限控制
- 资源共享

### 5. 微信集成
- 公众号/服务号接入
- 菜单管理
- 消息处理

### 6. 管理员系统
- 基于 Administrate 的现代化管理界面
- 用户管理和权限控制
- 事件、团队、知识库等资源管理
- 数据统计和监控

## 开发指南

### 代码规范
- 遵循 Ruby 和 Rails 最佳实践
- 使用 RuboCop 进行代码检查
- 编写测试（RSpec + Cucumber）

### 测试
```bash
# 运行单元测试
rspec

# 运行集成测试
cucumber

# 运行所有测试
rake test:all
```

### 代码检查
```bash
# 运行 RuboCop
rubocop

# 安全检查
brakeman
```

## 部署指南

### 多环境部署

- **预发环境**：`master` 分支推送自动触发
- **生产环境**：创建 GitHub Release 时触发

### 服务器配置

```yaml
# 生产环境 (config/deploy.production.yml)
servers:
  web:
    - ***************
proxy:
  host: codingirls.club # 需配置DNS解析

# 预发环境 (config/deploy.staging.yml)
servers:
  web:
    - ***************
proxy:
  host: sg-server.codingirls.club # TODO需配置DNS解析
```

### 密钥管理

通过 GitHub Secrets 配置：

```bash
# 必须配置的密钥
KAMAL_REGISTRY_USERNAME    # Docker仓库用户名
KAMAL_REGISTRY_PASSWORD    # Docker访问令牌
STG_SSH_PRIVATE_KEY        # 预发环境SSH密钥
PROD_SSH_PRIVATE_KEY       # 生产环境SSH密钥
STG_RAILS_MASTER_KEY       # 预发环境主密钥
PROD_RAILS_MASTER_KEY      # 生产环境主密钥
```

### 部署流程

```mermaid
sequenceDiagram
    participant GitHub
    participant Runner
    participant DockerHub
    participant Server

    GitHub->>Runner: 触发工作流
    Runner->>Runner: 安装依赖
    Runner->>DockerHub: 构建镜像
    Runner->>Server: 部署应用
    Server-->>Runner: 返回结果
    Runner-->>GitHub: 更新状态
```

### 常用命令

```bash
# 查看部署日志
kamal app logs -d staging

# 健康检查
kamal app healthcheck -d production

# 版本回滚
kamal rollback -d production
```

#### Rake 任务

```bash
# 创建微信服务号菜单
# 需要先在 config/wechat_menus/menu_service.yml 配置菜单
# 注意：由于微信 IP 白名单限制，此命令需要在服务器上执行

# 1. 在服务器上找到 Rails 应用的 Docker 容器 ID
docker ps

# 2. 使用容器 ID 执行 Rake 任务 (将 <container_id> 替换为实际 ID)
docker exec -it <container_id> bin/rails wechat:create_service_menu
```

### 注意事项

1. 确保服务器开放 22/80/443 端口
2. 域名解析指向正确 IP
3. 定期轮换 SSH 密钥

## 一些使用示例

### TeamResourceAccess

```ruby
# 分配课程教师角色
TeamResourceAccess.create!(
  team_member: user.team_members.first,
  resource: course,
  resource_kind: :course,
  role: :instructor
)

# 查询用户在某知识库的角色
current_user.team_members
  .joins(:resource_accesses)
  .where(
    team_resource_accesses: {
      resource_kind: :knowledge_base,
      resource: knowledge_base,
      role: :maintainer
    }
  )
```

## WeChat 接口数据格式

### 公众号用户信息 API 响应格式

当调用 `wechat(:service).user(openid)` 时，返回的用户信息格式如下：

```ruby
{
  subscribe: 1,                  # 用户是否订阅该公众号，1为已订阅，0为未订阅
  openid: 'OPENID',              # 用户的唯一标识
  nickname: 'NICKNAME',          # 用户昵称
  sex: 1,                        # 用户性别，1为男性，2为女性，0为未知
  language: 'zh_CN',             # 用户的语言，简体中文为zh_CN
  city: 'CITY',                  # 用户所在城市
  province: 'PROVINCE',          # 用户所在省份
  country: 'COUNTRY',            # 用户所在国家
  headimgurl: 'http://wx.qlogo.cn/...', # 用户头像URL
  subscribe_time: **********,    # 用户关注时间，时间戳
  unionid: 'oR5GjjgEhCMJFyzaVZdrxZ2zRRF4', # 只有在用户将公众号绑定到微信开放平台账号后，才会出现该字段
  remark: '',                    # 公众号运营者对粉丝的备注
  groupid: 0                     # 用户所在的分组ID
}
```

### 网页微信扫码登录 (OmniAuth) 响应格式

当使用 OmniAuth 进行微信网页登录时，`request.env["omniauth.auth"]` 的格式如下：

```ruby
{
  "provider" => "wechat",
  "uid" => "openid_value",
  "info" => {
    "nickname" => "用户昵称",
    "name" => "用户名称",
    "image" => "头像URL",
    "email" => nil  # 微信通常不提供邮箱
  },
  "credentials" => {
    "token" => "access_token_value",
    "refresh_token" => "refresh_token_value",
    "expires_at" => **********,
    "expires" => true
  },
  "extra" => {
    "raw_info" => {
      "openid" => "openid_value",
      "nickname" => "用户昵称",
      "sex" => 1,
      "language" => "zh_CN",
      "city" => "城市",
      "province" => "省份",
      "country" => "国家",
      "headimgurl" => "头像URL",
      "unionid" => "unionid_value",  # 如果有的话
      # 其他微信返回的原始数据
    }
  }
}
```

### 数据转换

我们提供了 `WechatInteractions.convert_wechat_api_to_omniauth` 方法将公众号 API 响应转换为 OmniAuth 格式：

```ruby
# 在 ServiceController 中使用示例
user_info = wechat(:service).user(openid)
auth = convert_wechat_api_to_omniauth(user_info)
User.create_from_oauth(auth)
```

## 📖 文档

### 🎯 新开发者快速开始
1. **开发环境** → [开发者指南](docs/DEVELOPMENT_GUIDE.md) - 环境配置和开发流程
2. **系统理解** → [系统架构](docs/ARCHITECTURE.md) - 技术架构和设计理念
3. **API 使用** → [API 文档](docs/API_DOCUMENTATION.md) - 接口规范和示例

### 📚 完整文档
📋 **[查看技术文档索引](docs/DEVELOPMENT_GUIDE.md)** - 包含开发环境配置、技术文档导航和详细说明

🔍 **快速查找**：[功能开发管理](docs/features_development/README.md) | [RAG 系统](docs/RAG.md) | [活动开发计划](docs/features_development/events_development_plan.md)

## TODO

- [ ] integrate rails 8.1 md preview
- [ ] add all credentials to credentials.yml
- [ ] when testing, skip langchainrb embedding
- [ ] learning atlas garden's user journey and ERD
- [ ] refactor knowledge base Team Resource Access

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页：[https://github.com/your-org/ai_pro_cgc](https://github.com/your-org/ai_pro_cgc)
- 问题反馈：[Issues](https://github.com/your-org/ai_pro_cgc/issues)
- 文档：[Wiki](https://github.com/your-org/ai_pro_cgc/wiki)
