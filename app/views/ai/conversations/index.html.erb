<div class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <!-- 面包屑导航 -->
        <%= render partial: 'shared/breadcrumb', locals: { items: [[t('ai.conversations.title'), nil]] } %>

        <div class="mb-6">
          <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white transition-colors duration-200"><%= t('ai.conversations.title') %></h1>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="col-span-full">
        <div class="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
          <%= link_to new_ai_conversation_path, class: "inline-flex items-center px-5 py-2.5 bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 text-white font-medium rounded-lg text-sm shadow-xs hover:shadow-md transition-all duration-200" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            <%= t('ai.conversations.new') %>
          <% end %>
        </div>
        
        <% if @conversations.any? %>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <% @conversations.each do |conversation| %>
              <%= link_to ai_conversation_path(conversation), class: "block" do %>
                <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-md dark:shadow-xl dark:shadow-gray-900/20 p-5 hover:bg-gray-50 dark:hover:bg-gray-700/70 group transition-all duration-200 h-full flex flex-col">
                  <div class="flex-grow">
                    <div class="flex items-center mb-3">
                      <div class="w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400 mr-3">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                        </svg>
                      </div>
                      <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-primary-600 dark:group-hover:text-primary-400 truncate transition-colors duration-200"><%= conversation.title || "#{t('ai.conversations.title')} #{conversation.id}" %></h3>
                    </div>
                    
                    <% if conversation.messages.any? %>
                      <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mb-4 transition-colors duration-200">
                        <%= truncate(conversation.messages.last.content, length: 120) %>
                      </p>
                    <% else %>
                      <p class="text-gray-500 dark:text-gray-400 text-sm italic mb-4 transition-colors duration-200"><%= t('ai.common.no_results') %></p>
                    <% end %>
                  </div>
                  
                  <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 pt-3 border-t border-gray-100 dark:border-gray-700 transition-colors duration-200">
                    <div class="flex items-center">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <%= t('common.time_formats.updated_ago', time: time_ago_in_words(conversation.updated_at)) %>
                    </div>
                    
                    <% if conversation.current_assistant %>
                      <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <%= conversation.current_assistant.name %>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
          
          <% if @conversations.total_pages > 1 %>
            <div class="mt-8 flex justify-center">
              <%= paginate @conversations, window: 2 %>
            </div>
          <% end %>
          
        <% else %>
          <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-md dark:shadow-xl p-8 text-center transition-all duration-200">
            <div class="mx-auto h-20 w-20 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 mb-4 transition-colors duration-200">
              <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2 transition-colors duration-200"><%= t('ai.conversations.empty_state') %></h3>
            <p class="text-gray-500 dark:text-gray-400 mb-6 transition-colors duration-200"><%= t('ai.conversations.empty_state') %></p>
            <%= link_to new_ai_conversation_path, class: "inline-flex items-center px-5 py-2.5 bg-primary-600 hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 text-white font-medium rounded-lg text-sm shadow-xs hover:shadow-md transition-all duration-200" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              <%= t('ai.conversations.new') %>
            <% end %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
