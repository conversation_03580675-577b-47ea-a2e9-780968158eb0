require 'rails_helper'

RSpec.describe NotificationsController, type: :controller do
  # Test data factories
  let(:admin)        { create(:user, :admin) }
  let(:owner)        { create(:user) }
  let(:other_user)   { create(:user) }
  let(:guest_user)   { nil }
  let!(:owner_ntf)   { create(:notification, :for_user, user: owner) }
  let!(:other_ntf)   { create(:notification, :for_user, user: other_user) }

  # Mock authentication and ActionPolicy
  around(:each) do |example|
    # Set locale to English for consistent test results
    I18n.with_locale(:en) do
      example.run
    end
  end

  before do
    # Mock the authentication concern methods
    allow(controller).to receive(:require_authentication)
    allow(controller).to receive(:authenticated?).and_return(true)
    # Mock ActionPolicy methods
    allow(controller).to receive(:authorize!)
    allow(controller).to receive(:verify_authorized)
    # Mock I18n translations to ensure English responses
    allow(controller).to receive(:t) { |key, **options|
      case key
      when 'notifications.index.messages.not_found'
        'Notification not found'
      when 'notifications.index.messages.mark_all_success'
        'All notifications marked as read'
      else
        I18n.t(key, **options.merge(locale: :en))
      end
    }
  end

  # Helper method to mock authorized_scope based on current user
  def mock_authorized_scope_for(user)
    allow(controller).to receive(:authorized_scope) do |scope|
      if user&.admin?
        scope # Admin can access all notifications
      else
        scope.where(recipient: user) # Normal users can only access their own
      end
    end
  end

  describe "GET #index" do
    context "when user is authenticated" do
      let(:user) { create(:user) }
      
      before do
        allow(controller).to receive(:current_user).and_return(user)
        create_list(:notification, 3, recipient: user)
      end

      it "returns http success" do
        get :index
        expect(response).to have_http_status(:success)
      end

      it "loads user's notifications successfully" do
        get :index
        expect(response).to have_http_status(:success)
        # 验证通知数据被正确加载(不使用assigns)
        expect(controller.instance_variable_get(:@notifications)).to be_present
      end
    end

    context "when user is not authenticated" do
      before do
        allow(controller).to receive(:authenticated?).and_return(false)
        allow(controller).to receive(:current_user).and_return(nil)
        allow(controller).to receive(:require_authentication) do
          controller.redirect_to new_session_path
        end
      end

      it "redirects to login" do
        get :index
        expect(response).to redirect_to(new_session_path)
      end
    end
  end

  describe "GET #show" do
    let(:user) { create(:user) }
    let(:other_user) { create(:user) }
    let(:admin) { create(:user, :admin) }
    
    # 创建通知工厂函数
    let(:notification) { create(:notification, recipient: user) }
    let(:other_user_notification) { create(:notification, recipient: other_user) }

    context "when accessing own notification" do
      before do
        allow(controller).to receive(:current_user).and_return(user)
        mock_authorized_scope_for(user)
      end

      it "allows access to own notification" do
        get :show, params: { id: notification.id }
        expect(response).to have_http_status(:success)
        expect(controller.instance_variable_get(:@notification)).to eq(notification)
      end

      it "marks notification as read when accessed" do
        expect(notification.read_at).to be_nil
        get :show, params: { id: notification.id }
        notification.reload
        expect(notification.read_at).not_to be_nil
      end

      it "does not mark already read notification as read again" do
        notification.update!(read_at: 1.hour.ago)
        original_read_at = notification.read_at
        
        get :show, params: { id: notification.id }
        notification.reload
        expect(notification.read_at).to eq(original_read_at)
      end
    end

    context "when accessing other user's notification" do
      before do
        allow(controller).to receive(:current_user).and_return(user)
        mock_authorized_scope_for(user)
      end

      it "denies access and redirects with alert" do
        get :show, params: { id: other_user_notification.id }
        expect(response).to redirect_to(notifications_path)
        expect(flash[:alert]).to eq(I18n.t('notifications.index.messages.not_found'))
      end
    end

    context "when admin accessing any notification" do
      before do
        allow(controller).to receive(:current_user).and_return(admin)
        mock_authorized_scope_for(admin)
      end

      it "allows admin to access any user's notification" do
        get :show, params: { id: other_user_notification.id }
        expect(response).to have_http_status(:success)
        expect(controller.instance_variable_get(:@notification)).to eq(other_user_notification)
      end

      it "allows admin to access own notification" do
        admin_notification = create(:notification, recipient: admin)
        get :show, params: { id: admin_notification.id }
        expect(response).to have_http_status(:success)
        expect(controller.instance_variable_get(:@notification)).to eq(admin_notification)
      end
    end

    context "when notification does not exist" do
      before do
        allow(controller).to receive(:current_user).and_return(user)
        mock_authorized_scope_for(user)
      end

      it "handles non-existent notification with graceful redirect" do
        get :show, params: { id: 99999 }
        expect(response).to redirect_to(notifications_path)
        expect(flash[:alert]).to eq(I18n.t('notifications.index.messages.not_found'))
      end
    end

    context "when requesting JSON format" do
      before do
        allow(controller).to receive(:current_user).and_return(user)
        mock_authorized_scope_for(user)
      end

      it "returns JSON error for non-existent notification" do
        get :show, params: { id: 99999 }, format: :json
        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq(I18n.t('notifications.index.messages.not_found'))
      end

      it "returns JSON error when accessing other user's notification" do
        get :show, params: { id: other_user_notification.id }, format: :json
        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq(I18n.t('notifications.index.messages.not_found'))
      end
    end
  end

  describe "PATCH #update" do
    let(:user) { create(:user) }
    let(:other_user) { create(:user) }
    let(:admin) { create(:user, :admin) }
    
    let(:notification) { create(:notification, recipient: user) }
    let(:other_user_notification) { create(:notification, recipient: other_user) }

    context "when updating own notification" do
      before do
        allow(controller).to receive(:current_user).and_return(user)
        mock_authorized_scope_for(user)
      end

      it "allows updating own notification" do
        patch :update, params: { id: notification.id }, format: :json
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end

      it "marks notification as read" do
        expect(notification.read_at).to be_nil
        patch :update, params: { id: notification.id }, format: :json
        notification.reload
        expect(notification.read_at).not_to be_nil
      end
    end

    context "when updating other user's notification" do
      before do
        allow(controller).to receive(:current_user).and_return(user)
        mock_authorized_scope_for(user)
      end

      it "denies access and returns JSON error" do
        patch :update, params: { id: other_user_notification.id }, format: :json
        expect(response).to have_http_status(:not_found)
        json_response = JSON.parse(response.body)
        expect(json_response['error']).to eq(I18n.t('notifications.index.messages.not_found'))
      end
    end

    context "when admin updating any notification" do
      before do
        allow(controller).to receive(:current_user).and_return(admin)
        mock_authorized_scope_for(admin)
      end

      it "allows admin to update any user's notification" do
        patch :update, params: { id: other_user_notification.id }, format: :json
        expect(response).to have_http_status(:success)
        json_response = JSON.parse(response.body)
        expect(json_response['success']).to be true
      end
    end
  end

  describe "PATCH #mark_all_read" do
    let(:user) { create(:user) }
    
    before do
      allow(controller).to receive(:current_user).and_return(user)
      # 创建一些未读通知
      create_list(:notification, 3, recipient: user)
    end

    it "marks all user notifications as read" do
      expect(user.unread_notifications_count).to eq(3)
      
      patch :mark_all_read, format: :json
      expect(response).to have_http_status(:success)
      
      user.reload
      expect(user.unread_notifications_count).to eq(0)
    end

    it "redirects with success message for HTML format" do
      patch :mark_all_read
      expect(response).to redirect_to(notifications_path(format: :html))
      expect(flash[:notice]).to eq(I18n.t('notifications.index.messages.mark_all_success'))
    end

    it "returns JSON success for JSON format" do
      patch :mark_all_read, format: :json
      expect(response).to have_http_status(:success)
      json_response = JSON.parse(response.body)
      expect(json_response['success']).to be true
    end

    # 新增系统测试：未读通知存在时，PATCH /notifications/mark_all 返回 302 并全部标记为已读
    context "when unread notifications exist" do
      it "returns 302 redirect and marks all as read" do
        # 确保有未读通知
        expect(user.unread_notifications_count).to eq(3)
        
        patch :mark_all_read
        
        # 检查返回状态码为 302 (重定向)
        expect(response).to have_http_status(:found) # 302
        
        # 检查所有通知都被标记为已读
        user.reload
        expect(user.unread_notifications_count).to eq(0)
        
        # 检查重定向目标
        expect(response).to redirect_to(notifications_path(format: :html))
      end
    end

    # 新增系统测试：路由应匹配 notifications#mark_all_read 而非 #update
    context "route matching" do
      it "routes to mark_all_read action not update action" do
        # 通过检查控制器响应来验证路由匹配正确的动作
        expect(controller).to receive(:mark_all_read).and_call_original
        expect(controller).not_to receive(:update)
        
        patch :mark_all_read
        
        # 验证调用了正确的控制器方法
        expect(response).to have_http_status(:found)
      end
    end
  end

  private

  # 创建通知的辅助方法
  def create_notification_for(user)
    # 使用 SystemNotifier 创建一个通知
    SystemNotifier.with(message: "Test notification for #{user.username}").deliver(user)
    user.notifications.last
  end

  def create_list_notifications_for(user, count)
    count.times do |i|
      SystemNotifier.with(message: "Test notification #{i + 1} for #{user.username}").deliver(user)
    end
  end
end
