class OmniAuthIdentity < ApplicationRecord
  belongs_to :user

  validates :uid, presence: true
  validates :provider, presence: true
  validates :uid, uniqueness: { scope: :provider }

  # Scopes
  scope :by_provider_uid, ->(p, u) { where(provider: p, uid: u) }
  scope :by_provider_unionid, ->(p, x) { where(provider: p, unionid: x) }
  scope :by_provider_uid_unionid, ->(p, u, x) { where(provider: p, uid: u, unionid: x) }

  # Finds identity by auth hash, handling WeChat unionid
  def self.find_by_auth(auth)
    provider = auth["provider"]
    uid = auth["uid"]
    if provider == "wechat" && auth["unionid"]
      by_provider_uid_unionid(provider, uid, auth["unionid"]).first
    else
      by_provider_uid(provider, uid).first
    end
  end

  # Creates identity from auth hash for given user, including unionid for WeChat
  def self.create_from_auth(auth, user)
    attrs = { uid: auth["uid"], provider: auth["provider"], user: user }
    attrs[:unionid] = auth["unionid"] if auth["provider"] == "wechat" && auth["unionid"]
    create!(attrs)
  end
end
