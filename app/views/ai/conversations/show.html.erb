<%# Removed h-full, flex, flex-col. Let main layout handle height/scroll %>
<div data-controller="ai-chat">
  <!-- 聊天头部 - 使用 sticky 定位相对于 main 滚动 -->
  <div class="sticky top-0 z-10 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
    <%= render partial: 'ai/conversations/chat_header', locals: { conversation: @conversation } %>
  </div>

  <!-- 聊天消息区域 - 不再需要 flex-1 or overflow-y-auto -->
  <div data-ai-chat-target="messages">
    <%= render partial: 'ai/conversations/chat_messages', locals: { conversation: @conversation } %>
  </div>

  <!-- 聊天输入区域 - 使用 sticky 定位相对于 main 滚动 -->
  <div class="sticky bottom-0 z-10 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700" data-controller="slash-commands">
    <%= render partial: 'ai/conversations/chat_form', locals: { conversation: @conversation } %>
  </div>
  <%= render 'ai/conversations/chat_history', conversation: @conversation %>
</div>
