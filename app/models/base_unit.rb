class BaseUnit < ApplicationRecord
  # 使用 concern 分离关注点
  include FileProcessable

  # 优化关联关系
  belongs_to :user
  has_many :posts
  belongs_to :knowledge_base, optional: true

  validates :name, presence: true, uniqueness: { scope: :user_id }
  before_validation :set_default_name, if: -> { file.attached? && name.blank? }

  # 添加新的内容转换方法
  def convert_to_md
    return nil unless file.attached?

    convert_file_to_markdown(file.download)
  rescue StandardError => e
    Rails.logger.error("File conversion failed: #{e.message}")
    raise ConversionError, "File conversion failed: #{e.message}"
  end

  def convert_to_markdown!
    # return if posts.exists?

    markdown_content = convert_to_md
    Post.create!(
        title: name,
        content: markdown_content,
        user: user
      )
  end

  private

  def set_default_name
    original_name = file.filename.to_s
    return self.name = original_name unless name_exists?(original_name)

    timestamp = Time.current.strftime("%Y%m%d%H%M%S")
    base_name = File.basename(original_name, ".*")
    extension = File.extname(original_name)
    self.name = "#{base_name}_#{timestamp}#{extension}"
  end

  def name_exists?(filename)
    user.base_units.exists?(name: filename)
  end

  def convert_file_to_markdown(file_content)
    text_content = extract_text_from_file(file_content)
    text_content = sanitize_text_encoding(text_content)
    MarkdownConverter.convert(text_content)
  end

  def sanitize_text_encoding(text)
    text.encode("UTF-8", invalid: :replace, undef: :replace, replace: "")
  end

  def extract_text_from_file(file_content)
    text_extractor = TextExtractorFactory.create(content_type)
    text_extractor.extract(file_content)
  end

  class TextExtractorFactory
    def self.create(content_type)
      case content_type
      when "application/pdf"
        PDFTextExtractor.new
      when "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        DocxTextExtractor.new
      else
        raise ConversionError, "Unsupported file type: #{content_type}"
      end
    end
  end

  class PDFTextExtractor
    def extract(file_content)
      PDF::Reader.new(StringIO.new(file_content)).pages.map(&:text).join("\n")
    rescue StandardError => e
      raise ConversionError, "PDF conversion failed: #{e.message}"
    end
  end

  class DocxTextExtractor
    def extract(file_content)
      docx = Docx::Document.open(StringIO.new(file_content))
      docx.paragraphs.map(&:to_s).join("\n")
    rescue StandardError => e
      raise ConversionError, "DOCX conversion failed: #{e.message}"
    end
  end

  # 自定义错误类
  class ConversionError < StandardError; end
end
