require 'rails_helper'

RSpec.describe UserPolicy, type: :policy do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let(:admin_user) { create(:user, admin: true) }

  describe "User self-permissions" do
    context "when accessing own profile" do
      let(:policy) { UserPolicy.new(user, user: user) }

      it "allows user to view their own profile" do
        expect(policy).to be_allowed_to(:show?)
      end

      it "allows user to update their own profile" do
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows user to update their own preferences" do
        expect(policy).to be_allowed_to(:update_preferences?)
      end

      it "allows user to access their own settings" do
        expect(policy).to be_allowed_to(:settings?)
      end

      it "allows user to update their own profile settings" do
        expect(policy).to be_allowed_to(:update_profile?)
      end

      it "allows user to update their own password" do
        expect(policy).to be_allowed_to(:update_password?)
      end

      it "prevents user from deleting themselves" do
        expect(policy).not_to be_allowed_to(:destroy?)
      end

      it "prevents user from toggling their own admin status" do
        expect(policy).not_to be_allowed_to(:toggle_admin?)
        expect(policy).not_to be_allowed_to(:make_admin?)
        expect(policy).not_to be_allowed_to(:remove_admin?)
      end
    end

    context "when accessing other users' profiles" do
      let(:policy) { UserPolicy.new(other_user, user: user) }

      it "denies user from viewing other users' profiles" do
        expect(policy).not_to be_allowed_to(:show?)
      end

      it "denies user from updating other users' profiles" do
        expect(policy).not_to be_allowed_to(:update?)
      end

      it "denies user from updating other users' preferences" do
        expect(policy).not_to be_allowed_to(:update_preferences?)
      end

      it "denies user from accessing other users' settings" do
        expect(policy).not_to be_allowed_to(:settings?)
      end

      it "denies user from updating other users' profile settings" do
        expect(policy).not_to be_allowed_to(:update_profile?)
      end

      it "denies user from updating other users' passwords" do
        expect(policy).not_to be_allowed_to(:update_password?)
      end

      it "denies user from deleting other users" do
        expect(policy).not_to be_allowed_to(:destroy?)
      end

      it "denies user from toggling other users' admin status" do
        expect(policy).not_to be_allowed_to(:toggle_admin?)
        expect(policy).not_to be_allowed_to(:make_admin?)
        expect(policy).not_to be_allowed_to(:remove_admin?)
      end
    end
  end

  describe "Guest permissions" do
    context "when user is not authenticated" do
      let(:policy) { UserPolicy.new(user, user: nil) }

      it "denies guest from viewing user profiles" do
        expect(policy).not_to be_allowed_to(:show?)
      end

      it "denies guest from updating user profiles" do
        expect(policy).not_to be_allowed_to(:update?)
      end

      it "denies guest from accessing user settings" do
        expect(policy).not_to be_allowed_to(:settings?)
      end

      it "denies guest from updating profile settings" do
        expect(policy).not_to be_allowed_to(:update_profile?)
      end

      it "denies guest from updating passwords" do
        expect(policy).not_to be_allowed_to(:update_password?)
      end

      it "denies guest from deleting users" do
        expect(policy).not_to be_allowed_to(:destroy?)
      end

      it "denies guest from toggling admin status" do
        expect(policy).not_to be_allowed_to(:toggle_admin?)
      end
    end
  end

  describe "Scope" do
    context "when user is regular user" do
      it "limits scope to user themselves only" do
        create_list(:user, 3)
        scope = UserPolicy.new(User, user: user).apply_scope(User.all, type: :active_record_relation)
        expect(scope.count).to eq(1)
        expect(scope.first).to eq(user)
      end
    end

    context "when user is not authenticated" do
      it "returns empty scope" do
        create_list(:user, 3)
        scope = UserPolicy.new(User, user: nil).apply_scope(User.all, type: :active_record_relation)
        expect(scope.count).to eq(0)
      end
    end
  end

  describe "Pre-check authentication" do
    context "for update_preferences action" do
      it "denies unauthenticated users" do
        policy = UserPolicy.new(user, user: nil)
        expect(policy.update_preferences?).to be_falsy
      end

      it "allows authenticated users to update their own preferences" do
        policy = UserPolicy.new(user, user: user)
        expect(policy.update_preferences?).to be true
      end

      it "denies authenticated users from updating other users' preferences" do
        policy = UserPolicy.new(other_user, user: user)
        expect(policy.update_preferences?).to be false
      end
    end
  end

  describe "Private methods" do
    let(:policy) { UserPolicy.new(user, user: user) }

    describe "#owner?" do
      it "returns true when user is the owner" do
        expect(policy.send(:owner?)).to be true
      end

      it "returns false when user is not the owner" do
        other_policy = UserPolicy.new(other_user, user: user)
        expect(other_policy.send(:owner?)).to be false
      end
    end

    describe "#self_user?" do
      it "returns true when user is the same as record" do
        expect(policy.send(:self_user?)).to be true
      end

      it "returns false when user is different from record" do
        other_policy = UserPolicy.new(other_user, user: user)
        expect(other_policy.send(:self_user?)).to be false
      end
    end
  end
end
