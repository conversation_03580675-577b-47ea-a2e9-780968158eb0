module WechatInteractions
  extend ActiveSupport::Concern

  included do
    wechat_responder account_from_request: proc { |request|
      controller_name = request.controller_class.name
      if controller_name == "Wechat::ServiceController"
        :service  # Name of your service account configuration
      elsif controller_name == "Wechat::SubscriptionController"
        :default  # Name of your subscription account configuration
      else
        :default  # Default account if no match
      end
    }

    # 允许未认证访问微信回调，因为这些请求来自微信服务器
    allow_unauthenticated_access only: [ :show, :create ]

    # 处理取消关注事件
    on :event, with: "unsubscribe" do |request|
      request.session.hash_store[:unsubscribe_time] = Time.current
      # 用户已取消关注，无需回复消息
    end

    # 处理文本消息
    #   # NOTE 示例：特定消息触发异步任务
    #     if content.downcase.include?("async_task")
    #       # 使用 Solid Queue (Active Job) 异步处理
    #       WechatMessageJob.perform_later(message_content: content, from_user: request[:FromUserName])
    #       # 使用 I18n 获取回复文本
    #       request.reply.text I18n.t("wechats.async_task_received")
    #     end
    on :text do |request|
      content = request[:Content].strip

      if content.present?
        # 常规命令处理
        case content.downcase # 不区分大小写
        when "help"
          request.reply.text "Available commands: help, account, about, home"
        when "about"
          request.reply.text "This is about page https://codingirlsclub.com/about"
        when "home"
          request.reply.text "This is home page https://codingirlsclub.com"
        when "account"
          handle_account_cmd(request)
        else
          request.reply.text "Unknown command: #{content}. Try 'help'."
        end
      end
    end

    # 公共点击菜单事件处理
    on :click, with: "AI_JUST_CHAT" do |request, key|
      request.reply.text "您点击了菜单: #{key}"
    end

    on :click, with: "USER_ACCOUNT" do |request, _key|
      handle_account_cmd(request)
    end

    # Fallback 处理，捕获未匹配的消息类型或事件
    on :fallback do |request|
      # 使用 I18n 获取回复文本
      Rails.logger.warn "Unhandled message/event in fallback: #{request.message_hash}"
      request.reply.text I18n.t("wechats.fallback_reply")
    end
  end

  # 将 WeChat API 返回的用户信息包装成 OmniAuth 格式的 Hash
  # 然后可以传给 User.create_from_oauth
  def convert_wechat_api_to_omniauth(user_info)
    OmniAuth::AuthHash.new(
      provider: "wechat",
      uid:      user_info["openid"] || user_info[:openid],
      info: {
        nickname: user_info["nickname"],
        name:     user_info["nickname"],
        image:    user_info["headimgurl"],
        email:    nil
      },
      extra: {
        raw_info: user_info
      }
    )
  end

  # 找到或创建用户及其 WeChat 身份
  # NOTE: 这个方法只能在绑定了微信开放平台的公众号使用，目前的订阅号为没有认证的个人号，没有 unionid
  def find_or_create_user_with_identity(openid)
    begin
      # Get the current account from the request
      current_account = self.class.account_from_request&.call(request)
      # Use the current account for the API call
      # NOTE 目前服务号和订阅号都还没有这个api的权限，会出现 48001 错误
      user_info = wechat(current_account).user(openid)
      return [ nil, nil ] unless user_info && user_info["openid"]

      unionid   = user_info["unionid"]
      identity  = OmniAuthIdentity.by_provider_uid("wechat", openid).first

      if unionid && (identity.nil? || identity.unionid.blank?)
        other_user = OmniAuthIdentity.by_provider_unionid("wechat", unionid).first&.user
      end

      if identity.nil?
        user     = other_user || User.create_from_oauth(convert_wechat_api_to_omniauth(user_info))
        identity = OmniAuthIdentity.create!(
          provider:  "wechat",
          uid:       openid,
          unionid:   unionid,
          user:      user
        )
      else
        identity.update!(unionid: unionid) if unionid && identity.unionid.blank?
      end

      [ user_info, identity ]
    rescue => e
      Rails.logger.error "Error in find_or_create_user_with_identity: #{e.message}"
      [ nil, nil ]
    end
  end

  # 将 WeChat 数据存入 session
  def store_wechat_session(request, user_info, identity)
    return unless user_info && identity

    store = request.session.hash_store
    store[:user_info]        = user_info
    store[:service_identity] = identity
    store[:subscribe_time]   = user_info["subscribe_time"]
  end

  # 处理 account 命令，查询或创建账号
  def handle_account_cmd(request)
    openid = request[:FromUserName]
    begin
      user_info, identity = find_or_create_user_with_identity(openid)

      unless identity&.user
        request.reply.text "抱歉，获取账户信息失败，请稍后再试。"
        return
      end

      store_wechat_session(request, user_info, identity)
      user = identity.user

      reply_text = <<~MSG
        您的帐号:
        用户名: #{user.username}
        邮箱: #{user.email_address || '未设置'}
        点击链接登录并管理您的帐号:
        https://codingirlsclub.com/auth/wechat?origin_path=https://codingirlsclub.com/users/settings
        关注时间: #{Time.at(user_info["subscribe_time"].to_i).strftime("%Y-%m-%d %H:%M:%S")}
      MSG

      request.reply.text reply_text
    rescue => e
      Rails.logger.error "Error in handle_account_cmd: #{e.message}"
      request.reply.text "抱歉，处理您的请求时出现错误，请稍后再试。"
    end
  end
end
