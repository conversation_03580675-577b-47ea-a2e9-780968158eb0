# This file is copied to spec/ when you run 'rails generate rspec:install'
require 'spec_helper'
ENV['RAILS_ENV'] ||= 'test'
require_relative '../config/environment'
# Prevent database truncation if the environment is production
abort("The Rails environment is running in production mode!") if Rails.env.production?
# Uncomment the line below in case you have `--require rails_helper` in the `.rspec` file
# that will avoid rails generators crashing because migrations haven't been run yet
# return unless Rails.env.test?
require 'rspec/rails'
# Add additional requires below this line. Rails is not loaded until this point!
require 'capybara/rspec'
require 'action_policy/rspec'
require "action_policy/rspec/dsl"
require "action_policy/test_helper"
require 'webmock/rspec'

# Requires supporting ruby files with custom matchers and macros, etc, in
# spec/support/ and its subdirectories. Files matching `spec/**/*_spec.rb` are
# run as spec files by default. This means that files in spec/support that end
# in _spec.rb will both be required and run as specs, causing the specs to be
# run twice. It is recommended that you do not name files matching this glob to
# end with _spec.rb. You can configure this pattern with the --pattern
# option on the command line or in ~/.rspec, .rspec or `.rspec-local`.
#
# The following line is provided for convenience purposes. It has the downside
# of increasing the boot-up time by auto-requiring all files in the support
# directory. Alternatively, in the individual `*_spec.rb` files, manually
# require only the support files necessary.
#
Rails.root.glob('spec/support/**/*.rb').sort_by(&:to_s).each { |f| require f }

# Checks for pending migrations and applies them before tests are run.
# If you are not using ActiveRecord, you can remove these lines.
begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  abort e.to_s.strip
end
RSpec.configure do |config|
  # 禁用所有未经 stub 的网络连接，但允许本地连接
  WebMock.disable_net_connect!(allow_localhost: true)

  # Rails 8 的 fixture 路径配置
  # Remove this line if you're not using ActiveRecord or ActiveRecord fixtures
  config.fixture_paths = [
    Rails.root.join('spec/fixtures')
  ]

  # 添加时间旅行支持
  config.include ActiveSupport::Testing::TimeHelpers

  # Include our RequestSpecHelpers for request specs
  config.include RequestSpecHelpers, type: :request

  # If you're not using ActiveRecord, or you'd prefer not to run each of your
  # examples within a transaction, remove the following line or assign false
  # instead of true.
  config.use_transactional_fixtures = true

  # You can uncomment this line to turn off ActiveRecord support entirely.
  # config.use_active_record = false

  # RSpec Rails uses metadata to mix in different behaviours to your tests,
  # for example enabling you to call `get` and `post` in request specs. e.g.:
  #
  #     RSpec.describe UsersController, type: :request do
  #       # ...
  #     end
  #
  # The different available types are documented in the features, such as in
  # https://rspec.info/features/7-0/rspec-rails
  #
  # You can also this infer these behaviours automatically by location, e.g.
  # /spec/models would pull in the same behaviour as `type: :model` but this
  # behaviour is considered legacy and will be removed in a future version.
  #
  # To enable this behaviour uncomment the line below.
  config.infer_spec_type_from_file_location!

  # Filter lines from Rails gems in backtraces.
  config.filter_rails_from_backtrace!
  # arbitrary gems may also be filtered via:
  # config.filter_gems_from_backtrace("gem name")

  # Add Action Policy test helpers
  # 为不同类型的测试添加 Action Policy 测试辅助方法
  %i[policy feature].each do |type|
    config.include ActionPolicy::RSpec::DSL, type: type
    config.include ActionPolicy::TestHelper, type: type
  end

  # 添加测试优化配置
  # 允许使用 :focus 标签来运行特定的测试
  config.filter_run_when_matching :focus
  # 保存测试用例状态到指定文件，以便下次运行
  config.example_status_persistence_file_path = "spec/examples.txt"
  # 随机化测试用例的执行顺序
  config.order = :random
  # 记录最慢的 10 个测试用例
  config.profile_examples = 10

  # 测试套件运行状态提示
  config.before(:suite) do
    puts "\n🚀 Starting test suite..."
    # 强制测试环境使用英文
    I18n.locale = :en
  end
  config.after(:suite) do
    puts "\n✨ Test suite completed"
  end

  # 为带有 :js 元数据的测试自动切换驱动
  config.before(:each, type: :feature) do |example|
    if example.metadata[:js]
      Capybara.current_driver = Capybara.javascript_driver
      # 尝试为 JS 驱动设置请求头 (更通用的方法)
      # 注意: 某些驱动可能不支持此方法
      begin
        page.driver.add_headers('Accept-Language' => 'en')
      rescue NoMethodError, NotImplementedError
        # 如果驱动不支持 add_headers, 尝试其他方法或记录警告
        # puts "Warning: Could not set Accept-Language header for JS driver using add_headers."
        # 可以在这里尝试之前在 capybara.rb 中添加的 options 方法，但它似乎也未生效
      end
    else
      Capybara.current_driver = Capybara.default_driver
      # 为 rack_test 驱动设置请求头
      Capybara.current_session.driver.header 'Accept-Language', 'en'
    end
  end

  # 清理测试状态
  config.after(:each, type: :feature) do
    Capybara.reset_session!
    Capybara.use_default_driver
  end
end

# 将 Capybara 配置移到单独的文件
require_relative 'support/capybara'
