module Ai
  class ConversationsController < ApplicationController
    before_action :set_conversation, only: [ :show, :update, :destroy, :switch_assistant, :clear_conversation ]

    def index
      @conversations = current_user.conversations
        .order(created_at: :desc)
        .page(params[:page])
        .per(10)
      authorize! @conversations
    end

    def new
      @conversation = current_user.conversations.new
      authorize! @conversation
    end

    def create
      @conversation = current_user.conversations.new(conversation_params)
      authorize! @conversation
      if @conversation.save
        redirect_to ai_conversation_path(@conversation)
      else
        render :new
      end
    end

    def show
      # authorize! is still good practice for verify_authorized,
      # but the primary check now happens in set_conversation.
      # Revert to simple authorize! without explicit policy.
      authorize! @conversation
      @current_assistant = @conversation.current_assistant
    end

    def update
      authorize! @conversation
      if @conversation.update(conversation_params)
        redirect_to ai_conversation_path(@conversation)
      else
        render :show
      end
    end

    def destroy
      authorize! @conversation
      @conversation.destroy
      redirect_to ai_conversations_path, notice: "Conversation deleted"
    end

    def latest
      latest_conversation = current_user.conversations.latest.first

      if latest_conversation.present?
        redirect_to ai_conversation_path(latest_conversation)
      else
        redirect_to new_ai_conversation_path
      end
    end

    # PATCH /ai/conversations/:id/switch_assistant
    def switch_assistant
      authorize! @conversation
      new_assistant_id = params[:assistant_id] # 从参数获取新的助手 ID

      if new_assistant_id.present? && @conversation.update(assistant_id: new_assistant_id)
        # 成功更新后重定向回会话页面，并显示成功提示
        # 使用 Turbo Stream 响应可能提供更好的用户体验，但重定向也能工作
        redirect_to ai_conversation_path(@conversation), notice: "Assistant switched successfully."
      else
        # 如果更新失败（例如，助手ID无效），则渲染当前页面并显示错误
        # 或者重定向并显示错误 flash 消息
        flash.now[:alert] = "Failed to switch assistant."
        # 重新加载必要的实例变量以渲染 show 页面
        @current_assistant = @conversation.current_assistant # 确保 @current_assistant 被设置
        render :show, status: :unprocessable_entity
      end
    end

    # POST /ai/conversations/:id/clear_conversation
    def clear_conversation # 重命名方法
      authorize! @conversation

      # 删除所有非系统消息
      @conversation.messages.where.not(role: :system).destroy_all

      # 使用专门的清除上下文缓存方法
      Ai::ChatService.clear_context_cache(@conversation)

      # 重定向并显示通知
      # 注意：这里需要确保页面在重定向后能正确刷新显示空消息列表
      # Turbo Stream 可能更适合实时更新，但重定向是直接的方式
      redirect_to ai_conversation_path(@conversation), notice: "Conversation cleared successfully." # 更新提示信息
    end

    # 移除 clear_context 方法

    private

    def set_conversation
      # Apply authorization scope to Conversation.all *before* finding the record
      @conversation = authorized(Conversation.all).find(params[:id])
    rescue ActiveRecord::RecordNotFound
      # Handle exception directly within the method
      flash[:error] = "You are not authorized" # Match test expectation
      # IMPORTANT: Use 'and return' to stop filter chain execution
      redirect_to root_path and return
    end

    def conversation_params
      params.require(:conversation).permit(:title, :assistant_id)
    end
  end
end
