require 'rails_helper'

RSpec.describe Assistant, type: :model do
  describe '关联关系' do
    it { is_expected.to belong_to(:user) }
    it { is_expected.to have_many(:conversations).dependent(:destroy) }
    it { is_expected.to have_many(:messages) }
  end

  describe '验证' do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_length_of(:name).is_at_most(100) }
    it { is_expected.to validate_presence_of(:instructions) }
    it { is_expected.to validate_presence_of(:tool_choice) }

    # 将自定义错误消息的验证替换为手动测试
    it '验证tool_choice在指定值范围内' do
      valid_choices = %w[auto none any]

      # 验证有效值被接受
      valid_choices.each do |choice|
        assistant = build(:assistant, tool_choice: choice)
        assistant.valid? # 触发验证
        expect(assistant.errors[:tool_choice]).not_to include("#{choice} is not a valid tool choice")
      end

      # 验证无效值被拒绝并给出正确的错误消息
      invalid_choice = 'invalid_choice'
      assistant = build(:assistant, tool_choice: invalid_choice)
      assistant.valid? # 触发验证
      expect(assistant.errors[:tool_choice]).to include("#{invalid_choice} is not a valid tool choice")
    end

    context '工具选择不是"none"时' do
      subject { build(:assistant, tool_choice: 'auto') }
      it { is_expected.to validate_presence_of(:tools) }
    end

    context '工具选择是"none"时' do
      subject { build(:assistant, tool_choice: 'none', tools: []) }
      it { is_expected.not_to validate_presence_of(:tools) }
    end
  end

  describe '#enabled_tools' do
    context 'tool_choice 是 "none" 时' do
      let(:assistant) { create(:assistant, :no_tools) }

      it '返回空数组' do
        expect(assistant.enabled_tools).to eq([])
      end
    end

    context 'tool_choice 不是 "none" 时' do
      let(:assistant) { create(:assistant, tool_choice: 'auto', tools: [ 'Langchain::Tool::Calculator' ]) }

      context '工具类存在时' do
        let(:tool_instance) { instance_double('Langchain::Tool::Calculator') }
        let(:calculator_class) { Class.new }

        before do
          stub_const('Langchain::Tool::Calculator', calculator_class)
          allow(calculator_class).to receive(:new).and_return(tool_instance)
        end

        it '返回工具实例数组' do
          expect(assistant.enabled_tools).to eq([ tool_instance ])
        end

        it '缓存工具实例' do
          first_call = assistant.enabled_tools
          expect(assistant.enabled_tools).to be(first_call)
        end
      end

      context '工具类不存在时' do
        let(:assistant) { create(:assistant, tool_choice: 'auto', tools: [ 'Langchain::Tool::NonExistentTool' ]) }
        it '跳过无效的工具' do
          expect(assistant.enabled_tools).to eq([])
        end
      end
    end
  end

  describe '#llm' do
    let(:assistant) { create(:assistant) }
    let(:llm) { double('LLM') }

    before do
      allow(Ai::LLMService).to receive(:create_llm).and_return(llm)
    end

    it '返回通过LLMService创建的语言模型' do
      expect(assistant.llm).to eq(llm)
    end

    it '缓存LLM实例' do
      first_call = assistant.llm
      expect(assistant.llm).to be(first_call) # 测试对象身份
    end
  end

  describe '#set_langchain_assistant' do
    let(:assistant) { create(:assistant) }
    let(:assistant_message) { build(:message, :assistant, messageable: build(:conversation)) }
    let(:llm) { double('LLM') }
    let(:tool_instance) { instance_double('Langchain::Tool::Calculator') }
    let(:tools) { [ tool_instance ] }
    # 为 set_langchain_assistant 调用添加 user_message
    let(:user_message) { create(:message, :user, messageable: assistant_message.messageable) }

    before do
      stub_const('Langchain::Tool::Calculator', Class.new)
      allow(assistant).to receive(:llm).and_return(llm)
      allow(assistant).to receive(:enabled_tools).and_return(tools)
      # 模拟 Langchain::Assistant.new 返回一个具有 messages 方法的对象
      # messages 方法返回的对象需要响应 clear 和 <<
      mock_messages = double('Langchain::Messages')
      allow(mock_messages).to receive(:clear)
      allow(mock_messages).to receive(:<<)
      mock_langchain_assistant = double('Langchain::Assistant', messages: mock_messages)
      allow(Langchain::Assistant).to receive(:new).and_return(mock_langchain_assistant)
      # 模拟 load_cached_langchain_assistant_messages 以隔离测试
      allow(assistant).to receive(:load_cached_langchain_assistant_messages)
    end

    it '初始化Langchain助手并设置回调' do
      expect(Langchain::Assistant).to receive(:new).with(
        id: assistant.id,
        llm: llm,
        instructions: assistant.instructions,
        tools: tools,
        tool_choice: assistant.tool_choice,
        add_message_callback: instance_of(Method),
        tool_execution_callback: instance_of(Method)
      )
      # 添加 user_message_id 参数
      assistant.set_langchain_assistant(assistant_message, user_message_id: user_message.id)
    end

    it '设置实例变量' do
      # 添加 user_message_id 参数
      assistant.set_langchain_assistant(assistant_message, user_message_id: user_message.id)
      expect(assistant.instance_variable_get(:@assistant_message)).to eq(assistant_message)
      # 更新检查的实例变量名为 @messageable_context
      expect(assistant.instance_variable_get(:@messageable_context)).to eq(assistant_message.messageable)
      expect(assistant.instance_variable_get(:@current_tool_usages)).to eq({})
    end

    it '调用 load_cached_langchain_assistant_messages' do
       expect(assistant).to receive(:load_cached_langchain_assistant_messages).with(user_message_id: user_message.id)
       assistant.set_langchain_assistant(assistant_message, user_message_id: user_message.id)
    end
  end

  describe '#handle_new_message' do
    let(:assistant) { create(:assistant) }
    let(:conversation) { create(:conversation, assistant: assistant) }
    # 使用 create 而不是 build 来确保 assistant_message 有 ID 并且状态可以被追踪
    let!(:assistant_message) { create(:message, :assistant, content: "Initial content", status: :pending, messageable: conversation) }
    # 为 set_langchain_assistant 调用添加 user_message
    let(:user_message) { create(:message, :user, messageable: conversation) }
    let(:langchain_message) { double('Langchain::Message') }

    before do
      # 添加 user_message_id 参数
      assistant.set_langchain_assistant(assistant_message, user_message_id: user_message.id)
      # 模拟 append_new_message_to_cache 以隔离测试
      allow(assistant).to receive(:append_new_message_to_cache)
    end

    context '消息角色是user时' do
      before do
        allow(langchain_message).to receive(:role).and_return('user')
      end

      it '不更新数据库消息' do
        expect(assistant_message).not_to receive(:update!)
        assistant.handle_new_message(langchain_message)
      end
    end

    context '消息角色是assistant时' do
      before do
        allow(langchain_message).to receive(:role).and_return('assistant')
      end

      context '有工具调用且没有内容时' do
        # 更新模拟数据以包含 function 结构，修复视图错误
        let(:tool_calls_data) { [ { 'type' => 'function', 'id' => 'call_123', 'function' => { 'name' => 'get_weather', 'arguments' => '{}' } } ] }
        before do
          allow(langchain_message).to receive(:tool_calls).and_return(tool_calls_data)
          allow(langchain_message).to receive(:content).and_return('')
          # 允许 update! 被调用，以便 change 匹配器可以工作
          allow(assistant_message).to receive(:update!).and_call_original
        end

         # 验证 status 被设置，并且 update! 不包含 status
         it '更新消息为处理工具调用状态' do
          # 验证 status 在调用 update! 前被设置 (使用符号比较)
          expect { assistant.handle_new_message(langchain_message) }
            .to change { assistant_message.status }.from(:pending).to(:processing)
          # 验证 update! 的参数 (不包含 status)
          expect(assistant_message).to have_received(:update!).with(
             content: I18n.t("ai.assistants.processing_message"),
             tool_calls: tool_calls_data
           )
        end
      end

      context '是普通响应或最终响应时' do
         let(:final_content) { 'Final response' }
        before do
          allow(langchain_message).to receive(:tool_calls).and_return(nil)
          allow(langchain_message).to receive(:content).and_return(final_content)
          # 允许 update! 被调用
          allow(assistant_message).to receive(:update!).and_call_original
        end

         # 验证 status 被设置，并且 update! 不包含 status
         it '更新消息为已完成状态' do
          # 验证 status 在调用 update! 前被设置 (使用符号比较)
          expect { assistant.handle_new_message(langchain_message) }
            .to change { assistant_message.status }.from(:pending).to(:completed)
          # 验证 update! 的参数 (不包含 status)
          expect(assistant_message).to have_received(:update!).with(
             content: final_content,
             tool_calls: nil
           )
        end
      end
    end

    context '消息角色是tool时' do
      before do
        allow(langchain_message).to receive(:role).and_return('tool')
      end

      context '有工具调用ID时' do
        let(:tool_call_id) { 'call_123' }
        before do
          allow(langchain_message).to receive(:tool_call_id).and_return(tool_call_id)
          # 模拟 handle_tool_response 以隔离测试
          allow(assistant).to receive(:handle_tool_response)
        end

        it '处理工具响应' do
          expect(assistant).to receive(:handle_tool_response).with(langchain_message)
          assistant.handle_new_message(langchain_message)
        end
      end

      context '没有工具调用ID时' do
        before do
          allow(langchain_message).to receive(:tool_call_id).and_return(nil)
          allow(assistant).to receive(:handle_tool_response) # Stub
        end

        it '不处理工具响应' do
          expect(assistant).not_to receive(:handle_tool_response)
          assistant.handle_new_message(langchain_message)
        end
      end
    end

    # 统一验证 append_new_message_to_cache 的调用
    it '为所有角色调用 append_new_message_to_cache' do
      %w[user assistant tool].each do |role|
        # RSpec 会在每个 example 前自动重置 doubles
        allow(assistant).to receive(:append_new_message_to_cache) # 重新设置模拟

        allow(langchain_message).to receive(:role).and_return(role)
        # 为 assistant 和 tool 角色设置必要属性以通过 case 语句
        allow(langchain_message).to receive(:tool_calls).and_return(nil) if role == 'assistant'
        allow(langchain_message).to receive(:content).and_return('some content') if role == 'assistant'
        allow(langchain_message).to receive(:tool_call_id).and_return('some_id') if role == 'tool'
        allow(assistant).to receive(:handle_tool_response) if role == 'tool' # Stub
        allow(assistant_message).to receive(:update!) if role == 'assistant' # Stub

        expect(assistant).to receive(:append_new_message_to_cache).with(langchain_message)
        assistant.handle_new_message(langchain_message)
      end
    end
  end

  describe '#handle_tool_execution' do
    let(:assistant) { create(:assistant) }
    let(:conversation) { create(:conversation, assistant: assistant) }
    let(:assistant_message) { create(:message, :assistant, messageable: conversation) }
    let(:tool_call_id) { 'call_123' }
    let(:tool_name) { 'weather' }
    let(:method_name) { 'get_forecast' }
    let(:arguments) { { "location" => "Beijing" } }
    let(:function_name) { "weather#get_forecast" }
    let(:tool_message) { instance_double(Message, tool_usages: tool_usages) }
    let(:tool_usages) { class_double(ToolUsage, 'tool_usages') }
    let(:tool_usage) { instance_double(ToolUsage) }
    let(:frozen_time) { Time.new(2023, 1, 1, 12, 0, 0).in_time_zone.freeze }
    # 为 set_langchain_assistant 调用添加 user_message
    let(:user_message) { create(:message, :user, messageable: conversation) }

    before do
      # 添加 user_message_id 参数
      assistant.set_langchain_assistant(assistant_message, user_message_id: user_message.id)
      allow(conversation.messages).to receive(:create_or_find_by!).and_yield(tool_message).and_return(tool_message)
      allow(tool_message).to receive(:content=)
      allow(tool_usages).to receive(:create_or_find_by!).and_yield(tool_usage).and_return(tool_usage)
      allow(tool_usage).to receive(:arguments=)
      allow(tool_usage).to receive(:started_at=)
      allow(tool_usage).to receive(:status=)
      # 使用travel_to固定时间点
      travel_to(frozen_time) do
        # 测试将在这个时间点执行
      end
    end

    it '创建工具消息' do
      expect(conversation.messages).to receive(:create_or_find_by!).with(
        role: "tool",
        tool_call_id: tool_call_id
      )

      assistant.handle_tool_execution(tool_call_id, tool_name, method_name, arguments)
    end

    it '设置工具消息内容' do
      expect(tool_message).to receive(:content=).with(/^Executing #{function_name} with parameters:/)

      assistant.handle_tool_execution(tool_call_id, tool_name, method_name, arguments)
    end

    it '创建工具使用记录' do
      expect(tool_usages).to receive(:create_or_find_by!).with(function_name: function_name)

      assistant.handle_tool_execution(tool_call_id, tool_name, method_name, arguments)
    end

    it '设置工具使用记录属性' do
      travel_to(frozen_time) do
        expect(tool_usage).to receive(:arguments=).with(arguments)
        expect(tool_usage).to receive(:started_at=).with(Time.current)
        expect(tool_usage).to receive(:status=).with(:pending)

        assistant.handle_tool_execution(tool_call_id, tool_name, method_name, arguments)
      end
    end

    it '将工具使用记录存储到哈希表' do
      assistant.handle_tool_execution(tool_call_id, tool_name, method_name, arguments)

      expect(assistant.instance_variable_get(:@current_tool_usages)[tool_call_id]).to eq(tool_usage)
    end

    it '返回原始参数' do
      result = assistant.handle_tool_execution(tool_call_id, tool_name, method_name, arguments)

      expect(result).to eq(arguments)
    end
  end

  describe '#handle_tool_response' do
    let(:assistant) { create(:assistant) }
    let(:conversation) { create(:conversation, assistant: assistant) }
    let(:assistant_message) { create(:message, :assistant, messageable: conversation) }
    let(:tool_message) { instance_double(Message, 'tool_message') }
    let(:tool_usage) { instance_double(ToolUsage, 'tool_usage', message: tool_message) }
    let(:tool_call_id) { 'call_123' }
    let(:langchain_message) { double('Langchain::Message', content: 'Tool response', tool_call_id: tool_call_id) }
    let(:frozen_time) { Time.new(2023, 1, 1, 12, 0, 0).in_time_zone.freeze }
    # 为 set_langchain_assistant 调用添加 user_message
    let(:user_message) { create(:message, :user, messageable: conversation) }

    before do
      # 添加 user_message_id 参数
      assistant.set_langchain_assistant(assistant_message, user_message_id: user_message.id)
      assistant.instance_variable_set(:@current_tool_usages, { tool_call_id => tool_usage })
      allow(tool_message).to receive(:update!)
    end

    context '存在匹配的工具使用记录' do
      it '更新工具使用记录状态为成功' do
        travel_to(frozen_time) do
          expect(tool_usage).to receive(:update!).with(
            status: :success,
            result: 'Tool response',
            completed_at: Time.current
          )

          assistant.send(:handle_tool_response, langchain_message)
        end
      end

      it '更新工具消息内容' do
        allow(tool_usage).to receive(:update!)
        expect(tool_message).to receive(:update!).with(content: 'Tool response')

        assistant.send(:handle_tool_response, langchain_message)
      end

      it '从哈希表中移除工具使用记录' do
        allow(tool_usage).to receive(:update!)
        allow(tool_message).to receive(:update!)

        assistant.send(:handle_tool_response, langchain_message)
        expect(assistant.instance_variable_get(:@current_tool_usages)).not_to have_key(tool_call_id)
      end

      context '错误消息' do
        let(:error_message) { double('Langchain::Message', content: "#{Assistant::ERROR_PREFIXES.first} Something went wrong", tool_call_id: tool_call_id) }

        it '更新工具使用记录状态为失败' do
          travel_to(frozen_time) do
            expect(tool_usage).to receive(:update!).with(
              status: :failed,
              result: "#{Assistant::ERROR_PREFIXES.first} Something went wrong",
              completed_at: Time.current
            )

            assistant.send(:handle_tool_response, error_message)
          end
        end
      end
    end

    context '不存在匹配的工具使用记录' do
      let(:unknown_message) { double('Langchain::Message', content: 'Unknown response', tool_call_id: 'unknown_id') }

      it '不执行任何操作' do
        expect(tool_usage).not_to receive(:update!)

        assistant.send(:handle_tool_response, unknown_message)
      end
    end
  end

  # 移除缓存逻辑的单元测试，这些应通过集成/功能测试覆盖
  # describe '缓存逻辑' do ... end

  # 新增：测试消息构建逻辑 (保留，因为它测试的是模型的纯逻辑)
  describe '#build_langchain_messages_from_history' do
     let(:assistant) { create(:assistant) }
     let(:llm_adapter) { double('Langchain::LLM::Adapter') }
     let(:mock_langchain_assistant) { double('Langchain::Assistant', llm_adapter: llm_adapter) }
     # 使用可序列化的哈希或真实对象模拟 Langchain 消息
     let(:user_msg_obj) { { role: 'user', content: 'User message 1', id: 1 } }
     let(:assistant_msg_obj) { { role: 'assistant', content: 'Assistant response 1', id: 2 } }
     let(:tool_msg_obj) { { role: 'tool', content: 'Tool result 1', tool_call_id: 'call1', id: 3 } }
     let(:user_msg_2_obj) { { role: 'user', content: 'User message 2', id: 5 } }

     before do
       assistant.instance_variable_set(:@langchain_assistant, mock_langchain_assistant)
       # 允许 build_message 接收任何哈希参数并根据角色返回相应的哈希
       allow(llm_adapter).to receive(:build_message) do |args|
         mock_msg = args.dup
         # 模拟 Langchain 消息对象可能具有的 id 属性（如果需要）
         mock_msg[:id] = case args[:content]
         when 'User message 1' then 1
         when 'Assistant response 1' then 2
         when 'Tool result 1' then 3
         when 'User message 2' then 5
         else nil
         end
         # 模拟 instance_variable_set 的行为（如果需要验证）
         allow(mock_msg).to receive(:instance_variable_set) do |var, val|
           mock_msg[var.to_s.delete('@').to_sym] = val if var == :@id
         end
         mock_msg
       end
     end

     let(:history) do
       [
         { id: 1, role: 'user', content: 'User message 1' },
         { id: 2, role: 'assistant', content: 'Assistant response 1', tool_calls: [ { id: 'call1' } ] }, # 包含 tool_calls 但不应传递给 build_message
         { id: 3, role: 'tool', content: 'Tool result 1', tool_call_id: 'call1' },
         { id: 4, role: 'system', content: 'System prompt' }, # 应被忽略
         { id: 5, role: 'user', content: 'User message 2' } # 用于测试排除
       ]
     end

     it '正确构建 Langchain 消息对象数组' do
       messages = assistant.send(:build_langchain_messages_from_history, history)
       # 期望结果应包含 user_msg_obj, assistant_msg_obj, tool_msg_obj, user_msg_2_obj (因为 exclude_id 未设置)
       expect(messages.count).to eq(4) # user1, assistant1, tool1, user2
       # 验证返回的哈希内容
       expect(messages).to include(
         hash_including(role: 'user', content: 'User message 1'),
         hash_including(role: 'assistant', content: 'Assistant response 1'),
         hash_including(role: 'tool', content: 'Tool result 1', tool_call_id: 'call1'),
         hash_including(role: 'user', content: 'User message 2')
       )
     end

     it '忽略 system 消息' do
       messages = assistant.send(:build_langchain_messages_from_history, history)
       expect(messages.none? { |m| m[:role] == 'system' }).to be true
     end

     it '为 tool 消息传递 tool_call_id' do
       # 验证 build_message 被调用时包含正确的 tool_call_id
       expect(llm_adapter).to receive(:build_message).with(hash_including(role: 'tool', tool_call_id: 'call1')).and_return(tool_msg_obj)
       assistant.send(:build_langchain_messages_from_history, history)
     end

     it '不为 assistant 消息传递 tool_calls' do
        # 验证 build_message 对 assistant 调用时不包含 tool_calls
        expect(llm_adapter).to receive(:build_message).with(hash_including(role: 'assistant')).and_return(assistant_msg_obj)
        expect(llm_adapter).not_to receive(:build_message).with(hash_including(role: 'assistant', tool_calls: anything))
        assistant.send(:build_langchain_messages_from_history, history)
     end

     it '排除指定的 exclude_message_id' do
       messages = assistant.send(:build_langchain_messages_from_history, history, exclude_message_id: 5)
       expect(messages.count).to eq(3) # User message 2 (id: 5) 应被排除
       # 验证返回的消息对象中不包含 id 为 5 的消息
       expect(messages.map { |m| m[:id] }).not_to include(5)
     end

     it '为消息对象设置原始 ID' do
        # 验证返回的哈希包含正确的 ID
        messages = assistant.send(:build_langchain_messages_from_history, history)
        expect(messages.find { |m| m[:content] == 'User message 1' }[:id]).to eq(1)
        expect(messages.find { |m| m[:content] == 'Assistant response 1' }[:id]).to eq(2)
        expect(messages.find { |m| m[:content] == 'Tool result 1' }[:id]).to eq(3)
     end
  end


  describe '#format_tool_execution_message' do
    let(:assistant) { create(:assistant) }
    let(:function_name) { 'weather#get_forecast' }

    context '参数是Hash' do
      let(:arguments) { { "location" => "Beijing", "days" => 3 } }

      it '格式化为JSON字符串' do
        message = assistant.send(:format_tool_execution_message, function_name, arguments)
        expect(message).to include(function_name)
        expect(message).to include('"location": "Beijing"')
        expect(message).to include('"days": 3')
      end
    end

    context '参数是有效的JSON字符串' do
      let(:arguments) { '{"location":"Beijing","days":3}' }

      it '格式化为美化的JSON字符串' do
        message = assistant.send(:format_tool_execution_message, function_name, arguments)
        expect(message).to include(function_name)
        expect(message).to include('"location": "Beijing"')
        expect(message).to include('"days": 3')
      end
    end

    context '参数是无效的JSON字符串' do
      let(:arguments) { 'invalid json' }

      it '不尝试解析直接返回原始字符串' do
        message = assistant.send(:format_tool_execution_message, function_name, arguments)
        expect(message).to include(function_name)
        expect(message).to include('invalid json')
      end
    end
  end
end
