class KnowledgeBase < ApplicationRecord
  # 使用 concern 分离关注点
  include Archivable
  include Cacheable
  include Visibility  # 将可见性相关逻辑移到单独的 concern
  include SoftDeletable  # 将软删除相关逻辑移到单独的 concern

  MAX_POSTS_LIMIT = 1000

  # 序列化 metadata
  serialize :metadata, coder: JSON

  # 所有者关联
  belongs_to :owner, class_name: "User"

  # 团队资源访问关联
  has_many :team_resource_accesses, as: :resource, dependent: :destroy
  has_many :team_members, through: :team_resource_accesses
  has_many :teams, through: :team_members

  # Post 关联
  has_many :knowledge_base_posts, dependent: :destroy
  has_many :posts, through: :knowledge_base_posts

  # BaseUnit 关联
  has_many :base_units, through: :posts

  # 回调
  before_destroy :cleanup_team_resource_accesses

  # TODO 添加回调
  # before_destroy :check_dependencies
  # after_create :notify_creation
  # after_update :track_changes

  # 添加验证
  validates :name, presence: true,
                  uniqueness: { scope: [ :owner_id, :deleted_at ] },
                  length: { minimum: 2, maximum: 100 }
  validates :visibility, presence: true
  validates :description, length: { maximum: 5000 }

  # 添加所有者相关的验证
  validate :ensure_owner_presence
  validate :validate_name_uniqueness_per_owner
  # Only validate team presence on create or if visibility remains team
  validate :validate_teams_presence_if_team_visibility, on: :create
  # Renamed validation for clarity and adjusted logic
  validate :validate_teams_presence_if_still_team_visibility, on: :update
  validate :team_associations_only_for_team_visibility
  validate :validate_teams_presence_when_becoming_team_visible, on: :update # <--- 添加这一行
  # 预加载关联
  scope :with_posts, -> { includes(:posts) }
  scope :with_teams, -> { includes(:teams) }

  # 分页
  scope :recent, -> { order(created_at: :desc) }

  # 归档相关的 scope
  scope :archived, -> { where(archived: true) }
  scope :not_archived, -> { where(archived: false) }

  # 搜索相关的 scope
  scope :search_by_name_or_description, ->(query) {
    return all if query.blank?

    where(
      arel_table[:name].matches("%#{sanitize_sql_like(query)}%").or(
        arel_table[:description].matches("%#{sanitize_sql_like(query)}%")
      )
    )
  }

  scope :filter_by_visibility, ->(visibility) {
    return if visibility.blank?
    where(visibility: visibilities[visibility])
  }

  # 添加团队资源访问相关的便捷方法
  def viewers
    team_members.merge(TeamResourceAccess.where(role: :viewer))
  end

  def editors
    team_members.merge(TeamResourceAccess.where(role: :editor))
  end

  def maintainers
    team_members.merge(TeamResourceAccess.where(role: :maintainer))
  end

  # 清理相关联的缓存
  def clear_associated_caches(base_key)
    # 使用单一缓存键清理所有关联缓存
    Rails.cache.delete(base_key + [ "associations" ])
  end

  # 事务处理
  def transfer_ownership(new_owner)
    transaction do
      update!(owner: new_owner)
    rescue ActiveRecord::RecordInvalid => e
      logger.error "Ownership transfer failed: #{e.message}"
      raise  # 重新抛出异常确保事务回滚
    end
  end

  # 批量操作
  def self.bulk_archive(ids)
    transaction do
      where(id: ids).update_all(archived: true)
    end
  end

  def self.bulk_update_visibility(ids, visibility)
    transaction do
      where(id: ids).update_all(visibility: visibility)
    end
  end

  private

  def clear_associated_caches(base_key = nil)
    # 检查触发条件
    return unless previous_changes.keys.any? { |attr| %w[visibility archived deleted_at].include?(attr) }

    base_key ||= [ cache_key_with_version ]

    # 使用单一缓存键清理所有关联缓存
    Rails.cache.delete(base_key + [ "associations" ])
  end

  ## 验证方法

  def ensure_owner_presence
    errors.add(:owner, "must be present") unless owner.present?
  end

  def validate_name_uniqueness_per_owner
    return unless name_changed? || owner_id_changed?

    scope = KnowledgeBase.where(name: name, owner_id: owner_id)
    scope = scope.where.not(id: id) if persisted?

    errors.add(:name, :taken) if scope.exists?
  end

  # Validation on create
  def validate_teams_presence_if_team_visibility
    if team_visibility? && team_resource_accesses.empty?
      # Note: This check might be tricky on create as accesses are often added after save.
      # Consider if this validation is truly needed or handled by controller logic.
      # If kept, ensure controller adds accesses *before* save or handle potential errors.
      # For now, let's assume controller handles it correctly on create.
      # errors.add(:team_resource_accesses, "must have at least one team access on create if team visibility")
    end
  end

  # Validation on update: only run if visibility IS team and DID NOT change in this update.
  def validate_teams_presence_if_still_team_visibility
    # Only apply if visibility is currently team and it hasn't changed during this update cycle
    if team_visibility? && !visibility_changed? && team_resource_accesses.empty?
       errors.add(:team_resource_accesses, "must have at least one team access if visibility remains team")
    end
  end

  # Validation on update: check presence if visibility *changes to* team
  def validate_teams_presence_when_becoming_team_visible
    # 检查可见性是否刚刚变为 team_visibility 并且没有关联的团队访问记录
    if visibility_changed? && team_visibility? && team_resource_accesses.empty?
      # 添加错误信息到 :team_resource_accesses 属性
      errors.add(:team_resource_accesses, "must have at least one team access when changing visibility to team")
    end
  end

  def validate_post_limits
    if posts.count >= MAX_POSTS_LIMIT
      errors.add(:base, "Maximum number of posts reached")
    end
  end

  def team_associations_only_for_team_visibility
    if !team_visibility? && team_resource_accesses.any?
      errors.add(:team_resource_accesses, "can only be associated with team visibility knowledge bases")
    end
  end

  def cleanup_team_resource_accesses
    ActiveRecord::Base.transaction do
      # 先删除所有相关的 team_resource_accesses
      team_resource_accesses.delete_all
      # 删除所有相关的 knowledge_base_posts
      knowledge_base_posts.delete_all
    end
  end

  # TODO
  # def check_dependencies
  #   # 检查是否有依赖关系
  #   throw :abort if knowledge_base_posts.exists?
  # end
  #
  # def notify_creation
  #   # 创建通知
  #   Notification.create!(
  #     recipient: user,
  #     action: "created",
  #     notifiable: self
  #   )
  # end

  # def track_changes
  #   # 记录修改历史
  #   versions.create!(
  #     event: "update",
  #     [[2025_01_22]]: Current.user&.id,
  #     object_changes: saved_changes
  #   )
  # end
end
