<%
  # 根据当前视图确定页面标题
  current_view_param = params[:view] || 'grid'
  page_title = case current_view_param
               when 'map'
                 t('events.index.map_title')
               when 'calendar'
                 t('events.calendar.title')
               else
                 t('events.index.grid_title')
               end

  # 面包屑路径 - Home 由组件自动添加，我们添加 Events 层级和具体视图
  breadcrumb_items = [
    [t('events.index.title'), events_path],
    [page_title, nil]
  ]
%>
<% content_for :title, page_title %>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8"
       data-controller="view-switcher"
       data-view-switcher-default-view-value="grid">
    <!-- 添加面包屑导航 -->
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { items: breadcrumb_items } %>

        <!-- 页面标题和操作按钮 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white"><%= page_title %></h1>
          </div>
          <div class="mt-4 md:mt-0 flex items-center space-x-3">
            <%= render 'shared/events_view_switcher',
                current_view: (params[:view].blank? || params[:view] == 'grid') ? 'grid' : params[:view],
                params_except: [:view] %>

            <% if authenticated? && allowed_to?(:create?, Event) %>
              <%= link_to new_event_path,
                  class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" do %>
                <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 1v16M1 9h16"/>
                </svg>
                <%= t('events.new.title') %>
              <% end %>
            <% end %>
          </div>
        </div>

        <!-- Flowbite Tabs -->
        <div class="mb-6">
          <div class="border-b border-gray-200 dark:border-gray-700">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="eventTabs" data-tabs-toggle="#eventTabContent" role="tablist">
              <li class="mr-2" role="presentation">
                <%= link_to events_path(tab: 'upcoming', **params.except(:tab, :page).permit!),
                    class: "inline-block p-4 border-b-2 rounded-t-lg #{'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500' if @current_tab == 'upcoming'} #{'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' unless @current_tab == 'upcoming'} transition-colors duration-200",
                    role: "tab" do %>
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <%= t('events.index.tabs.upcoming') %>
                <% end %>
              </li>
              <li class="mr-2" role="presentation">
                <%= link_to events_path(tab: 'past', **params.except(:tab, :page).permit!),
                    class: "inline-block p-4 border-b-2 rounded-t-lg #{'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500' if @current_tab == 'past'} #{'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' unless @current_tab == 'past'} transition-colors duration-200",
                    role: "tab" do %>
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <%= t('events.index.tabs.past') %>
                <% end %>
              </li>
              <li role="presentation">
                <%= link_to events_path(tab: 'all', **params.except(:tab, :page).permit!),
                    class: "inline-block p-4 border-b-2 rounded-t-lg #{'text-primary-600 border-primary-600 dark:text-primary-500 dark:border-primary-500' if @current_tab == 'all'} #{'border-transparent hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300' unless @current_tab == 'all'} transition-colors duration-200",
                    role: "tab" do %>
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                  </svg>
                  <%= t('events.index.tabs.all') %>
                <% end %>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>


    <!-- 搜索和筛选栏 -->
    <div class="mb-4">
      <div class="p-2 bg-white border border-gray-200 rounded-lg shadow-xs dark:border-gray-700 dark:bg-gray-800">
        <%= form_with url: events_path, method: :get, local: true, class: "mobile-search-form", data: { controller: "search-suggestions" } do |f| %>
          <%= f.hidden_field :tab, value: @current_tab %>

          <div class="flex flex-wrap items-center gap-2">
            <!-- 搜索框 -->
            <div class="relative flex-grow" style="min-width: 12rem;">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg>
              </div>
              <%= f.text_field :search,
                  value: params[:search],
                  placeholder: t('events.search.placeholder'),
                  "aria-label": "Search events",
                  class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white",
                  data: { search_suggestions_url_value: search_suggestions_events_path, action: "input->search-suggestions#search" } %>
              <div data-search-suggestions-target="results" class="absolute z-10 w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg mt-1 hidden"></div>
            </div>

            <!-- 分类筛选 -->
            <div class="flex-grow" style="min-width: 9rem;">
              <%= f.collection_select :category_id, EventCategory.all, :id, :name, { prompt: t('events.search.all_categories'), selected: params[:category_id] }, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white" %>
            </div>

            <!-- 城市筛选 -->
            <div class="flex-grow" style="min-width: 9rem;">
              <%= f.select :city, options_from_collection_for_select(EventLocation.distinct.pluck(:city).compact, :to_s, :to_s, params[:city]), { prompt: t('events.search.all_cities') }, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white" %>
            </div>

            <!-- 时间范围筛选 -->
            <div class="flex-grow" style="min-width: 9rem;">
              <%= f.select :date_range, options_for_select([[t('common.time_ranges.anytime'), ''], [t('common.time_ranges.today'), 'today'], [t('common.time_ranges.this_week'), 'this_week'], [t('common.time_ranges.this_month'), 'this_month'], [t('common.time_ranges.next_month'), 'next_month']], params[:date_range]), {}, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white" %>
            </div>

            <!-- 价格范围筛选 -->
            <div class="flex-grow" style="min-width: 9rem;">
              <%= f.select :price_range, options_for_select([[t('common.price_ranges.any_price'), ''], [t('events.search.free'), 'free'], [t('events.search.under_50'), 'under_50'], [t('events.search.50_to_100'), '50_to_100'], [t('events.search.over_100'), 'over_100']], params[:price_range]), {}, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white" %>
            </div>

            <!-- 难度级别筛选 -->
            <div class="flex-grow" style="min-width: 9rem;">
              <%= f.select :difficulty_level, options_for_select([[t('common.difficulty_levels.all_levels'), ''], [t('common.difficulty_levels.beginner'), 'beginner'], [t('common.difficulty_levels.intermediate'), 'intermediate'], [t('common.difficulty_levels.advanced'), 'advanced']], params[:difficulty_level]), {}, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white" %>
            </div>

            <!-- 排序选项 -->
            <div class="flex-grow" style="min-width: 9rem;">
              <%= f.select :sort_by, options_for_select([[t('events.search.sort.date_asc'), 'date_asc'], [t('events.search.sort.date_desc'), 'date_desc'], [t('events.search.sort.price_asc'), 'price_asc'], [t('events.search.sort.price_desc'), 'price_desc'], [t('events.search.sort.name_asc'), 'name_asc'], [t('events.search.sort.popularity'), 'popularity']], params[:sort_by] || 'date_asc'), {}, class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full p-2 dark:bg-gray-700 dark:border-gray-600 dark:text-white" %>
            </div>

            <!-- 搜索按钮 -->
            <div class="flex-grow sm:flex-grow-0">
              <%= f.submit t('events.search.filters'), class: "w-full text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2 text-center dark:bg-primary-600 dark:hover:bg-primary-700" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>


    <!-- 加载状态 -->
    <div id="loading" class="text-center p-8 hidden">
      <div role="status" class="flex justify-center items-center">
        <svg aria-hidden="true" class="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-primary-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
            <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0492C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5424 39.6781 93.9676 39.0409Z" fill="currentFill"/>
        </svg>
        <span class="sr-only">Loading...</span>
      </div>
    </div>

    <!-- 视图内容区域 -->
    <%= render "events/grid_view", events: @events, current_tab: @current_tab %>

    <%= render "events/map_view", current_tab: @current_tab %>

    <!-- 日历视图 - 总是渲染，但默认隐藏，由 JavaScript 控制显示 -->
    <%= render "events/calendar_view",
        events_by_date: @events_by_date || {},
        date: @date || Date.current,
        start_date: @start_date || Date.current.beginning_of_month.beginning_of_week(:sunday),
        end_date: @end_date || Date.current.end_of_month.end_of_week(:sunday) %>

    <!-- "加载更多" 按钮 或 Kaminari 分页 - 只在 grid 视图中显示 -->
    <% if @current_view == 'grid' %>
      <div id="pagination-container" class="mt-8 flex justify-center">
        <% if @events.next_page %>
          <%= link_to t('common.load_more'),
                url_for(params.to_unsafe_h.merge(page: @events.next_page)),
                class: "load-more-button text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:outline-none focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800",
                data: { controller: 'infinite-scroll',
                        'infinite-scroll-url-value': url_for(params.to_unsafe_h.merge(page: @events.next_page, format: :turbo_stream)),
                        action: "click->infinite-scroll#loadMore"
                      } %>
        <% end %>
      </div>
    <% end %>

  </div>
</div>
