# 投稿モジュールの日本語翻訳
ja:
  posts:
    form:
      error: "フォームに%{count}件のエラーがあります:"
      title_label: "タイトル"
      title_placeholder: "投稿タイトルを入力"
      content_label: "内容"
      content_placeholder: "ここに投稿内容を記述..."
      save: "投稿を保存"
      cancel: "キャンセル"
    new:
      title: "新規投稿作成"
      subtitle: "新しい投稿を作成し、アイデアや知見を共有しましょう。"
    edit:
      title: "投稿編集"
      subtitle: "ここで投稿を修正します。"
    index:
      title: "私の投稿"
      subtitle: "すべての投稿を管理"
      write_new: "新規投稿作成"
      empty_state: "まだ投稿がありません"
    show:
      title: "投稿"
      status:
        published: "公開済み"
        draft: "下書き"
      metadata:
        author: "著者:"
        published_at: "公開日:"
    actions:
      create: "投稿作成"
      edit: "投稿編集"
      delete: "投稿削除"
      share: "投稿共有"
      publish: "公開"
      unpublish: "非公開にする"
      view: "表示"
      delete_confirm: "この投稿を削除してもよろしいですか？"
    fields:
      title: "タイトル"
      content: "内容"
      tags: "タグ"
      category: "カテゴリ"
      visibility: "公開範囲"
    editor:
      save_draft: "下書き保存"
      publish: "公開"
      preview: "プレビュー"
      formatting: "書式設定"
