# 静态页面中文翻译
zh:
  static_pages:
    about:
      title: "关于"
      subtitle: "了解程序媛汇的使命和发展历程"
      organization_intro: "组织介绍"
      organization_intro_text: "程序媛汇(CGC)成立于2016年6月1日，是一家致力于通过数字技术赋能女性的社会企业。CGC通过线上线下相结合的方式，包括编程教育、入门工作坊、职业培训和高级开发团队赋能，鼓励和帮助更多女性体验编程并进入STEM领域。"
      organization_honors: "组织荣誉"
      honors:
        undp: '2018年入选联合国开发计划署发起的"科技与慈善"项目案例集'
        youth_program: '2019年成为共青团中央"全国青年社会组织"伙伴计划"获奖项目'
        media_coverage: "获得中国日报、环球时报、央视等媒体报道"
      mission: "我们的使命"
      team: "我们的团队"
      contact: "联系我们"
      email: "<EMAIL>"
      website: "codingirlsclub.com/about"
    terms:
      title: "服务条款"
      subtitle: "了解我们的服务规则和使用条款"
      service_description: "服务描述"
      service_description_text: "程序媛汇提供的服务包括但不限于:"
      services:
        workshop: "编程教育工作坊"
        vocational: "职业培训课程"
        advanced: "高级技能培训"
        platform: "在线学习平台"
      user_responsibilities: "用户责任"
      user_responsibilities_text: "使用我们的服务时，用户需要:"
      responsibilities:
        info: "提供真实准确的个人信息"
        ip: "遵守知识产权规定"
        rules: "遵守课程规则和行为准则"
        security: "保护账户安全"
      payment_refund: "支付与退款"
      payment_refund_text: "关于支付和退款的规定:"
      payment_rules:
        advance: "课程费用需提前支付"
        refund_before: "课程开始前7天可申请全额退款"
        no_refund: "课程开始后不予退款"
        special: "特殊情况可与客服协商"
      intellectual_property: "知识产权"
      intellectual_property_text: "关于知识产权的规定:"
      ip_rules:
        copyright: "课程内容版权归程序媛汇所有"
        distribution: "未经许可不得分发课程内容"
        student_works: "学生作品知识产权归学生所有"
        collaborative: "合作项目知识产权需另行约定"
      last_updated: "最后更新"
    privacy:
      title: "隐私政策"
      subtitle: "了解我们如何保护您的个人信息"
      information_collection: "信息收集"
      information_collection_text: "我们收集的信息包括但不限于:"
      collected_info:
        basic: "基本信息:姓名、邮箱地址、电话号码等"
        learning: "学习记录:课程参与、完成进度等"
        usage: "使用数据:访问日志、设备信息等"
      information_usage: "信息使用"
      information_usage_text: "我们收集的信息将用于:"
      usage_purposes:
        improve: "提供和改进我们的服务"
        personalize: "个性化学习体验"
        notifications: "发送课程通知和更新"
        payments: "处理支付和退款"
      information_protection: "信息保护"
      information_protection_text: "我们采取多种措施保护您的个人信息:"
      protection_measures:
        encryption: "数据加密存储"
        access: "访问控制"
        audits: "定期安全审计"
        training: "员工保密培训"
      cookie_usage: "Cookie使用"
      cookie_usage_text: "我们使用cookie来改善用户体验，包括:"
      cookie_purposes:
        login: "保持登录状态"
        preferences: "记住用户偏好"
        analytics: "分析网站使用情况"
      last_updated: "最后更新"
    home:
      title: "首页"
      headline: "程序媛汇"
      tagline: "CODING GIRLS CLUB"
      subtitle: "科技领域赋能女孩和女性"
      description: "程序媛汇(CGC)成立于2016年6月1日，是一家致力于通过数字技术赋能女性的社会企业。CGC通过线上线下相结合的方式，包括编程教育、入门工作坊、职业培训和高级开发团队赋能，鼓励和帮助更多女性体验编程并进入STEM领域。"
      start_learning: "开始学习"
      learn_more: "了解更多"
      mission_vision: "使命与愿景"
      mission_vision_subtitle: "我们致力于通过数字技术赋能女性，创造更多可能性。"
      mission: "使命"
      mission_points:
        step: "鼓励女性迈出第一步"
        possibilities: "开启人生可能性"
        economy: "赋能数字经济"
      vision: "愿景"
      vision_points:
        power: '注入更多"她力量"'
        empowerment: "女性数字赋能"
        diversity: "通过多样性改变世界"
      core_products: "核心产品"
      core_products_subtitle: "我们提供多层次编程学习路径，满足不同阶段学习需求。"
      products:
        coding_day: "编程女孩日"
        coding_day_subtitle: "一日编程"
        coding_day_desc: "教育工作坊:面向想体验编程的初学者，提供半天至一周的编程培训。"
        vocational: '"400+300"职业培训'
        vocational_desc: "面向想通过编程技能转型的初学者，提供400+300小时的职业培训。"
        development: "软件开发赋能"
        development_desc: "面向有行业经验的程序员想进一步提高编程技能，提供以敏捷开发为核心的端到端赋能课程体系。"
      achievements: "项目成果"
      achievements_subtitle: "我们的影响力不断扩大，帮助更多女性实现数字化转型。"
      stats:
        cities: "个城市"
        universities: "所高校"
        students: "名学员"
        coaches: "位教练"
      get_started: "立即开始"
