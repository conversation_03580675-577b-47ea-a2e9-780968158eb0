<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { items: [[t('users.settings.title'), nil]] } %>

        <!-- 页面标题和描述 -->
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white"><%= t('.title', default: 'User Settings') %></h1>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= t('.subtitle', default: 'Manage your account settings and preferences.') %></p>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
    <%# Left Column %>
    <div class="xl:col-span-2 space-y-6">
      <%# Connected Accounts Card (New) %>
      <div class="bg-white dark:bg-gray-800 rounded-sm shadow-sm p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white"><%= t('users.settings.connected_accounts') %></h2>
        <div class="space-y-4">
          <%# List of providers to manage %>
          <% providers = [:github, :wechat] %>
          <% providers.each do |provider| %>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3"> <%# Added container for icon + text %>
                <%# Add SVG Icon based on provider %>
                <% if provider == :github %>
                  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M12.006 2a9.847 9.847 0 0 0-6.484 2.44 10.32 10.32 0 0 0-3.393 6.17 10.48 10.48 0 0 0 1.317 6.955 10.045 10.045 0 0 0 5.4 4.418c.504.095.683-.223.683-.494 0-.245-.01-1.052-.014-1.908-2.78.62-3.366-1.21-3.366-1.21a2.711 2.711 0 0 0-1.11-1.5c-.907-.637.07-.621.07-.621.317.044.62.163.885.346.266.183.487.426.647.71.135.253.318.476.538.655a2.079 2.079 0 0 0 2.37.196c.045-.52.27-1.006.635-1.37-2.219-.259-4.554-1.138-4.554-5.07a4.022 4.022 0 0 1 1.031-2.75 3.77 3.77 0 0 1 .096-2.713s.839-.275 2.749 1.05a9.26 9.26 0 0 1 5.004 0c1.906-1.325 2.74-1.05 2.74-1.05.37.858.406 1.828.101 2.713a4.017 4.017 0 0 1 1.029 2.75c0 3.939-2.339 4.805-4.564 5.058a2.471 2.471 0 0 1 .679 1.897c0 1.372-.012 2.477-.012 2.814 0 .272.18.592.687.492a10.05 10.05 0 0 0 5.388-4.421 10.473 10.473 0 0 0 1.313-6.948 10.32 10.32 0 0 0-3.39-6.165A9.847 9.847 0 0 0 12.007 2Z" clip-rule="evenodd"/>
                  </svg>
                <% elsif provider == :wechat %>
                  <svg class="w-6 h-6 text-green-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" height="24" fill="currentColor" viewBox="0 0 576 512">
                    <path fill="currentColor" d="M385.2 167.6c6.4 0 12.6.3 18.8 1.1C387.4 90.3 303.3 32 207.7 32 100.5 32 13 104.8 13 197.4c0 53.4 29.3 97.5 77.9 131.6l-19.3 58.6 68-34.1c24.4 4.8 43.8 9.7 68.2 9.7 6.2 0 12.1-.3 18.3-.8-4-12.9-6.2-26.6-6.2-40.8-.1-84.9 72.9-154 165.3-154zm-104.5-52.9c14.5 0 24.2 9.7 24.2 24.4 0 14.5-9.7 24.2-24.2 24.2-14.8 0-29.3-9.7-29.3-24.2.1-14.7 14.6-24.4 29.3-24.4zm-136.4 48.6c-14.5 0-29.3-9.7-29.3-24.2 0-14.8 14.8-24.4 29.3-24.4 14.8 0 24.4 9.7 24.4 24.4 0 14.6-9.6 24.2-24.4 24.2zM563 319.4c0-77.9-77.9-141.3-165.4-141.3-92.7 0-165.4 63.4-165.4 141.3S305 460.7 397.6 460.7c19.3 0 38.9-5.1 58.6-9.9l53.4 29.3-14.8-48.6C534 402.1 563 363.2 563 319.4zm-219.1-24.5c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.8 0 24.4 9.7 24.4 19.3 0 10-9.7 19.6-24.4 19.6zm107.1 0c-9.7 0-19.3-9.7-19.3-19.6 0-9.7 9.7-19.3 19.3-19.3 14.5 0 24.4 9.7 24.4 19.3.1 10-9.9 19.6-24.4 19.6z"/>
                  </svg>
                <% end %>
                <span class="text-sm font-medium text-gray-900 dark:text-white"><%= provider.to_s.humanize %></span>
              </div>
              <% if current_user.has_oauth_provider?(provider) %>
                <%# Disconnect Button %>
                <%= form_with url: unbind_oauth_path(provider: provider), method: :delete, data: { turbo_confirm: t('users.settings.disconnect_confirm', provider: provider.to_s.humanize) } do %>
                  <% can_unbind = current_user.can_unbind_oauth?(provider) %>
                  <%= button_tag t('users.settings.disconnect'),
                      type: 'submit',
                      disabled: !can_unbind,
                      class: "text-red-600 dark:text-red-500 hover:text-red-800 dark:hover:text-red-400 text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed",
                      title: (can_unbind ? nil : t('users.settings.cannot_disconnect')) %>
                <% end %>
              <% else %>
                <%# Connect Button %>
                <%= form_tag "/auth/#{provider}", method: :post, data: { turbo: false } do %>
                  <%= button_tag t('users.settings.connect'),
                      type: 'submit',
                      class: "text-primary-600 dark:text-primary-500 hover:text-primary-800 dark:hover:text-primary-400 text-sm font-medium" %>
                <% end %>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>

      <%# Preferences Card %>
      <div class="bg-white dark:bg-gray-800 rounded-sm shadow-sm p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white"><%= t('.preferences') %></h2>
        <%= form_with scope: :preferences, url: update_preferences_users_path, method: :patch, class: "space-y-4" do |f| %>
          <%# Language Selection %>
          <div>
            <%= f.label :language, t('users.settings.language'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.select :language,
                options_for_select([
                  ['English', 'en'],
                  ['中文', 'zh'],
                  ['Français', 'fr'],
                  ['Deutsch', 'de'],
                  ['Español', 'es'],
                  ['ไทย', 'th'],
                  ['日本語', 'ja'],
                  ['한국어', 'ko'],
                  ['Русский', 'ru'],
                  ['العربية', 'ar'],
                  ['Tiếng Việt', 'vi']
                ], current_user.preferences['language']),
                {},
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# Theme Selection %>
          <div>
            <%= f.label :theme, t('users.settings.theme'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.select :theme,
                options_for_select([
                  [t('users.settings.theme_light'), 'light'],
                  [t('users.settings.theme_dark'), 'dark'],
                  [t('users.settings.theme_system'), 'system']
                ], current_user.preferences['theme']),
                {},
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# Timezone Selection %>
          <div>
            <%= f.label :timezone, t('users.settings.timezone'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.time_zone_select :timezone,
                ActiveSupport::TimeZone.all,
                { default: current_user.preferences['timezone'] },
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# Currency Selection %>
          <div>
            <%= f.label :currency, t('users.settings.currency'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.select :currency,
                options_for_select([
                  [t('users.settings.currency_usd'), 'USD'],
                  [t('users.settings.currency_cny'), 'CNY'],
                  [t('users.settings.currency_eur'), 'EUR']
                ], current_user.preferences['currency']),
                {},
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%= f.submit t('users.settings.save_preferences'),
              class: "text-white bg-primary-600 hover:bg-primary-700 focus:ring-3 focus:ring-primary-300 font-medium rounded-sm text-sm px-5 py-2.5 focus:outline-hidden" %>
        <% end %>
      </div>

      <%# Notifications Card %>
      <div class="bg-white dark:bg-gray-800 rounded-sm shadow-sm p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white"><%= t('users.settings.notifications') %></h2>
        <%= form_with scope: :preferences, url: update_preferences_users_path, method: :patch, class: "space-y-4" do |f| %>
          <%# 全局通知开关 - 顺序: Web, WeChat, Email %>
          <div class="space-y-2">
            <div class="flex items-center">
              <%= check_box_tag 'preferences[notification][web]', '1', current_user.preferences.dig('notification', 'web'), class: "w-4 h-4 text-primary-600 rounded-sm focus:ring-3 focus:ring-primary-500", id: "preferences_notification_web_card" %>
              <%= label_tag 'preferences_notification_web_card', t('users.settings.web_notifications'), class: "ml-2 text-sm text-gray-900 dark:text-white" %>
            </div>
            <div class="flex items-center">
              <%= check_box_tag 'preferences[notification][service_wechat]', '1', current_user.preferences.dig('notification', 'service_wechat'), class: "w-4 h-4 text-primary-600 rounded-sm focus:ring-3 focus:ring-primary-500", id: "preferences_notification_service_wechat_card" %>
              <%= label_tag 'preferences_notification_service_wechat_card', t('users.settings.service_wechat_notifications'), class: "ml-2 text-sm text-gray-900 dark:text-white" %>
            </div>
            <div class="flex items-center" title="<%= t('users.settings.email_notification_disabled_tooltip') %>">
              <%= check_box_tag 'preferences[notification][email]', '1', false, class: "w-4 h-4 text-gray-400 rounded-sm focus:ring-0 cursor-not-allowed", id: "preferences_notification_email_card", disabled: true %>
              <%= label_tag 'preferences_notification_email_card', t('users.settings.email_notifications'), class: "ml-2 text-sm text-gray-500 dark:text-gray-400 cursor-not-allowed" %>
            </div>
          </div>

          <%# 微信通知连接状态提示 %>
          <% if current_user.has_wechat_bound? %>
            <div class="mt-2 text-sm text-green-600 dark:text-green-400">
              <%= t('users.settings.wechat_connected') %>
            </div>

            <%# 按类型细分的微信通知设置 %>
            <div class="mt-4 space-y-4">
              <h3 class="text-sm font-medium text-gray-900 dark:text-white"><%= t('users.settings.notification_types') %></h3>
              
              <div class="space-y-2 pl-4">
                <% %w[ai_message event_registration event_reminder system knowledge_base].each do |type| %>
                  <div class="flex items-center">
                    <%= check_box_tag "preferences[notification][#{type}][service_wechat]",
                      '1',
                      current_user.notification_enabled?(type, :service_wechat),
                      class: "w-4 h-4 text-primary-600 rounded-sm focus:ring-3 focus:ring-primary-500",
                      id: "preferences_notification_#{type}_service_wechat" %>
                    <%= label_tag "preferences_notification_#{type}_service_wechat",
                      t("users.settings.notification_types.#{type}"),
                      class: "ml-2 text-sm text-gray-900 dark:text-white" %>
                  </div>
                <% end %>
              </div>
            </div>
          <% else %>
            <div class="mt-2 text-sm text-yellow-600 dark:text-yellow-400">
              <%= t('users.settings.wechat_not_connected') %>
            </div>
          <% end %>

          <%# --- 精细化通知设置 --- %>
          <div class="pt-4 mt-4 border-t border-gray-200 dark:border-gray-700">
            <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-3"><%= t('users.settings.notification_details') %></h4>
            <div class="space-y-3">
              <%- notification_types = { ai_message: t('notifications.show.types.ai_message'), event_registration: t('notifications.show.types.event_registration'), event_reminder: t('notifications.show.types.event_reminder'), system: t('notifications.show.types.system'), knowledge_base: t('notifications.show.types.knowledge_base') } -%>
              <% channels = [:web, :service_wechat, :email] %>
              <% notification_types.each do |type, name| %>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-2 items-center">
                  <div class="col-span-1">
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= t("notifications.show.types.#{type}", default: name) %></span>
                  </div>
                  <div class="col-span-3 flex items-center space-x-6">
                    <% channels.each do |channel| %>
                      <div class="flex items-center" <%= 'title="' + t('users.settings.email_notification_disabled_tooltip') + '"' if channel == :email %>>
                        <%= check_box_tag "preferences[notification][#{type}][#{channel}]", '1', (channel == :email ? false : current_user.notification_enabled?(type, channel)),
                            class: "w-4 h-4 rounded-sm #{channel == :email ? 'text-gray-400 focus:ring-0 cursor-not-allowed' : 'text-primary-600 focus:ring-3 focus:ring-primary-500'}",
                            id: "preferences_notification_#{type}_#{channel}_card",
                            disabled: (channel == :email) %>
                        <%= label_tag "preferences_notification_#{type}_#{channel}_card", t("users.settings.notification_channels.#{channel}"), class: "ml-2 text-sm #{channel == :email ? 'text-gray-500 dark:text-gray-400 cursor-not-allowed' : 'text-gray-900 dark:text-white'}" %>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
          <%= f.submit t('users.settings.save_preferences'), class: "text-white bg-primary-600 hover:bg-primary-700 focus:ring-3 focus:ring-primary-300 font-medium rounded-sm text-sm px-5 py-2.5 focus:outline-hidden" %>
        <% end %>
      </div>
    </div>

    <%# Right Column %>
    <div class="xl:col-span-1 space-y-6">
      <%# Profile Information Card %>
      <div class="bg-white dark:bg-gray-800 rounded-sm shadow-sm p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white"><%= t('users.profile.personal_info') %></h2>
        <%= form_with model: current_user, url: update_profile_users_path, method: :patch, class: "space-y-4" do |f| %>
          <%# Phone Number %>
          <div>
            <%= f.label :phone_number, t('users.settings.phone_number'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.text_field :phone_number,
                value: current_user.phone_number,
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# Gender %>
          <div>
            <%= f.label :gender, t('users.settings.gender'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.select :gender,
                options_for_select([
                  [t('users.settings.gender_male'), 'male'],
                  [t('users.settings.gender_female'), 'female']
                ], current_user.gender),
                { include_blank: t('users.settings.gender_select') },
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# Location %>
          <div>
            <%= f.label :location, t('users.settings.location'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.text_field :location,
                value: current_user.location,
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# Hobbies %>
          <div>
            <%= f.label :hobbies, t('users.settings.hobbies'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.text_field :hobbies,
                value: current_user.hobbies&.join(', '),
                placeholder: t('users.settings.hobbies_hint'),
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%= f.submit t('users.profile.update_button'),
              class: "text-white bg-primary-600 hover:bg-primary-700 focus:ring-3 focus:ring-primary-300 font-medium rounded-sm text-sm px-5 py-2.5 focus:outline-hidden" %>
        <% end %>
      </div>



      <%# Security Card %>
      <div class="bg-white dark:bg-gray-800 rounded-sm shadow-sm p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-white"><%= t('users.settings.security') %></h2>
        <%= form_with url: update_password_users_path, method: :patch, local: true, class: "space-y-4" do |f| %>
          <%# Current Password %>
          <div>
            <%= f.label :current_password, t('users.settings.current_password'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.password_field :current_password,
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# New Password %>
          <div>
            <%= f.label :password, t('users.settings.new_password'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.password_field :password,
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%# Password Confirmation %>
          <div>
            <%= f.label :password_confirmation, t('users.settings.confirm_password'), class: "block mb-2 text-sm font-medium text-gray-900 dark:text-white" %>
            <%= f.password_field :password_confirmation,
                class: "bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-sm focus:ring-3 focus:ring-primary-500 focus:border-primary-500 block w-full p-2.5" %>
          </div>

          <%= f.submit t('users.settings.change_password'),
              class: "text-white bg-primary-600 hover:bg-primary-700 focus:ring-3 focus:ring-primary-300 font-medium rounded-sm text-sm px-5 py-2.5 focus:outline-hidden" %>
        <% end %>
      </div>
    </div>
    </div>
  </div>
</div>
