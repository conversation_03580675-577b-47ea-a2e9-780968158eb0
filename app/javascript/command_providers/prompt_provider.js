// Prompt 命令提供者
import BaseCommandProvider from "command_providers/base_provider";

export default class PromptProvider extends BaseCommandProvider {
  constructor(controller) {
    super(controller);
    this.prompts = [];
    this.loadPrompts();
    this.currentPrompt = null;
    this.modalId = "prompt-selection-modal";
    this.variableModalId = "prompt-variables-modal";
  }

  async loadPrompts() {
    try {
      const response = await fetch("/ai/prompts");
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      this.prompts = await response.json();
    } catch (error) {
      console.error("Failed to load prompts", error);
      this.prompts = [];
    }
  }

  // 获取命令列表
  getCommands(filter = "") {
    // 如果没有输入任何内容，返回提示命令本身
    if (!filter || filter === "") {
      return [
        {
          name: "prompt",
          description: "插入提示模板",
          icon: "📋", // 可选的图标
        },
      ];
    }

    // 如果已经选择了提示命令，返回所有可用的提示模板
    if (filter === "prompt" || filter === "prompt ") {
      return this.prompts.map((prompt) => ({
        id: `prompt-${prompt.id}`,
        name: prompt.name,
        description: "插入提示模板",
        prompt: prompt,
      }));
    }

    // 如果正在输入提示模板的名称
    if (filter.startsWith("prompt ")) {
      const promptFilter = filter.substring(7).toLowerCase();
      const filteredPrompts = this.prompts
        .filter((prompt) => prompt.name.toLowerCase().includes(promptFilter))
        .map((prompt) => ({
          id: `prompt-${prompt.id}`,
          name: prompt.name,
          description: "插入提示模板",
          prompt: prompt,
        }));
      return filteredPrompts;
    }

    return [];
  }

  // 执行命令
  executeCommand(command, options) {
    if (command.name === "prompt") {
      // 如果选择了主 prompt 命令，显示 prompt 选择模态框
      this.showPromptSelectionModal(options);
      return;
    }

    // 如果选择了具体的 prompt
    if (command.prompt) {
      const { prompt } = command;

      if (!prompt || !prompt.template) {
        console.error("Invalid prompt template");
        this.dispatchResult(false, { message: "无效的提示模板" });
        return;
      }

      this.currentPrompt = prompt;

      // 检查模板中是否有变量
      const variables = this.extractVariables(prompt.template);

      if (variables.length > 0) {
        // 如果有变量，显示变量输入界面
        this.showVariableInputsModal(variables, options);
      } else {
        // 如果没有变量，直接插入模板
        this.insertTemplate(prompt.template, {}, options);
      }
    } else {
      console.error("No prompt found in command", command);
      this.dispatchResult(false, { message: "未找到提示模板" });
    }
  }

  // 显示 Prompt 选择模态框
  showPromptSelectionModal(options) {
    // 移除现有的模态框（如果存在）
    const existingModal = document.getElementById(this.modalId);
    if (existingModal) {
      document.body.removeChild(existingModal);
    }

    // 创建新的模态框
    const modal = document.createElement("div");
    modal.id = this.modalId;
    modal.tabIndex = "-1";
    modal.setAttribute("aria-hidden", "true");
    modal.className =
      "fixed top-0 right-0 left-0 z-50 flex h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden p-4 md:inset-0";

    const modalHTML = `
      <div class="relative max-h-full w-full max-w-2xl">
        <!-- Modal content -->
        <div class="relative rounded-lg bg-white shadow-sm dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between rounded-t border-b p-4 dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
              选择提示模板
            </h3>
            <button type="button" class="close-modal ml-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white" data-modal-hide="${this.modalId}">
              <svg class="h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
              </svg>
              <span class="sr-only">关闭</span>
            </button>
          </div>
          <!-- Modal body -->
          <div class="p-4">
            <div class="mb-4">
              <input type="text" id="prompt-search" class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500" placeholder="搜索提示模板...">
            </div>
            <div id="prompt-list" class="max-h-60 overflow-y-auto">
              ${this.renderPromptList()}
            </div>
          </div>
          <!-- Modal footer -->
          <div class="flex items-center justify-end space-x-2 rounded-b border-t border-gray-200 p-4 dark:border-gray-600">
            <button type="button" class="close-modal rounded-lg border border-gray-200 bg-white px-5 py-2.5 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:outline-hidden focus:ring-4 focus:ring-blue-300 dark:border-gray-500 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white">取消</button>
          </div>
        </div>
      </div>
    `;

    modal.innerHTML = modalHTML;
    document.body.appendChild(modal);

    // 保存当前选项
    this.currentOptions = options;

    // 触发 Flowbite 模态框显示事件
    document.dispatchEvent(new CustomEvent("flowbite.modal.show", { detail: { id: this.modalId } }));

    // 添加事件监听器
    this.setupPromptModalEvents(modal);
  }

  // 渲染 Prompt 列表
  renderPromptList() {
    if (this.prompts.length === 0) {
      return '<div class="text-center p-4 text-gray-500">没有可用的提示模板</div>';
    }

    return this.prompts
      .map((prompt) => {
        return `
        <div class="prompt-item mb-2 cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-600" data-prompt-id="${prompt.id}">
          <div class="font-medium text-gray-900 dark:text-white">${prompt.name}</div>
          <div class="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">${prompt.template}</div>
        </div>
      `;
      })
      .join("");
  }

  // 设置 Prompt 模态框的事件
  setupPromptModalEvents(modal) {
    // 关闭按钮事件
    const closeButtons = modal.querySelectorAll(".close-modal");
    closeButtons.forEach((button) => {
      button.addEventListener("click", () => {
        document.body.removeChild(modal);
        // 当用户关闭模态框时，分发一个空的替换文本结果
        this.dispatchResult(false, { replacementText: "" });
      });
    });

    // Prompt 项点击事件
    const promptItems = modal.querySelectorAll(".prompt-item");
    promptItems.forEach((item) => {
      item.addEventListener("click", () => {
        const promptId = item.getAttribute("data-prompt-id");
        const selectedPrompt = this.prompts.find((p) => p.id.toString() === promptId);

        if (selectedPrompt) {
          document.body.removeChild(modal);

          // 检查模板中是否有变量
          const variables = this.extractVariables(selectedPrompt.template);

          if (variables.length > 0) {
            // 如果有变量，显示变量输入界面
            this.showVariableInputsModal(variables, this.currentOptions, selectedPrompt);
          } else {
            // 如果没有变量，直接插入模板
            this.insertTemplate(selectedPrompt.template, {}, this.currentOptions);
          }
        }
      });
    });

    // 搜索框事件
    const searchInput = modal.querySelector("#prompt-search");
    if (searchInput) {
      searchInput.addEventListener("input", (e) => {
        const searchValue = e.target.value.toLowerCase();
        const filteredPrompts = this.prompts.filter(
          (prompt) =>
            prompt.name.toLowerCase().includes(searchValue) || prompt.template.toLowerCase().includes(searchValue),
        );

        const promptList = modal.querySelector("#prompt-list");
        if (promptList) {
          if (filteredPrompts.length === 0) {
            promptList.innerHTML = '<div class="text-center p-4 text-gray-500">没有找到符合条件的提示模板</div>';
          } else {
            promptList.innerHTML = filteredPrompts
              .map((prompt) => {
                return `
                <div class="prompt-item mb-2 cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-600" data-prompt-id="${prompt.id}">
                  <div class="font-medium text-gray-900 dark:text-white">${prompt.name}</div>
                  <div class="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">${prompt.template}</div>
                </div>
              `;
              })
              .join("");

            // 重新添加点击事件
            const newPromptItems = promptList.querySelectorAll(".prompt-item");
            newPromptItems.forEach((item) => {
              item.addEventListener("click", () => {
                const promptId = item.getAttribute("data-prompt-id");
                const selectedPrompt = this.prompts.find((p) => p.id.toString() === promptId);

                if (selectedPrompt) {
                  document.body.removeChild(modal);

                  // 检查模板中是否有变量
                  const variables = this.extractVariables(selectedPrompt.template);

                  if (variables.length > 0) {
                    // 如果有变量，显示变量输入界面
                    this.showVariableInputsModal(variables, this.currentOptions, selectedPrompt);
                  } else {
                    // 如果没有变量，直接插入模板
                    this.insertTemplate(selectedPrompt.template, {}, this.currentOptions);
                  }
                }
              });
            });
          }
        }
      });

      // 聚焦搜索框
      setTimeout(() => searchInput.focus(), 100);
    }
  }

  // 显示变量输入模态框
  showVariableInputsModal(variables, options, selectedPrompt = null) {
    const prompt = selectedPrompt || this.currentPrompt;
    if (!prompt) return;

    // 移除现有的模态框（如果存在）
    const existingModal = document.getElementById(this.variableModalId);
    if (existingModal) {
      document.body.removeChild(existingModal);
    }

    // 创建新的模态框
    const modal = document.createElement("div");
    modal.id = this.variableModalId;
    modal.tabIndex = "-1";
    modal.setAttribute("aria-hidden", "true");
    modal.className =
      "fixed top-0 right-0 left-0 z-50 flex h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden p-4 md:inset-0";

    const formFields = variables
      .map((variable) => {
        return `
        <div class="mb-4">
          <label for="var-${variable}" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">${variable}</label>
          <input type="text" id="var-${variable}" name="${variable}" class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500" placeholder="请输入${variable}的值">
        </div>
      `;
      })
      .join("");

    const modalHTML = `
      <div class="relative max-h-full w-full max-w-md">
        <!-- Modal content -->
        <div class="relative rounded-lg bg-white shadow-sm dark:bg-gray-700">
          <!-- Modal header -->
          <div class="flex items-center justify-between rounded-t border-b p-4 dark:border-gray-600">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">
              输入变量值
            </h3>
            <button type="button" class="close-var-modal ml-auto inline-flex h-8 w-8 items-center justify-center rounded-lg bg-transparent text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white">
              <svg class="h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
              </svg>
              <span class="sr-only">关闭</span>
            </button>
          </div>
          <!-- Modal body -->
          <form id="variables-form">
            <div class="p-4">
              ${formFields}
            </div>
            <!-- Modal footer -->
            <div class="flex items-center justify-end space-x-2 rounded-b border-t border-gray-200 p-4 dark:border-gray-600">
              <button type="button" class="close-var-modal rounded-lg border border-gray-200 bg-white px-5 py-2.5 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900 focus:z-10 focus:outline-hidden focus:ring-4 focus:ring-blue-300 dark:border-gray-500 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white">取消</button>
              <button type="submit" class="rounded-lg bg-blue-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-blue-800 focus:outline-hidden focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">确认</button>
            </div>
          </form>
        </div>
      </div>
    `;

    modal.innerHTML = modalHTML;
    document.body.appendChild(modal);

    // 保存当前选项和 prompt
    this.currentOptions = options;
    this.currentPrompt = prompt;

    // 触发 Flowbite 模态框显示事件
    document.dispatchEvent(new CustomEvent("flowbite.modal.show", { detail: { id: this.variableModalId } }));

    // 添加事件监听器
    const closeButtons = modal.querySelectorAll(".close-var-modal");
    closeButtons.forEach((button) => {
      button.addEventListener("click", () => {
        document.body.removeChild(modal);
        // 当用户关闭变量输入模态框时，分发一个空的替换文本结果
        this.dispatchResult(false, { replacementText: "" });
      });
    });

    // 表单提交事件
    const form = modal.querySelector("#variables-form");
    if (form) {
      form.addEventListener("submit", (e) => {
        e.preventDefault();

        // 收集变量值
        const variableValues = {};
        variables.forEach((variable) => {
          const input = modal.querySelector(`#var-${variable}`);
          variableValues[variable] = input.value || "";
        });

        // 插入模板
        this.insertTemplate(prompt.template, variableValues, options);

        // 关闭模态框
        document.body.removeChild(modal);
      });
    }

    // 聚焦第一个输入框
    if (variables.length > 0) {
      setTimeout(() => {
        const firstInput = modal.querySelector(`#var-${variables[0]}`);
        if (firstInput) firstInput.focus();
      }, 100);
    }
  }

  // 提取模板中的变量
  extractVariables(template) {
    const regex = /{([^{}]+)}/g;
    const variables = [];
    let match;

    while ((match = regex.exec(template)) !== null) {
      const variable = match[1];
      if (!variables.includes(variable)) {
        variables.push(variable);
      }
    }

    return variables;
  }

  // 插入模板
  insertTemplate(template, variables, options) {
    // 替换模板中的变量
    let replacedTemplate = template;
    Object.keys(variables).forEach((variable) => {
      const regex = new RegExp(`{${variable}}`, "g");
      replacedTemplate = replacedTemplate.replace(regex, variables[variable]);
    });

    // 分发结果事件
    this.dispatchResult(true, {
      replacementText: replacedTemplate,
    });
  }

  // 获取提供者ID
  get id() {
    return "prompt";
  }
}
