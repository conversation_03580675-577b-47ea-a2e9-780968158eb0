require "rails_helper"

RSpec.describe "User Settings", type: :feature do
  let(:user) { create(:user, password: "password123") }
  let(:other_user) { create(:user) }

  before do
    # 设置默认的 OmniAuth 测试模式
    OmniAuth.config.test_mode = true

    # 模拟常见的 OAuth 响应
    OmniAuth.config.mock_auth[:github] = OmniAuth::AuthHash.new({
      provider: "github",
      uid: "123456",
      info: {
        email: "<EMAIL>",
        name: "GitHub User",
        image: "http://github.com/avatar.jpg"
      }
    })

    OmniAuth.config.mock_auth[:wechat] = OmniAuth::AuthHash.new({
      provider: "wechat",
      uid: "wx_123456",
      info: {
        nickname: "WeChat User",
        image: "http://wx.qlogo.cn/avatar.jpg"
      }
    })
  end

  describe "第三方账号管理" do
    context "当用户已登录" do
      before do
        sign_in(user)
        visit settings_users_path
      end

      it "显示所有支持的第三方登录选项" do
        within("div.space-y-4") do
          expect(page).to have_content("Github")
          expect(page).to have_content("Wechat")
        end
      end

      context "绑定第三方账号" do
        it "可以成功绑定 GitHub 账号" do
          within("div.space-y-4") do
            click_button "Connect", match: :first
          end

          expect(page).to have_content("Your Github account has been successfully linked!")

          expect(user.omni_auth_identities.find_by(provider: "github")).to be_present
        end

        it "可以成功绑定微信账号" do
          within("div.space-y-4") do
            find("form[action='/auth/wechat']").find_button("Connect").click
          end

          expect(page).to have_content("Your Wechat account has been successfully linked!")

          expect(user.omni_auth_identities.find_by(provider: "wechat")).to be_present
        end

        it "不能绑定已被其他用户绑定的账号" do
          create(:omni_auth_identity, provider: "github", uid: "123456", user: other_user)

          within("div.space-y-4") do
            click_button "Connect", match: :first
          end

          expect(page).to have_content("This Github account is linked to a different user. Please sign out first!")

          expect(user.reload.omni_auth_identities.where(provider: "github")).to be_empty
        end
      end

      context "解绑第三方账号" do
        before do
          create(:omni_auth_identity, provider: "github", uid: "123456", user: user)
          user.reload
          sign_in(user)
          visit settings_users_path
        end

        it "可以成功解绑已绑定的账号" do
          within("div.space-y-4") do
            click_button "Disconnect", match: :first
          end

          expect(page).to have_content("Successfully disconnected Github account.")
          expect(user.reload.omni_auth_identities.count).to eq(0)
        end

        context "当用户只有一个第三方登录但设置了密码时" do
          before do
            user.omni_auth_identities.where.not(provider: "github").destroy_all
            expect(user.reload.omni_auth_identities.count).to eq(1)
            sign_in(user)
            visit settings_users_path
          end

          it "可以解绑 (因为密码已设置)" do
            within("div.space-y-4") do
              disconnect_button = find_button("Disconnect", match: :first)
              expect(disconnect_button).not_to be_disabled

              click_button "Disconnect", match: :first
            end

            expect(page).to have_content("Successfully disconnected Github account.")
            expect(user.reload.omni_auth_identities.count).to eq(0)
          end
        end
      end
    end

    context "当用户未登录" do
      it "重定向到登录页面" do
        visit settings_users_path
        expect(current_path).to eq(new_session_path)
      end
    end
  end
end
