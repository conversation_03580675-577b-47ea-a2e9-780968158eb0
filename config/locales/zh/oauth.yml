zh:
  auth:
    omniauth:
      linked: "成功关联 %{provider} 账号！"
      already_linked: "您已经关联过此 %{provider} 账号。"
      signed_in: "欢迎回来！已通过 %{provider} 成功登录。"
      signed_up_and_in: "欢迎！您的帐户已创建并成功关联 %{provider}。"
      failure: "%{provider} 认证失败，请重试。"
      mismatch: "此 %{provider} 帐户已关联到其他用户。"
      creation_failed: "无法从 %{provider} 信息创建帐户。"
      already_linked_other_user: "此 %{provider} 帐户已关联到其他用户。"
