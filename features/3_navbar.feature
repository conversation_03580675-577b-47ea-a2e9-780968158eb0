Feature: Navigation Bar
  As a user
  I want to navigate through the website using the navigation bar
  So that I can easily access different parts of the website

  Scenario: Clicking logo redirects to homepage
    Given I visit any page
    When I click the logo
    Then I should be redirected to the homepage

  Scenario: Clicking home link redirects to homepage
    Given I visit any page
    When I click the "Home" link
    Then I should be redirected to the homepage

  Scenario: Clicking about link redirects to about page
    Given I visit any page
    When I click the "About" link
    Then I should be redirected to the about page

  Scenario: Clicking login button redirects to login page
    Given I visit any page
    When I click the "Login" button
    Then I should be redirected to the login page

  Scenario: Clicking sign up button redirects to registration page
    Given I visit any page
    When I click the "Sign Up" button
    Then I should be redirected to the registration page
