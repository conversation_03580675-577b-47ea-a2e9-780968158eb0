@import 'tailwindcss';

/* 引入样式依赖 */
@import '../stylesheets/typography.min.css';
@import '../stylesheets/actiontext.css';
@import '../stylesheets/prism.css';
@import '../stylesheets/map.css';
@import '../stylesheets/leaflet.css';
@import '../stylesheets/MarkerCluster.css';
@import '../stylesheets/MarkerCluster.Default.css';
@import '../stylesheets/admin.css';

/* 先引入 intl-tel-input 的基础 CSS，再引入自定义样式覆盖，确保优先级正确 */
@import '../stylesheets/intlTelInput.min.css';

/* 自定义 intl-tel-input 的样式 */
@layer utilities {
  .iti {
    @apply w-full;
  }

  .iti__flag-box {
    @apply flex items-center justify-center;
  }

  .iti__selected-flag {
    @apply flex items-center justify-center;
  }

  .iti__selected-flag__icon {
    @apply w-4 h-4;
  }
}

/* 这里是 intl-tel-input 的 flags 图片路径 (从 public/ 提供) */

:root {
  --iti-path-flags-1x: url("/flags.webp");
  --iti-path-flags-2x: url("/<EMAIL>");
}

@config '../../../config/tailwind.config.js';

/* 添加一些自定义样式来确保格式类正常工作 */
.format {
  line-height: 1.75;
  max-width: 100%;
  /* 覆盖typography.min.css中的65ch限制 */
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {

  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}
