# SQLite. Versions 3.8.0 and up are supported.
#   gem install sqlite3
#
#   Ensure the SQLite 3 gem is defined in your Gemfile
#   gem "sqlite3"
#
default: &default
  adapter: sqlite3
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>
  timeout: 5000

development:
  primary:
    <<: *default
    database: storage/ai_pro_cgc_development.sqlite3
  cache:
    <<: *default
    database: storage/solid/ai_pro_cgc_development_cache.sqlite3
    migrations_paths: db/cache_migrate
  queue:
    <<: *default
    database: storage/solid/ai_pro_cgc_development_queue.sqlite3
    migrations_paths: db/queue_migrate
  cable:
    <<: *default
    database: storage/solid/ai_pro_cgc_development_cable.sqlite3
    migrations_paths: db/cable_migrate

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  primary:
    <<: *default
    database: storage/ai_pro_cgc_test.sqlite3
  cache:
    <<: *default
    database: storage/solid/ai_pro_cgc_test_cache.sqlite3
    migrations_paths: db/cache_migrate
  queue:
    <<: *default
    database: storage/solid/ai_pro_cgc_test_queue.sqlite3
    migrations_paths: db/queue_migrate
  cable:
    <<: *default
    database: storage/solid/ai_pro_cgc_test_cable.sqlite3
    migrations_paths: db/cable_migrate

# Store production database in the storage/ directory, which by default
# is mounted as a persistent Docker volume in config/deploy.yml.
production:
  primary:
    <<: *default
    database: storage/ai_pro_cgc_production.sqlite3
  cache:
    <<: *default
    database: storage/solid/ai_pro_cgc_production_cache.sqlite3
    migrations_paths: db/cache_migrate
  queue:
    <<: *default
    database: storage/solid/ai_pro_cgc_production_queue.sqlite3
    migrations_paths: db/queue_migrate
  cable:
    <<: *default
    database: storage/solid/ai_pro_cgc_production_cable.sqlite3
    migrations_paths: db/cable_migrate
