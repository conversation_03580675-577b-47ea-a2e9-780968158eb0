FactoryBot.define do
  factory :event_instructor do
    association :event
    association :user
    role { "instructor" }
    bio { "Experienced instructor with 5+ years in the field" }
    title { "Senior Developer" }

    trait :instructor do
      role { "instructor" }
      title { "Lead Instructor" }
      bio { "Primary instructor with extensive teaching experience" }
    end

    trait :co_instructor do
      role { "co_instructor" }
      title { "Co-Instructor" }
      bio { "Supporting instructor helping with hands-on activities" }
    end

    trait :assistant do
      role { "assistant" }
      title { "Teaching Assistant" }
      bio { "Graduate student assisting with the workshop" }
    end

    trait :guest_speaker do
      role { "guest_speaker" }
      title { "Industry Expert" }
      bio { "Guest speaker sharing industry insights" }
    end

    trait :without_title do
      title { nil }
    end

    trait :without_bio do
      bio { nil }
    end
  end
end
