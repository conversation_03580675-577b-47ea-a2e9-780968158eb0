class TeamMember < ApplicationRecord
  # 关联关系
  belongs_to :team
  belongs_to :user
  has_many :team_resource_accesses, dependent: :destroy

  # 通过 team_resource_accesses 关联到不同类型的资源
  has_many :knowledge_base_accesses, -> {
    where(resource_kind: TeamResourceAccess::RESOURCE_KINDS[:knowledge_base][:enum])
  }, class_name: "TeamResourceAccess", dependent: :destroy

  has_many :knowledge_bases, through: :knowledge_base_accesses,
           source: :resource,
           source_type: "KnowledgeBase"

  # 枚举定义
  enum :role, {
    member: 0,
    admin: 1
  }, prefix: :team,
     suffix: :role,
     default: :member,
     validate: true

  # 验证规则
  validates :user_id, uniqueness: { scope: :team_id }

  # 资源访问相关方法（使用枚举查询方法）
  def knowledge_base_viewer?(knowledge_base)
    knowledge_base_accesses.exists?(resource: knowledge_base, role: :viewer)
  end

  def knowledge_base_editor?(knowledge_base)
    knowledge_base_accesses.exists?(resource: knowledge_base, role: :editor)
  end

  def knowledge_base_maintainer?(knowledge_base)
    knowledge_base_accesses.exists?(resource: knowledge_base, role: :maintainer)
  end

  def grant_knowledge_base_access(knowledge_base, role)
    knowledge_base_accesses.create!(
      resource: knowledge_base,
      resource_kind: TeamResourceAccess::RESOURCE_KINDS[:knowledge_base][:enum],
      role: role
    )
  end

  def revoke_knowledge_base_access(knowledge_base)
    knowledge_base_accesses.find_by(resource: knowledge_base)&.destroy
  end
end
