require 'rails_helper'

RSpec.describe EventInstructor, type: :model do
  let(:user) { create(:user) }
  let(:event) { create(:event) }
  let(:instructor) { build(:event_instructor, user: user, event: event) }

  describe 'associations' do
    it { should belong_to(:event) }
    it { should belong_to(:user) }
  end

  describe 'validations' do
    subject { build(:event_instructor, user: user, event: event) }

    it { should validate_presence_of(:role) }

    describe 'uniqueness validation' do
      it 'prevents duplicate instructor assignments for same user and event' do
        create(:event_instructor, user: user, event: event)
        duplicate_instructor = build(:event_instructor, user: user, event: event)

        expect(duplicate_instructor).not_to be_valid
        expect(duplicate_instructor.errors[:user_id]).to include('is already an instructor for this event')
      end

      it 'allows same user to be instructor for different events' do
        other_event = create(:event)
        create(:event_instructor, user: user, event: event)

        other_instructor = build(:event_instructor, user: user, event: other_event)
        expect(other_instructor).to be_valid
      end

      it 'allows different users to be instructors for same event' do
        other_user = create(:user)
        create(:event_instructor, user: user, event: event)

        other_instructor = build(:event_instructor, user: other_user, event: event)
        expect(other_instructor).to be_valid
      end
    end

    describe 'role validation' do
      it 'is valid with valid role' do
        instructor.role = 'instructor'
        expect(instructor).to be_valid
      end

      it 'is invalid without a role' do
        instructor.role = nil
        expect(instructor).not_to be_valid
        expect(instructor.errors[:role]).to include("can't be blank")
      end
    end
  end

  describe 'enums' do
    it 'defines role enum with correct values' do
      expect(EventInstructor.roles.keys).to match_array([ 'instructor', 'co_instructor', 'assistant', 'guest_speaker' ])
    end

    it 'allows setting role values' do
      instructor.instructor!
      expect(instructor.role).to eq('instructor')

      instructor.co_instructor!
      expect(instructor.role).to eq('co_instructor')

      instructor.assistant!
      expect(instructor.role).to eq('assistant')

      instructor.guest_speaker!
      expect(instructor.role).to eq('guest_speaker')
    end

    it 'provides role query methods' do
      instructor.role = 'instructor'
      expect(instructor.instructor?).to be true
      expect(instructor.co_instructor?).to be false

      instructor.role = 'assistant'
      expect(instructor.assistant?).to be true
      expect(instructor.instructor?).to be false
    end
  end

  describe 'scopes' do
    let!(:primary_instructor) { create(:event_instructor, :instructor, event: event) }
    let!(:co_instructor) { create(:event_instructor, :co_instructor, event: event) }
    let!(:assistant) { create(:event_instructor, :assistant, event: event) }
    let!(:guest_speaker) { create(:event_instructor, :guest_speaker, event: event) }

    describe '.primary_instructors' do
      it 'returns only instructors with primary role' do
        primary_instructors = EventInstructor.primary_instructors
        expect(primary_instructors).to include(primary_instructor)
        expect(primary_instructors).not_to include(co_instructor)
        expect(primary_instructors).not_to include(assistant)
        expect(primary_instructors).not_to include(guest_speaker)
      end
    end

    describe '.by_role' do
      it 'returns instructors with specified role' do
        assistants = EventInstructor.by_role('assistant')
        expect(assistants).to include(assistant)
        expect(assistants).not_to include(primary_instructor)
        expect(assistants).not_to include(co_instructor)

        guest_speakers = EventInstructor.by_role('guest_speaker')
        expect(guest_speakers).to include(guest_speaker)
        expect(guest_speakers).not_to include(assistant)
      end

      it 'returns empty result for non-existent role' do
        expect(EventInstructor.by_role('non_existent_role')).to be_empty
      end
    end
  end

  describe 'instance methods' do
    describe '#display_name' do
      it 'returns user name with title when title is present' do
        instructor.title = 'Senior Developer'
        instructor.user.username = 'john_doe'

        expect(instructor.display_name).to eq('john_doe - Senior Developer')
      end

      it 'returns only user name when title is blank' do
        instructor.title = ''
        instructor.user.username = 'jane_smith'

        expect(instructor.display_name).to eq('jane_smith')
      end

      it 'returns only user name when title is nil' do
        instructor.title = nil
        instructor.user.username = 'bob_johnson'

        expect(instructor.display_name).to eq('bob_johnson')
      end
    end

    describe '#primary_instructor?' do
      it 'returns true when role is instructor' do
        instructor.role = 'instructor'
        expect(instructor.primary_instructor?).to be true
      end

      it 'returns false when role is not instructor' do
        instructor.role = 'co_instructor'
        expect(instructor.primary_instructor?).to be false

        instructor.role = 'assistant'
        expect(instructor.primary_instructor?).to be false

        instructor.role = 'guest_speaker'
        expect(instructor.primary_instructor?).to be false
      end
    end
  end

  describe 'factory validations' do
    it 'creates valid instructor with factory' do
      instructor = build(:event_instructor, user: user, event: event)
      expect(instructor).to be_valid
    end

    it 'creates valid instructor with traits' do
      instructor_role = build(:event_instructor, :instructor, user: user, event: event)
      expect(instructor_role).to be_valid
      expect(instructor_role.role).to eq('instructor')

      co_instructor_role = build(:event_instructor, :co_instructor, user: user, event: event)
      expect(co_instructor_role).to be_valid
      expect(co_instructor_role.role).to eq('co_instructor')

      assistant_role = build(:event_instructor, :assistant, user: user, event: event)
      expect(assistant_role).to be_valid
      expect(assistant_role.role).to eq('assistant')

      guest_speaker_role = build(:event_instructor, :guest_speaker, user: user, event: event)
      expect(guest_speaker_role).to be_valid
      expect(guest_speaker_role.role).to eq('guest_speaker')
    end

    it 'creates valid instructor without title' do
      instructor_without_title = build(:event_instructor, :without_title, user: user, event: event)
      expect(instructor_without_title).to be_valid
      expect(instructor_without_title.title).to be_nil
    end

    it 'creates valid instructor without bio' do
      instructor_without_bio = build(:event_instructor, :without_bio, user: user, event: event)
      expect(instructor_without_bio).to be_valid
      expect(instructor_without_bio.bio).to be_nil
    end
  end

  describe 'edge cases' do
    it 'handles unicode characters in bio and title' do
      instructor.bio = '拥有5年以上的教学经验'
      instructor.title = '高级开发工程师'
      expect(instructor).to be_valid
    end

    it 'handles very long bio text' do
      long_bio = 'A' * 1000
      instructor.bio = long_bio
      expect(instructor).to be_valid
    end

    it 'handles empty strings vs nil for optional fields' do
      instructor.title = ''
      instructor.bio = ''
      expect(instructor).to be_valid
      expect(instructor.display_name).to eq(instructor.user.username)
    end
  end

  describe 'business logic' do
    it 'allows multiple instructors for same event with different roles' do
      primary = create(:event_instructor, :instructor, event: event)
      co_instructor = build(:event_instructor, :co_instructor, event: event)
      assistant = build(:event_instructor, :assistant, event: event)

      expect(co_instructor).to be_valid
      expect(assistant).to be_valid
    end

    it 'allows same user to have different roles in different events' do
      event2 = create(:event)

      instructor1 = create(:event_instructor, :instructor, user: user, event: event)
      instructor2 = build(:event_instructor, :assistant, user: user, event: event2)

      expect(instructor2).to be_valid
    end

    it 'prevents same user from having multiple roles in same event' do
      create(:event_instructor, :instructor, user: user, event: event)
      duplicate = build(:event_instructor, :assistant, user: user, event: event)

      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:user_id]).to include('is already an instructor for this event')
    end
  end
end
