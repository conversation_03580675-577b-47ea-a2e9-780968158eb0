<div id="message_<%= message.id %>" class="flex items-start gap-2.5 mb-4">
  <div class="w-8 h-8 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center">
    <svg class="w-5 h-5 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
    </svg>
  </div>

  <div class="flex flex-col w-full leading-1.5 p-4 border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-e-xl rounded-es-xl"
       data-controller="clipboard">
    <%# Hidden Original Content %>
    <div class="hidden" data-clipboard-content><%= message.content %></div>

    <%# Header: Name, Status, Copy Button and Timestamp %>
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <%# Assistant name removed as per request %>
        <% status_color = case message.status
           when :completed
             "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
           when :processing
             "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
           when :failed
             "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
           else
             "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
           end %>
        <%# Status span moved to footer %>
      </div>
    </div>

    <%# 消息内容区域 %>
    <% if message.content.present? %>
      <div class="format dark:format-invert max-w-none py-2.5">
        <% if markdown?(message.content) %>
          <%= markdown(message.content) %>
        <% else %>
          <%= simple_format(message.content) %>
        <% end %>
      </div>
    <%# 处理中动画 %>
    <% elsif message.status == :processing %>
      <div class="flex items-center py-2.5">
        <div class="animate-pulse flex space-x-2">
          <div class="h-2 w-2 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
          <div class="h-2 w-2 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
          <div class="h-2 w-2 bg-gray-500 dark:bg-gray-400 rounded-full"></div>
        </div>
      </div>
    <% end %>

    <%# Tool Calls %>
    <% if message.tool_calls.present? %>
      <div class="mt-2 space-y-2">
        <div class="text-sm font-semibold text-gray-700 dark:text-gray-300"><%= t('ai.conversations.messages.tool_calls') %></div>
        <% message.tool_calls.each do |tool_call| %>
          <div class="text-xs bg-gray-50 dark:bg-gray-700 rounded-sm p-2">
            <%# Tool Info %>
            <div class="font-medium text-gray-700 dark:text-gray-300">
              <%= tool_call["function"]["name"] %>
            </div>

            <%# Arguments %>
            <pre class="mt-1 text-gray-600 dark:text-gray-400 overflow-x-auto"><%= JSON.pretty_generate(JSON.parse(tool_call["function"]["arguments"])) %></pre>

            <%# Tool Message %>
            <% tool_call_id = tool_call["id"] %>
            <% tool_message = message.messageable.messages.where(role: "tool", tool_call_id: tool_call_id).first %>

            <% if tool_message.present? %>
              <div class="mt-2 border-t border-gray-200 dark:border-gray-600 pt-2">
                <div class="font-medium text-gray-700 dark:text-gray-300">
                  <%= tool_message.tool_usages.first&.status == "success" ? t('ai.conversations.messages.tool_result') : t('ai.conversations.messages.tool_status') %>
                </div>
                <div class="mt-1 text-gray-600 dark:text-gray-400">
                  <%= tool_message.content %>
                </div>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
    <% end %>

    <%# Footer: Copy Button and Timestamp %>
    <div class="flex items-center justify-between mt-1">
      <%# Copy Button %>
      <button data-clipboard-target="trigger" class="text-gray-900 dark:text-gray-400 hover:bg-gray-100 dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700 rounded-lg py-2 px-2.5 inline-flex items-center justify-center bg-white border-gray-200 border h-8">
        <span data-clipboard-target="defaultMessage">
          <span class="inline-flex items-center">
            <svg class="w-3 h-3 me-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 18 20">
              <path d="M16 1h-3.278A1.992 1.992 0 0 0 11 0H7a1.993 1.993 0 0 0-1.722 1H2a2 2 0 0 0-2 2v15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2Zm-3 14H5a1 1 0 0 1 0-2h8a1 1 0 0 1 0 2Zm0-4H5a1 1 0 0 1 0-2h8a1 1 0 1 1 0 2Zm0-5H5a1 1 0 0 1 0-2h2V2h4v2h2a1 1 0 1 1 0 2Z"/>
            </svg>
            <span class="text-xs font-semibold"><%= t('ai.common.copy') %></span>
          </span>
        </span>
        <span data-clipboard-target="successMessage" class="hidden">
          <span class="inline-flex items-center">
            <svg class="w-3 h-3 text-blue-700 dark:text-blue-500 me-1.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 16 12">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5.917 5.724 10.5 15 1.5"/>
            </svg>
            <span class="text-xs font-semibold text-blue-700 dark:text-blue-500"><%= t('ai.common.copied') %></span>
          </span>
        </span>
      </button>

      <%# Status and Timestamp %>
      <div class="flex items-center gap-2"> <%# Use gap for spacing %>
        <% status_color = case message.status
           when :completed
             "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
           when :processing
             "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
           when :failed
             "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
           else
             "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
           end %>
        <span class="<%= status_color %> text-xs font-medium px-2.5 py-0.5 rounded-sm">
          <%= message.status %>
        </span>
        <span class="text-xs font-normal text-gray-500 dark:text-gray-400">
          <%= message.created_at.strftime("%H:%M") %>
        </span>
      </div>
    </div>
  </div>
</div>
