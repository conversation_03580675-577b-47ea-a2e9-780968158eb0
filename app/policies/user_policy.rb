class UserPolicy < ApplicationPolicy
  # 前置检查用户身份认证 - 只用于需要认证的操作
  pre_check :check_authenticated!, only: %i[update_preferences]

  # 定义作用域
  relation_scope do |relation|
    next relation.none unless user
    next relation if user.admin?  # Admin可以看到所有用户
    relation.where(id: user.id)   # 普通用户只能看到自己
  end

  def show?
    admin? || owner?
  end

  def update?
    admin? || owner?
  end

  def update_preferences?
    owner?
  end

  def settings?
    owner?
  end

  def update_profile?
    owner?
  end

  def update_password?
    owner?
  end

  def destroy?
    admin? && !self_user?  # Admin可以删除其他用户，但不能删除自己
  end

  def toggle_admin?
    admin? && !self_user?  # Admin可以设置其他用户为admin，但不能修改自己的admin状态
  end

  alias_method :make_admin?, :toggle_admin?
  alias_method :remove_admin?, :toggle_admin?

  private

  def owner?
    user && record.id == user.id
  end

  def self_user?
    user && record.id == user.id
  end
end
