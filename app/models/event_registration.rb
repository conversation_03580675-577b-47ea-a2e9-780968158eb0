class EventRegistration < ApplicationRecord
  # 关联关系
  belongs_to :user
  belongs_to :event

  # 枚举值定义 - Rails 8 语法
  enum :status, {
    pending: "pending",
    confirmed: "confirmed",
    cancelled: "cancelled",
    attended: "attended",
    no_show: "no_show"
  }

  # 验证规则
  validates :status, presence: true
  validates :amount_paid, numericality: { greater_than_or_equal_to: 0 }
  validates :user_id, uniqueness: {
    scope: :event_id,
    conditions: -> { where.not(status: "cancelled") },
    message: "is already registered for this event"
  }
  validate :event_not_full, on: :create
  validate :event_not_cancelled, on: :create
  validate :event_not_past, on: :create

  # 作用域
  scope :confirmed, -> { where(status: "confirmed") }
  scope :attended, -> { where(status: "attended") }
  scope :for_upcoming_events, -> { joins(:event).where("events.start_time > ?", Time.current) }
  scope :recent, -> { order(registered_at: :desc) }

  # 回调函数
  before_create :set_registered_at
  after_create :update_event_cache
  after_destroy :update_event_cache

  private

  def set_registered_at
    self.registered_at ||= Time.current
  end

  def event_not_full
    return unless event&.full?
    errors.add(:base, "Event is already full")
  end

  def event_not_cancelled
    return unless event&.cancelled?
    errors.add(:base, "Cannot register for cancelled event")
  end

  def event_not_past
    return unless event&.start_time && event.start_time < Time.current
    errors.add(:base, "Cannot register for past event")
  end

  def update_event_cache
    event&.touch # 触发缓存更新
  end
end
