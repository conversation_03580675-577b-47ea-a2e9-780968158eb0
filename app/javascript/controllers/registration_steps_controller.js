import { Controller } from "@hotwired/stimulus";
import intlTelInput from "intl-tel-input";
import intlTelInputUtils from "intl-tel-input-utils";
import { Turbo } from "@hotwired/turbo-rails"; // Import Turbo

export default class extends Controller {
  static targets = ["verificationButton", "form", "phoneInput", "verificationCodeInput"]; // Add verificationCodeInput target

  connect() {
    // 初始化国际区号输入框
    const input = this.element.querySelector("input[name='phone']");
    if (input && !input.classList.contains("iti__initialized")) {
      // Store the iti instance on the controller
      this.iti = intlTelInput(input, {
        initialCountry: "auto",
        strictMode: true,
        loadUtils: () => Promise.resolve({ default: intlTelInputUtils }),
        geoIpLookup: function (callback) {
          fetch("https://ipinfo.io/json?token=5f93710b0455be")
            .then((resp) => resp.json())
            .then((resp) => {
              callback(resp && resp.country ? resp.country : "us");
            })
            .catch(() => {
              callback("us");
            });
        },
        nationalMode: false, // Keep false to get the full number with dial code
        separateDialCode: true,
        countryOrder: ["cn", "us", "va", "th", "jp", "fr", "de", "gb", "it", "es", "kr", "ru"],
        // useFullscreenPopup: true
      });
      input.classList.add("iti__initialized");
    }

    this.countdown = 60;
    // 检查是否存在未完成的倒计时
    const savedEndTime = localStorage.getItem("verificationEndTime");
    if (savedEndTime) {
      const remainingTime = Math.ceil(
        (parseInt(savedEndTime) - Date.now()) / 1000,
      );
      if (remainingTime > 0) {
        this.countdown = remainingTime;
        this.verificationButtonTarget.disabled = true;
        this.startCountdown();
      } else {
        localStorage.removeItem("verificationEndTime");
      }
    }
  }

  async verifyCodeAndProceed(event) {
    event.preventDefault(); // Prevent default button behavior

    const phoneNumber = this.iti ? this.iti.getNumber() : null;
    const verificationCode = this.verificationCodeInputTarget.value;

    if (!phoneNumber) {
      alert("Please enter a valid mobile number");
      return;
    }
    if (!verificationCode) {
      alert("Please enter the verification code");
      return;
    }

    // Disable button temporarily (optional, add target if needed)
    // event.currentTarget.disabled = true;

    try {
      const response = await fetch(this.formTarget.action, { // Use form's action URL
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "text/vnd.turbo-stream.html", // Important: Accept Turbo Stream
          "X-CSRF-Token": document.querySelector("[name='csrf-token']").content,
        },
        body: JSON.stringify({
          phone: phoneNumber,
          verification_code: verificationCode,
          step: "send_verification_code", // Keep the step name
          // Include authenticity_token if needed by backend, though CSRF header is usually sufficient
          // authenticity_token: document.querySelector("[name='csrf-token']").content
        }),
      });

      if (response.ok) {
        // If successful (2xx), read the Turbo Stream response
        const turboStream = await response.text();
        // Render the Turbo Stream content
        Turbo.renderStreamMessage(turboStream);
      } else if (response.status === 422) {
        // If validation fails (422), read the Turbo Stream response
        const turboStream = await response.text();
        // Render the Turbo Stream content (which should include the flash message)
        Turbo.renderStreamMessage(turboStream);
      } else {
        // Handle other errors (e.g., 500)
        throw new Error(`Server error: ${response.status}`);
      }
    } catch (error) {
      console.error("Verification request failed:", error);
      alert(`An error occurred: ${error.message}. Please try again.`);
    } finally {
      // Re-enable button (optional)
      // event.currentTarget.disabled = false;
    }
  }

  sendVerificationCode(event) {
    event.preventDefault();

    // 从当前控制器元素获取表单
    const form = this.element;
    // Get the E.164 formatted number from the iti instance
    const phoneNumber = this.iti ? this.iti.getNumber() : null;

    if (!phoneNumber) {
      // Optionally, check iti.isValidNumber() for better validation
      alert("Please enter a valid mobile number");
      return;
    }

    // 禁用按钮并开始倒计时
    this.verificationButtonTarget.disabled = true;
    this.startCountdown();

    // 保存倒计时结束时间
    const endTime = Date.now() + this.countdown * 1000;
    localStorage.setItem("verificationEndTime", endTime.toString());

    // 发送验证码请求
    fetch("/registration/send_verification_code", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": document.querySelector("[name='csrf-token']").content,
      },
      body: JSON.stringify({
        phone: phoneNumber, // Send the E.164 formatted number
        step: "send_verification_code",
      }),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to send the verification code. Procedure");
        }
        return response.json();
      })
      .then((data) => {
        //console.log("Verification code sent successfully");
      })
      .catch((error) => {
        alert(error.message);
        this.verificationButtonTarget.disabled = false;
        this.verificationButtonTarget.textContent = "Verification Code";
        this.countdown = 60;
        localStorage.removeItem("verificationEndTime");
      });
  }

  startCountdown() {
    if (this.countdown > 0) {
      this.verificationButtonTarget.textContent = `Try again in ${this.countdown} seconds`;
      this.countdown--;
      setTimeout(() => this.startCountdown(), 1000);
    } else {
      this.verificationButtonTarget.disabled = false;
      this.verificationButtonTarget.textContent = "Verification Code";
      this.countdown = 60;
      localStorage.removeItem("verificationEndTime");
    }
  }
}
