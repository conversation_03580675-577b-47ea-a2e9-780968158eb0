FactoryBot.define do
  factory :omni_auth_identity do
    association :user
    sequence(:uid) { |n| "uid_#{n}" }
    provider { %w[github wechat].sample } # Default to a random known provider if no trait is given

    trait :github do
      provider { "github" }
      uid { "github_uid_#{SecureRandom.hex(4)}" }
    end

    trait :wechat do
      provider { "wechat" }
      uid { "wechat_uid_#{SecureRandom.hex(4)}" }
      unionid { "wechat_unionid_#{SecureRandom.hex(4)}" }
    end

    # Assuming the provider name for Google is 'google_oauth2'
    trait :google do
      provider { "google_oauth2" }
      uid { "google_uid_#{SecureRandom.hex(4)}" }
    end
  end
end
