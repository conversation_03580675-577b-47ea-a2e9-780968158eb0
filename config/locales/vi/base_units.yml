# Bản dịch tiếng Việt cho module đơn vị cơ bản
vi:
  base_units:
    index:
      title: "Đơn vị cơ bản"
      subtitle: "Quản lý tất cả các tệp cơ sở kiến thức của bạn"
    show:
      title: "Chi tiết tệp"
      subtitle: "Xem chi tiết tệp"
      sections:
        file_info: "Thông tin tệp"
        file_preview: "Xem trước tệp"
      fields:
        filename: "Tên tệp"
        file_type: "Loại tệp"
        file_size: "<PERSON>ích thước tệp"
        upload_time: "Thời gian tải lên"
      messages:
        preview_not_supported: "Không hỗ trợ xem trước cho loại tệp này. Bạn có thể"
        download_to_view: "tải xuống tệp"
        processing: "Đang xử lý tệp, vui lòng đợi..."
        encoding_error: "<PERSON>ân tích mã hóa tệp thất bại: %{error}"
        loading: "Đang tải..."
      actions:
        return_to_list: "Quay lại danh sách"
    fields:
      name: "Tên"
      category: "Danh mục"
      size: "Kích thước"
      status: "Trạng thái"
    status:
      ready: "Sẵn sàng"
      processing: "Đang xử lý"
    actions:
      view: "Xem"
      download: "Tải xuống"
      view_post: "Xem bài viết"
      convert_to_post: "Chuyển thành bài viết"
    categories:
      length: "Độ dài"
      mass: "Khối lượng"
      time: "Thời gian"
      temperature: "Nhiệt độ"
      currency: "Tiền tệ"
      custom: "Tùy chỉnh"
