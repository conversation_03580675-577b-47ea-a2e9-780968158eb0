class CreateEventRegistrations < ActiveRecord::Migration[8.0]
  def change
    create_table :event_registrations do |t|
      t.references :user, null: false, foreign_key: true
      t.references :event, null: false, foreign_key: true
      t.string :status, default: 'confirmed'
      t.decimal :amount_paid, precision: 8, scale: 2, default: 0
      t.text :notes
      t.datetime :registered_at

      t.timestamps
    end

    add_index :event_registrations, [ :user_id, :event_id ], unique: true
    add_index :event_registrations, :status
    add_index :event_registrations, :registered_at
  end
end
