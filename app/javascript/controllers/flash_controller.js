import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static flashCounter = 0

  connect() {
    // 监听 show-flash 事件
    window.addEventListener('show-flash', this.handleFlash.bind(this))
    
    // 初始化现有的 flash 消息
    this.initializeExistingFlashes()
  }

  disconnect() {
    window.removeEventListener('show-flash', this.handleFlash.bind(this))
  }

  initializeExistingFlashes() {
    const flashContainer = document.getElementById('flash')
    if (flashContainer) {
      const existingFlashes = flashContainer.querySelectorAll('.flash-message')
      existingFlashes.forEach(flash => {
        if (!flash.dataset.flashId) {
          const flashId = ++this.constructor.flashCounter
          flash.dataset.flashId = flashId
          
          // 为每个 flash 设置自动移除
          setTimeout(() => {
            const alert = flashContainer.querySelector(`[data-flash-id="${flashId}"]`)
            if (alert) {
              alert.remove()
            }
          }, 5000)
        }
      })
    }
  }

  handleFlash(event) {
    const { type, message } = event.detail
    const flashContainer = document.getElementById('flash')
    if (flashContainer) {
      const flashId = ++this.constructor.flashCounter
      const alertHtml = this.buildAlertHtml(type, message, flashId)
      // 在容器开头插入新的消息
      flashContainer.insertAdjacentHTML('afterbegin', alertHtml)
      
      // 5秒后自动移除消息
      setTimeout(() => {
        const alert = flashContainer.querySelector(`[data-flash-id="${flashId}"]`)
        if (alert) {
          alert.remove()
        }
      }, 5000)
    }
  }

  buildAlertHtml(type, message, flashId) {
    const colorClasses = this.getColorClasses(type)
    
    return `
      <div class="flash-message" data-type="${type}" role="alert" data-flash-id="${flashId}">
        <div class="flex items-center p-4 mb-4 ${colorClasses.container} rounded-lg">
          ${this.getIconSvg()}
          <span class="sr-only">${type.charAt(0).toUpperCase() + type.slice(1)}</span>
          <div class="ms-3 text-sm font-medium">${message}</div>
          <button type="button" 
                  class="ms-auto -mx-1.5 -my-1.5 ${colorClasses.button} rounded-lg focus:ring-2 p-1.5 inline-flex items-center justify-center h-8 w-8"
                  data-action="click->flash#dismiss">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
          </button>
        </div>
      </div>
    `
  }

  getColorClasses(type) {
    switch (type) {
      case 'notice':
        return {
          container: 'text-primary-800 bg-primary-50 dark:bg-gray-800 dark:text-primary-400',
          button: 'bg-primary-50 text-primary-500 hover:bg-primary-200 focus:ring-primary-400 dark:bg-gray-800 dark:text-primary-400 dark:hover:bg-gray-700'
        }
      case 'success':
        return {
          container: 'text-green-800 bg-green-50 dark:bg-gray-800 dark:text-green-400',
          button: 'bg-green-50 text-green-500 hover:bg-green-200 focus:ring-green-400 dark:bg-gray-800 dark:text-green-400 dark:hover:bg-gray-700'
        }
      case 'alert':
        return {
          container: 'text-red-800 bg-red-50 dark:bg-gray-800 dark:text-red-400',
          button: 'bg-red-50 text-red-500 hover:bg-red-200 focus:ring-red-400 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700'
        }
      default:
        return {
          container: 'text-gray-800 bg-gray-50 dark:bg-gray-800 dark:text-gray-400',
          button: 'bg-gray-50 text-gray-500 hover:bg-gray-200 focus:ring-gray-400 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700'
        }
    }
  }

  getIconSvg() {
    return `
      <svg class="shrink-0 w-4 h-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
      </svg>
    `
  }

  dismiss(event) {
    const button = event.currentTarget
    const alert = button.closest('[role="alert"]')
    if (alert) {
      alert.remove()
    }
  }
} 