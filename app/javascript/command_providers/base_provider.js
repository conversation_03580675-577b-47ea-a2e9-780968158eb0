// 命令提供者基类
export default class BaseCommandProvider {
  constructor(controller) {
    this.controller = controller
  }
  
  // 子类必须实现的方法
  getCommands(filter = "") {
    throw new Error("Command provider must implement getCommands method")
  }
  
  // 子类必须实现的方法
  executeCommand(command, options) {
    throw new Error("Command provider must implement executeCommand method")
  }
  
  // 共享工具方法
  dispatchResult(success, details = {}) {
    const event = new CustomEvent("command:result", {
      bubbles: true,
      detail: {
        success,
        ...details
      }
    })
    
    // 将事件分发到 document 上，避免 controller.element 被垃圾回收
    document.dispatchEvent(event)
  }
}
