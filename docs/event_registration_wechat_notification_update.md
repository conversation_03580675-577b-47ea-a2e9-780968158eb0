# 活动注册微信通知功能完善

## 概述

根据你提供的微信服务号模板信息，我已经完善了活动注册(event registration)的微信通知功能。新的模板使用了符合微信官方规范的字段格式。

## 更新的文件

### 1. 配置文件更新

#### `config/wechat_notification_templates.yml`
- 更新了活动注册模板配置
- 使用了你提供的真实模板ID：`uHM5RigbUMfoB3ad9dGKPLEP5E3h81SqJlMe4_gi5J8`
- 配置了新的模板字段格式：
  - `thing30`: 讲座名称
  - `time33`: 讲座时间
  - `thing14`: 上课地址
  - `amount49`: 金额
  - `character_string11`: 课程链接

### 2. 通知器更新

#### `app/notifiers/event_registration_notifier.rb`
- 更新了微信投递配置，使用动态模板ID
- 添加了新的通知方法：
  - `event_name()`: 获取活动名称
  - `event_time()`: 格式化活动时间
  - `event_location()`: 获取活动地点
  - `event_price()`: 格式化活动价格
  - `event_url()`: 获取活动链接
- 使用完整的URL而不是path

### 3. 投递方法更新

#### `lib/noticed/delivery_methods/wechat.rb`
- 重构了模板数据构建逻辑
- 使用新的服务类来管理模板数据
- 支持新的微信模板字段格式

### 4. 新增服务类

#### `app/services/wechat_notification_template_service.rb`
- 专门管理微信通知模板的配置和数据构建
- 提供了不同通知类型的数据构建方法
- 统一处理价格格式化和URL生成

### 5. 测试工具

#### `test/event_registration_wechat_notification_test.rb`
- 专门测试活动注册微信通知功能
- 包含完整的功能验证
- 提供使用示例

#### `lib/tasks/wechat_notification.rake`
- 添加了新的测试任务：`rails wechat:notification:test_event_registration`

## 模板字段映射

你提供的微信模板字段对应关系：

| 微信字段 | 字段含义 | 数据来源 |
|---------|---------|---------|
| `thing30` | 讲座名称 | `event.title` |
| `time33` | 讲座时间 | `event.start_time` (格式：2023年10月21日) |
| `thing14` | 上课地址 | `event.location.name` 或 `event.address` |
| `amount49` | 金额 | `event.price` (格式：398.8元 或 免费) |
| `character_string11` | 课程链接 | `event_url(event)` |

## 使用方法

### 在控制器中发送通知

```ruby
# 活动注册成功后发送通知
def create
  @event_registration = current_user.event_registrations.build(event_registration_params)
  
  if @event_registration.save
    # 发送微信通知给注册用户
    EventRegistrationNotifier.with(
      event: @event,
      record: @event_registration
    ).deliver(current_user)
    
    redirect_to @event, notice: '报名成功！您将收到微信通知。'
  else
    render :new
  end
end
```

### 测试功能

```bash
# 测试活动注册微信通知
rails wechat:notification:test_event_registration

# 测试基本微信通知功能
rails wechat:notification:test

# 查看配置信息
rails wechat:notification:config

# 发送测试通知
rails wechat:notification:send_test
```

## 环境变量配置

生产环境建议使用环境变量：

```bash
# .env 文件
WECHAT_EVENT_REGISTRATION_TEMPLATE_ID=uHM5RigbUMfoB3ad9dGKPLEP5E3h81SqJlMe4_gi5J8
```

## 注意事项

1. **模板ID**: 使用了你提供的真实模板ID `uHM5RigbUMfoB3ad9dGKPLEP5E3h81SqJlMe4_gi5J8`
2. **字段格式**: 严格按照微信模板要求格式化数据
3. **URL生成**: 使用完整的URL，确保在微信中可以正常访问
4. **价格处理**: 自动处理免费和付费活动的价格显示
5. **时间格式**: 使用中文友好的时间格式（如：2023年10月21日）

## 验证清单

- [x] 更新模板配置文件
- [x] 修改事件注册通知器
- [x] 更新微信投递方法
- [x] 创建模板服务类
- [x] 添加测试脚本
- [x] 更新任务管理
- [x] 创建说明文档

## 后续建议

1. 在真实环境中测试微信模板消息发送
2. 根据实际需要调整时间和价格的显示格式
3. 考虑添加错误重试机制
4. 监控微信API调用频率，避免超出限制

现在你的活动注册微信通知功能已经完全支持你提供的微信模板格式了！
