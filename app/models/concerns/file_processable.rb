module FileProcessable
  extend ActiveSupport::Concern

  # Allowed file types
  ALLOWED_TYPES = {
    "application/pdf" => "pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => "docx",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation" => "pptx",
    "text/plain" => "txt",
    "application/epub+zip" => "epub",
    "text/markdown" => "md",
    "image/jpeg" => "jpg",
    "image/png" => "png"
  }.freeze

  included do
    has_one_attached :file

    # Add file attributes
    attribute :file_type, :string
    attribute :file_size, :integer
    attribute :content_type, :string
    attribute :original_filename, :string
    attribute :checksum, :string

    # Callback to set file attributes must be before validation
    before_validation :set_file_attributes, if: :file_attached?

    # Add validations
    validates :file_size, presence: true, numericality: { greater_than: 0 }, if: :file_attached?
    validates :content_type, inclusion: { in: ALLOWED_TYPES.keys }, if: :file_attached?
    validate :validate_storage_limit, on: :create

    before_save :set_file_attributes
    after_create_commit :process_file
  end

  private

  def file_attached?
    file.attached?
  end

  def set_file_attributes
    return unless file_attached?

    self.content_type = file.content_type
    self.file_type = ALLOWED_TYPES[file.content_type]
    self.file_size = file.byte_size
    self.original_filename = file.filename.to_s
    self.checksum = file.checksum
  end

  def validate_storage_limit
    return unless file_attached?

    # Ensure file_size is set
    self.file_size ||= file.byte_size
    total_storage = user.storage_used + file_size

    if total_storage > user.storage_limit
      errors.add(:file, :storage_limit_exceeded)
      throw :abort
    end
  end

  def validate_file_type
    return unless file_attached?

    unless ALLOWED_TYPES.key?(file.content_type)
      errors.add(:file, "Unsupported file type. Supported types: PDF, DOCX, PPTX, TXT, EPUB, MD, JPG, PNG")
    end
  end

  def process_file
    return unless file_attached?

    # TODO Add basic file processing logic here
    # Such as generating thumbnails, compression, etc.
    # File name can include the uploading user's username

    # Update processing status
    update(
      status: "ready",
      processed_at: Time.current
    )
  end
end
