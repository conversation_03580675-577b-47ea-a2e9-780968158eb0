import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["visibilitySelect", "teamContainer", "teamSelect"]
  static values = { 
    required: Boolean,
    knowledgeBaseId: Number,
    updateUrl: String
  }

  connect() {
    this.toggleTeamVisibility()
    this.setupValidation()
    // 初始化时强制触发一次验证
    this.handleVisibilityChange()
  }

  // 统一处理可见性切换
  handleVisibilityChange() {
    this.toggleTeamVisibility()
    this.clearValidation()
  }

  // 切换团队可见性相关字段
  toggleTeamVisibility() {
    const isTeamVisible = this.visibilitySelectTarget.value === 'team_visibility'
    // 强制设置 display 属性而不是依赖 class
    this.teamContainerTarget.style.display = isTeamVisible ? 'block' : 'none'
    // 保持原有 required 状态设置
    const selectElement = this.teamSelectTarget.querySelector('select')
    selectElement.required = isTeamVisible
  }

  // 设置验证逻辑
  setupValidation() {
    this.teamSelectTarget.querySelector('select').addEventListener('change', () => {
      this.clearValidation()
    })
  }

  // 验证团队选择
  validateTeamSelection() {
    if (this.visibilitySelectTarget.value === 'team_visibility' &&
      this.teamSelectTarget.querySelector('select').value === '') {
      this.teamSelectTarget.querySelector('select').setCustomValidity('至少需要选择一个团队')
      return false
    }
    return true
  }

  // 清除验证信息
  clearValidation() {
    this.teamSelectTarget.querySelector('select').setCustomValidity('')
  }

  // 开始拖动
  dragStart(event) {
    event.dataTransfer.setData("text/plain", event.target.dataset.postId)
  }

  // 允许放置
  allowDrop(event) {
    event.preventDefault()
  }

  // 附加文章
  async attach(event) {
    event.preventDefault()
    const postId = event.dataTransfer.getData("text/plain")
    await this.updateAssociation(postId, "attach")
  }

  // 分离文章
  async detach(event) {
    event.preventDefault()
    const postId = event.dataTransfer.getData("text/plain")
    await this.updateAssociation(postId, "detach")
  }

  // 更新关联关系
  async updateAssociation(postId, actionType) {
    try {
      const response = await fetch(this.updateUrlValue, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({ post_id: postId, action_type: actionType })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      } else{
        location.reload()
      }
    } catch (error) {
      console.error("Error:", error)
    }
  }
} 