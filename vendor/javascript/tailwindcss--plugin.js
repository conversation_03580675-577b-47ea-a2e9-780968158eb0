// tailwindcss/plugin@3.4.17 downloaded from https://ga.jspm.io/npm:tailwindcss@3.4.17/plugin.js

var e={};Object.defineProperty(e,"__esModule",{value:true});Object.defineProperty(e,"default",{enumerable:true,get:function(){return t}});function createPlugin$1(e,t){return{handler:e,config:t}}createPlugin$1.withOptions=function(e,t=(()=>({}))){const optionsFunction=function(n){return{__options:n,handler:e(n),config:t(n)}};optionsFunction.__isOptionsFunction=true;optionsFunction.__pluginFunction=e;optionsFunction.__configFunction=t;return optionsFunction};const t=createPlugin$1;var n={};Object.defineProperty(n,"__esModule",{value:true});Object.defineProperty(n,"default",{enumerable:true,get:function(){return u}});const r=_interop_require_default(e);function _interop_require_default(e){return e&&e.__esModule?e:{default:e}}const u=r.default;var o={};let i=n;o=(i.__esModule?i:{default:i}).default;var l=o;export{l as default};

