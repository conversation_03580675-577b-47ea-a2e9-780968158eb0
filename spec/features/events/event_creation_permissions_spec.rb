require 'rails_helper'

RSpec.feature 'Event Creation Permissions', type: :feature do
  let(:regular_user) { create(:user) }
  let(:admin_user) { create(:user, :admin) }
  let(:team) { create(:team) }
  let(:team_admin) { create(:user) }
  let(:event_moderator) { create(:user) }

  before do
    # 设置团队管理员
    create(:team_member, user: team_admin, team: team, role: :admin)

    # 设置事件协作者
    team_member = create(:team_member, user: event_moderator, team: team)
    existing_event = create(:event)
    create(:team_resource_access,
           team_member: team_member,
           resource: existing_event,
           resource_kind: :event,
           role: :moderator)
  end

  describe 'Event creation access control' do
    scenario '管理员可以访问创建活动页面' do
      sign_in admin_user

      visit new_event_path

      expect(page).to have_content(I18n.t('events.new.title'))
      expect(page).to have_field('Title')
      expect(page).to have_field('Description')
    end

    scenario '团队管理员可以访问创建活动页面' do
      sign_in team_admin

      visit new_event_path

      expect(page).to have_content(I18n.t('events.new.title'))
      expect(page).to have_field('Title')
      expect(page).to have_field('Description')
    end

    scenario '事件协作者可以访问创建活动页面' do
      sign_in event_moderator

      visit new_event_path

      expect(page).to have_content(I18n.t('events.new.title'))
      expect(page).to have_field('Title')
      expect(page).to have_field('Description')
    end

    scenario '普通用户无法访问创建活动页面' do
      sign_in regular_user

      visit new_event_path

      # 应该被重定向到首页并显示错误消息
      expect(current_path).to eq(root_path)
      expect(page).to have_content('You are not authorized')
    end

    scenario '未登录用户无法访问创建活动页面' do
      visit new_event_path

      # 应该被重定向到登录页面或首页
      expect(current_path).not_to eq(new_event_path)
    end
  end

  describe 'UI权限控制' do
    scenario '管理员在导航中看到创建活动链接' do
      sign_in admin_user

      visit root_path

      # 检查导航中是否有创建活动的链接
      # 这取决于你的布局文件中的具体实现
      expect(page).to have_link(href: new_event_path) if page.has_css?('nav')
    end

    scenario '普通用户在导航中看不到创建活动链接' do
      sign_in regular_user

      visit root_path

      # 普通用户不应该看到创建活动的链接
      # 这需要在布局文件中添加 allowed_to?(:create?, Event) 检查
      expect(page).not_to have_link(href: new_event_path) if page.has_css?('nav')
    end

    scenario '管理员在活动索引页面看到创建活动按钮' do
      sign_in admin_user

      visit events_path

      # 管理员应该在活动列表页面看到创建活动按钮
      expect(page).to have_link(href: new_event_path)
      expect(page).to have_content(I18n.t('events.new.title'))
    end

    scenario '普通用户在活动索引页面看不到创建活动按钮' do
      sign_in regular_user

      visit events_path

      # 普通用户不应该在活动列表页面看到创建活动按钮
      expect(page).not_to have_link(href: new_event_path)
    end

    scenario '团队管理员在活动索引页面看到创建活动按钮' do
      sign_in team_admin

      visit events_path

      # 团队管理员应该在活动列表页面看到创建活动按钮
      expect(page).to have_link(href: new_event_path)
      expect(page).to have_content(I18n.t('events.new.title'))
    end
  end
end
