class User < ApplicationRecord
  attr_accessor :created_via_oauth_without_email # Flag to bypass email validation on OAuth creation

  before_destroy :cleanup_owned_knowledge_bases

  has_one_attached :avatar # Added for Active Storage avatar
  has_secure_password
  has_many :sessions, dependent: :destroy
  has_many :omni_auth_identities, dependent: :destroy
  has_many :subscriptions, dependent: :destroy
  has_many :team_members, dependent: :destroy
  has_many :teams, through: :team_members
  # 直接关联到 team_resource_accesses 以提供更便捷和高效的查询接口
  # 虽然可以通过 team_members 访问，但直接关联可以生成更优化的 SQL
  has_many :team_resource_accesses, through: :team_members
  has_many :posts, dependent: :destroy
  has_many :knowledge_bases, class_name: "KnowledgeBase", foreign_key: "owner_id", dependent: :destroy
  has_many :assistants, dependent: :destroy
  has_many :prompts, dependent: :destroy
  has_many :conversations, dependent: :destroy

  # 通过 team_resource_accesses 访问不同类型的资源
  has_many :accessible_knowledge_bases,
           -> { where(team_resource_accesses: { resource_type: "KnowledgeBase" }) },
           through: :team_resource_accesses,
           source: :resource,
           source_type: "KnowledgeBase"

  # TODO 需要考虑是否需要删除，或者 soft delete
  has_many :base_units, dependent: :destroy

  # Event related associations
  has_many :event_registrations, dependent: :destroy
  has_many :registered_events, through: :event_registrations, source: :event

  # Notification related associations
  has_many :notifications, as: :recipient, dependent: :destroy, class_name: "Noticed::Notification"

  normalizes :email_address, with: ->(e) { e.strip.downcase }
  normalizes :username, with: ->(u) { u.strip }
  # Normalize phone number to keep only '+' and digits (E.164 like)
  normalizes :phone_number, with: ->(pn) { pn.gsub(/[^+\d]/, "") if pn.present? }

  # Email is required unless created via OAuth without an email provided
  validates :email_address, presence: true, uniqueness: true, format: { with: URI::MailTo::EMAIL_REGEXP }, unless: :created_via_oauth_without_email?
  # Allow nil email if created via OAuth without email, but ensure uniqueness if present
  validates :email_address, uniqueness: true, allow_nil: true

  validates :password, on: [ :registration, :password_change ],
            presence: true,
            length: { minimum: 8, maximum: 72 }
  validates :phone_number, uniqueness: true, allow_nil: true, format: { with: /\A\+\d+\z/, message: "must be in E.164 format (+country_code...)" }
  validates :gender, inclusion: { in: %w[male female other], allow_nil: true }
  validates :username, presence: true,
                    uniqueness: true,
                    length: { minimum: 3 },
                    format: { with: /\A[a-zA-Z0-9_]+\z/,
                             message: "only allows letters, numbers and underscore" }

  # 从OAuth认证信息中提取用户属性并更新用户
  def self.assign_attributes_from_auth(auth, user)
    provider = auth["provider"] || auth.provider

    # ---- 1. 处理地理位置 ----
    if provider == "wechat"
      raw = (auth["extra"] && auth["extra"]["raw_info"]) || (auth.extra && auth.extra.raw_info) || {}
      location = [ raw["country"], raw["province"], raw["city"] ].compact.join(" ")
      user.location = location if location.present?
    else
      if auth.info.respond_to?(:location) && auth.info.location.present?
        user.location = auth.info.location
      elsif auth["info"] && auth["info"]["location"].present?
        user.location = auth["info"]["location"]
      end
    end

    # ---- 2. 处理性别 ----
    if provider == "wechat"
      raw = (auth["extra"] && auth["extra"]["raw_info"]) || (auth.extra && auth.extra.raw_info) || {}
      sex = raw["sex"] || raw[:sex]
      gender =
        case sex.to_s
        when "1" then "male"
        when "2" then "female"
        else "other"
        end
      user.gender = gender
    elsif auth.info.respond_to?(:gender) && auth.info.gender.present?
      user.gender = auth.info.gender.downcase
    elsif auth["info"] && auth["info"]["gender"].present?
      user.gender = auth["info"]["gender"].downcase
    end

    # ---- 3. 处理头像 ----
    if auth.info.present?
      if auth.info.image.present? && user.persisted?
        unless user.avatar.attached?
          FetchOauthAvatarJob.perform_later(user.id, auth.info.image)
        end
      end
    end

    # ---- 4. 其他可能的信息 ----
    if auth.extra.present? && auth.extra.raw_info.present?
      if auth.extra.raw_info.timezone.present?
        user.update_preferences({
          "timezone" => auth.extra.raw_info.timezone
        })
      end
    end
  end

  # 添加创建和更新用户的方法
  def self.create_from_oauth(auth)
    email = auth.info.email
    # Generate username: nickname > name > email prefix > provider_uid
    username = auth.info.nickname.presence ||
               auth.info.name&.parameterize(separator: "_").presence ||
               (email.present? ? email.split("@").first.gsub(/[^a-zA-Z0-9_]/, "_") : nil) ||
               "#{auth.provider}_#{auth.uid}" # Fallback using provider and uid

    # Ensure username uniqueness
    base_username = username.slice(0, 30) # Limit length
    username = base_username
    counter = 1
    while User.exists?(username: username)
      suffix = "_#{counter}"
      # Ensure the combined length doesn't exceed limits (adjust if needed)
      allowed_length = 30 - suffix.length
      username = "#{base_username.slice(0, allowed_length)}#{suffix}"
      counter += 1
      # Add a safeguard against infinite loops, though unlikely with provider_uid fallback
      break if counter > 100
    end

    user = self.new(
      username: username,
      # Only set email if provided by OAuth
      email_address: email.present? ? email : nil,
      # Set a secure random password as password_digest cannot be null
      password: SecureRandom.base64(64).truncate_bytes(72) # Max 72 bytes for bcrypt
    )

    # Mark if created via OAuth without email to bypass validation
    user.created_via_oauth_without_email = true if email.blank?

    # 从OAuth信息中提取并设置用户属性
    assign_attributes_from_auth(auth, user)

    # Use save! to raise an error on failure for easier debugging in controller
    user.save!
    user
  rescue ActiveRecord::RecordInvalid => e
    # Log the error or handle it as needed
    Rails.logger.error "Failed to create user from OAuth: #{e.message}, Record: #{e.record.errors.full_messages.join(", ")}"
    nil # Return nil on failure
  end

  def signed_in_with_oauth(auth)
    # 更新用户信息
    User.assign_attributes_from_auth(auth, self)

    # 只有当用户信息有变化时才保存
    # Save only if there are changes and the record is valid
    save if changed? && valid?
  end

  # OAuth related methods
  def has_oauth_provider?(provider)
    omni_auth_identities.exists?(provider: provider.to_s)
  end

  def can_unbind_oauth?(provider)
    # Allow unbinding if user has a password set OR has more than one OAuth identity
    has_password_set? || omni_auth_identities.count > 1
  end

  def has_password_set?
    password_digest.present?
  end

  # Check if the bypass flag is set
  def created_via_oauth_without_email?
    @created_via_oauth_without_email == true
  end

  # User preferences methods
  def preferred_language
    preferences&.dig("language") || "zh"
  end

  def preferred_theme
    preferences&.dig("theme") || "light"
  end

  def preferred_timezone
    preferences&.dig("timezone") || "Asia/Shanghai"
  end

  def preferred_currency
    preferences&.dig("currency") || "CNY"
  end

  def notification_settings
    preferences&.dig("notification") || {
      "email" => true,
      "web" => true,
      "service_wechat" => true
    }
  end

  # Check if user has enabled notifications for a specific type and channel
  def notification_enabled?(type, channel)
    return false unless notification_settings.dig(channel.to_s)

    # Check specific notification type preferences
    specific_setting = notification_settings.dig(type.to_s, channel.to_s)
    return specific_setting unless specific_setting.nil?

    # Fallback to general channel setting
    notification_settings.dig(channel.to_s) == true
  end

  # WeChat specific notification methods
  def wechat_notification_enabled?(type = nil)
    return false unless has_wechat_bound?

    if type
      notification_enabled?(type, :service_wechat)
    else
      notification_settings.dig("service_wechat") == true
    end
  end

  def has_wechat_bound?
    omni_auth_identities.exists?(provider: "wechat")
  end

  def wechat_openid
    omni_auth_identities.find_by(provider: "wechat")&.uid
  end

  def wechat_unionid
    omni_auth_identities.find_by(provider: "wechat")&.unionid
  end

  # Notification convenience methods
  def unread_notifications
    notifications.where(read_at: nil)
  end

  def unread_notifications_count
    unread_notifications.count
  end

  def mark_all_notifications_as_read!
    notifications.where(read_at: nil).update_all(read_at: Time.current)
  end

  # Update user preferences
  def update_preferences(new_preferences)
    current_preferences = preferences || {}
    self.preferences = current_preferences.merge(new_preferences)
    save
  end

  def hobbies=(value)
    super(Array(value))
  end

  def subscribed?
    subscriptions.active.exists?
  end

  def storage_limit
    subscribed? ? 10.gigabytes : 100.megabytes
  end

  def storage_used
    base_units.sum(:file_size)
  end

  def storage_available?
    storage_used < storage_limit
  end

  # 资源访问相关的便捷方法
  def viewable_knowledge_bases
    accessible_knowledge_bases.merge(TeamResourceAccess.where(role: :viewer))
  end

  def editable_knowledge_bases
    accessible_knowledge_bases.merge(TeamResourceAccess.where(role: :editor))
  end

  def maintainable_knowledge_bases
    accessible_knowledge_bases.merge(TeamResourceAccess.where(role: :maintainer))
  end

  # 管理员权限检查
  def admin?
    admin == true
  end

  # 设置为管理员
  def make_admin!
    update!(admin: true)
  end

  # 取消管理员权限
  def remove_admin!
    update!(admin: false)
  end

  # 切换管理员状态
  def toggle_admin!
    update!(admin: !admin?)
  end

  # 作用域
  scope :admins, -> { where(admin: true) }
  scope :non_admins, -> { where(admin: false) }

  private

  def cleanup_owned_knowledge_bases
    ActiveRecord::Base.transaction do
      # 先删除所有相关的 team_resource_accesses
      TeamResourceAccess.where(resource_type: "KnowledgeBase", resource_id: knowledge_base_ids).delete_all
      # 删除所有相关的 knowledge_base_posts
      KnowledgeBasePost.where(knowledge_base_id: knowledge_base_ids).delete_all
      # 再删除知识库
      knowledge_bases.delete_all
    end
  end
end
