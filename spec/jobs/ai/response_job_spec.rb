require 'rails_helper'

RSpec.describe Ai::ResponseJob, type: :job do
  include ActiveJob::TestHelper

  let(:user) { create(:user) }
  let(:assistant) { create(:assistant, user: user) }
  let(:conversation) { create(:conversation, assistant: assistant, user: user) }
  let(:user_message) { create(:message, :user, messageable: conversation) }
  let(:assistant_message) { create(:message, :assistant, messageable: conversation) }
  let(:user_message_id) { user_message.id }

  # Mock AI Services
  let(:chat_service_instance) { instance_double(Ai::ChatService) }
  let(:message_service_instance) { instance_double(Ai::MessageService) }

  before do
    # Stub the service instantiation and process calls
    allow(Ai::ChatService).to receive(:new).and_return(chat_service_instance)
    allow(Ai::MessageService).to receive(:new).and_return(message_service_instance)
    allow(chat_service_instance).to receive(:process)
    allow(message_service_instance).to receive(:process)
  end

  describe "#perform" do
    context "when messageable is a Conversation" do
      it "instantiates Ai::ChatService with correct arguments" do
        expect(Ai::ChatService).to receive(:new).with(
          messageable: conversation,
          user_message_id: user_message_id,
          assistant_message: assistant_message
        ).and_return(chat_service_instance)

        subject.perform(conversation, user_message_id, assistant_message)
      end

      it "calls process on the Ai::ChatService instance" do
        expect(chat_service_instance).to receive(:process)
        subject.perform(conversation, user_message_id, assistant_message)
      end
    end

    context "when messageable is not a Conversation (e.g., Assistant)" do
      # 使用 Assistant 作为 messageable 的例子
      let(:other_messageable) { assistant }
      let(:other_user_message) { create(:message, :user, messageable: other_messageable) }
      let(:other_assistant_message) { create(:message, :assistant, messageable: other_messageable) }
      let(:other_user_message_id) { other_user_message.id }

      # 在此上下文中阻止广播，以避免视图错误
      before do
        allow(Ai::MessageService).to receive(:broadcast)
      end

      it "instantiates Ai::MessageService with correct arguments" do
        expect(Ai::MessageService).to receive(:new).with(
          messageable: other_messageable,
          user_message_id: other_user_message_id,
          assistant_message: other_assistant_message
        ).and_return(message_service_instance)

        subject.perform(other_messageable, other_user_message_id, other_assistant_message)
      end

      it "calls process on the Ai::MessageService instance" do
        expect(message_service_instance).to receive(:process)
        subject.perform(other_messageable, other_user_message_id, other_assistant_message)
      end
    end

    context "error handling (triggering retry)" do
      let(:error) { StandardError.new("Service process error") }

      before do
        # 让 ChatService 的 process 抛出错误
        allow(chat_service_instance).to receive(:process).and_raise(error)
      end

      it "re-raises the error from the service to trigger ActiveJob retries" do
        expect {
          subject.perform(conversation, user_message_id, assistant_message)
        }.to raise_error(StandardError, "Service process error")
      end

      # 注意：直接测试 retry_on 和 discard_on 的行为比较复杂，
      # 通常在集成测试或手动测试中验证。
      # 这里我们只验证了 Job 会重新抛出异常，这是触发重试的前提。
    end
  end

  describe "Retry settings" do
    it "is configured to retry on StandardError" do
      # 检查 retry_on 是否被定义（基本检查）
      # 注意：精确检查参数（wait, attempts）比较困难，依赖 ActiveJob 内部实现
      expect(described_class.respond_to?(:retry_on)).to be true
      # 我们可以间接检查，比如确认它不是默认的不重试
      # expect(described_class.retry_on_exceptions).not_to be_empty
    end
  end

  describe "Discard settings" do
    it "is configured to discard on StandardError after retries" do
      # 检查 discard_on 是否被定义
      expect(described_class.respond_to?(:discard_on)).to be true
      # expect(described_class.discard_on_exceptions).not_to be_empty
    end
  end


  describe "Queue settings" do
    it "uses the :ai_processing queue" do
      expect(described_class.queue_name).to eq("ai_processing")
    end
  end
end
