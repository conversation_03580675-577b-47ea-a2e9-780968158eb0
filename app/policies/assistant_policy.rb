class AssistantPolicy < ApplicationPolicy
  # 前置检查优化
  pre_check :check_authenticated!

  # 定义作用域过滤规则
  relation_scope do |relation|
    return relation if user&.admin?  # Admin可以看到所有助手
    next relation.none unless user

    # 用户只能看到自己的助手
    relation.where(user_id: user.id)
          .distinct
  end

  def index?
    admin? || authenticated?
  end

  def create?
    admin? || authenticated?
  end

  def show?
    admin? || owner?
  end

  def update?
    admin? || owner?
  end

  def destroy?
    admin? || owner?
  end

  # 所有拥有检查方法
  protected

  def owner?
    record.user_id == user.id
  end

  # 扩展权限检查方法
  def allowed_to?(action, *args)
    return false if record.nil? && !%i[index create].include?(action)
    method_name = "#{action}?"
    return send(method_name) if respond_to?(method_name, true)
    super
  end
end
