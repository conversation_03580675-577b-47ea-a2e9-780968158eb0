vi:
  activerecord:
    models:
      user: "Người dùng"
      team: "Nhóm"
      team_member: "Thành viên nhóm"
      knowledge_base: "Cơ sở kiến thức"
      post: "<PERSON>à<PERSON> viết"
      assistant: "<PERSON>r<PERSON> lý"
      conversation: "<PERSON><PERSON><PERSON><PERSON> trò chuyện"
      message: "Tin nhắn"
      base_unit: "Đơn vị cơ bản"
      subscription: "Gói đăng ký"
    attributes:
      user:
        name: "<PERSON>ê<PERSON>"
        email: "<PERSON><PERSON>"
        password: "<PERSON>ậ<PERSON> khẩu"
        bio: "Tiểu sử"
        preferences: "T<PERSON><PERSON> chọn"
      team:
        name: "Tên"
        description: "Mô tả"
    errors:
      messages:
        blank: "không thể để trống"
        taken: "đã được sử dụng"
        invalid: "không hợp lệ"
        too_short: "quá <PERSON>n (tối thiểu %{count} ký tự)"
        too_long: "quá dài (tối đa %{count} ký tự)"
        confirmation: "không khớp với x<PERSON>"
