# SqliteSearch 模块文档

## 概述

`SqliteSearch` 是一个可重用的 Rails concern，为 ActiveRecord 模型提供基于 SQLite FTS5 的全文搜索功能。该模块支持中文搜索，并提供了多种搜索方法。

## 功能特性

- **全文搜索**：基于 SQLite FTS5 引擎
- **中文支持**：使用 unicode61 分词器
- **多种搜索模式**：
  - 基本搜索
  - 短语搜索
  - 字段特定搜索
  - 高级搜索（支持 AND/OR/NOT 操作符）
  - 带相关性评分的搜索
- **搜索建议**：自动完成功能
- **错误处理**：自动回退到 LIKE 搜索
- **索引管理**：自动维护搜索索引

## 使用方法

### 1. 创建 FTS5 虚拟表

```ruby
class CreateEventsFts5Table < ActiveRecord::Migration[8.0]
  def up
    create_virtual_table :events_fts, :fts5, [
      "title",
      "description", 
      "prerequisites",
      "what_to_bring",
      "what_included",
      "event_id UNINDEXED",        # 外键，不索引
      "tokenize='unicode61'"        # 使用支持中文的分词器
    ]
  end

  def down
    execute "DROP TABLE IF EXISTS events_fts;"
  end
end
```

### 2. 在模型中引入 concern

```ruby
class Event < ApplicationRecord
  include SqliteSearch
  
  # 配置搜索字段
  search_scope :title, :description, :prerequisites, :what_to_bring, :what_included
  
  # 配置分词器（可选，默认为 unicode61）
  search_tokenizer "unicode61"
end
```

### 3. 使用搜索方法

```ruby
# 基本搜索
Event.full_search("Ruby")

# 带相关性评分的搜索
Event.full_search_with_rank("JavaScript")

# 短语搜索（精确匹配）
Event.phrase_search("web development")

# 字段特定搜索
Event.search_in_field("title", "Python")

# 高级搜索（支持布尔操作符）
Event.advanced_search("Ruby AND Rails NOT Python")

# 搜索建议（自动完成）
Event.search_suggestions("prog", limit: 5)
```

### 4. 索引管理

```ruby
# 重建所有记录的索引
Event.rebuild_search_index

# 重建特定记录的索引
Event.rebuild_search_index(1, 2, 3)
```

## Rake 任务

创建 `lib/tasks/search_index.rake`：

```ruby
namespace :search_index do
  desc "Rebuild FTS5 search index for all events"
  task rebuild_events: :environment do
    puts "Rebuilding search index for Event model..."
    
    ActiveRecord::Base.connection.execute("DELETE FROM events_fts;")
    
    Event.find_each(batch_size: 100) do |event|
      event.send(:update_search_index)
      print "."
    end
    
    puts "\nDone! Rebuilt search index for #{Event.count} events."
  end
end
```

运行：`rails search_index:rebuild_events`

## 实现细节

### 表命名约定

- FTS5 虚拟表命名：`{table_name}_fts`
- 外键命名：`{model_name}_id`（如 `event_id`）

### 自动索引维护

- `after_save_commit`：创建或更新记录时自动更新索引
- `after_destroy_commit`：删除记录时自动删除索引

### 中文搜索优化

- 使用 `unicode61` 分词器
- 中文字符不添加通配符
- 英文字符自动添加通配符支持部分匹配

### 错误处理

所有搜索方法都包含错误处理，当 FTS5 搜索失败时自动回退到 LIKE 搜索。

## 性能优势

- FTS5 搜索比 LIKE 操作快 10-100 倍
- 支持相关性评分排序
- 索引更新自动异步处理

## 向后兼容

如果需要保持与现有代码的兼容性，可以创建别名：

```ruby
class << self
  alias_method :fts_search, :full_search_with_rank
  alias_method :fts_search_count, :full_search
end
```

## 注意事项

1. **SQLite 版本**：需要 SQLite 3.9.0+ 支持 FTS5
2. **索引大小**：FTS5 索引会占用额外的存储空间
3. **事务处理**：索引更新在事务提交后执行
4. **特殊字符**：搜索查询会自动清理特殊字符

## 故障排除

### 搜索没有返回结果

1. 检查索引是否已建立：
   ```sql
   SELECT COUNT(*) FROM events_fts;
   ```

2. 重建索引：
   ```bash
   rails search_index:rebuild_events
   ```

### 中文搜索不工作

确保使用 `unicode61` 分词器而不是 `porter ascii`。

### 性能问题

1. 检查索引覆盖率
2. 考虑批量重建索引
3. 优化搜索查询复杂度 