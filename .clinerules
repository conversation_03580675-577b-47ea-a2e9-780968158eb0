我的应用使用的是最新的Tailwind CSS v4，css 升级指南规则遵从下面规则

## 主要变更

### 1. 废弃的工具类替换
- bg-opacity-* -> bg-black/50 
- text-opacity-* -> text-black/50
- border-opacity-* -> border-black/50
- divide-opacity-* -> divide-black/50
- ring-opacity-* -> ring-black/50
- placeholder-opacity-* -> placeholder-black/50
- flex-shrink-* -> shrink-*
- flex-grow-* -> grow-*
- overflow-ellipsis -> text-ellipsis
- decoration-slice -> box-decoration-slice
- decoration-clone -> box-decoration-clone

### 2. 重命名的工具类
- shadow-sm -> shadow-xs
- shadow -> shadow-sm
- drop-shadow-sm -> drop-shadow-xs
- drop-shadow -> drop-shadow-sm
- blur-sm -> blur-xs
- blur -> blur-sm
- backdrop-blur-sm -> backdrop-blur-xs
- backdrop-blur -> backdrop-blur-sm
- rounded-sm -> rounded-xs
- rounded -> rounded-sm
- outline-none -> outline-hidden
- ring -> ring-3



### 3. 默认值变更
- border-* 和 divide-* 默认颜色从 gray-200 改为 currentColor
- ring 默认宽度从 3px 改为 1px
- ring 默认颜色从 blue-500 改为 currentColor

### 4. 变体堆叠顺序
- v3: 从右到左应用
- v4: 从左到右应用

示例:
```html
// v3
<ul class="py-4 first:*:pt-0 last:*:pb-0">

// v4
<ul class="py-4 *:first:pt-0 *:last:pb-0">
```
