require 'rails_helper'

RSpec.describe TeamMember, type: :model do
  describe 'associations' do
    it { should belong_to(:team) }
    it { should belong_to(:user) }
    it { should have_many(:team_resource_accesses).dependent(:destroy) }
    it { should have_many(:knowledge_base_accesses).conditions(resource_kind: 'knowledge_base').class_name('TeamResourceAccess') }
    it { should have_many(:knowledge_bases).through(:knowledge_base_accesses).source(:resource) }
  end

  describe 'validations' do
    subject { build(:team_member) }
    it { should validate_uniqueness_of(:user_id).scoped_to(:team_id) }
  end

  describe 'enums' do
    it {
      should define_enum_for(:role)
        .with_values(member: 0, admin: 1)
        .with_prefix(:team)
        .with_suffix(:role)
        .backed_by_column_of_type(:integer)
    }
  end

  describe 'scopes and methods' do
    let(:team) { create(:team) }
    let(:user) { create(:user) }
    let(:team_member) { create(:team_member, team: team, user: user) }
    let(:knowledge_base) { create(:knowledge_base) }

    context 'when checking knowledge base access' do
      describe '#knowledge_base_viewer?' do
        it 'returns true when member has viewer access' do
          create(:team_resource_access, :viewer, team_member: team_member, resource: knowledge_base)
          expect(team_member.knowledge_base_viewer?(knowledge_base)).to be true
        end

        it 'returns false when member has no viewer access' do
          expect(team_member.knowledge_base_viewer?(knowledge_base)).to be false
        end
      end

      describe '#knowledge_base_editor?' do
        it 'returns true when member has editor access' do
          create(:team_resource_access, :editor, team_member: team_member, resource: knowledge_base)
          expect(team_member.knowledge_base_editor?(knowledge_base)).to be true
        end

        it 'returns false when member has no editor access' do
          expect(team_member.knowledge_base_editor?(knowledge_base)).to be false
        end
      end

      describe '#knowledge_base_maintainer?' do
        it 'returns true when member has maintainer access' do
          create(:team_resource_access, :maintainer, team_member: team_member, resource: knowledge_base)
          expect(team_member.knowledge_base_maintainer?(knowledge_base)).to be true
        end

        it 'returns false when member has no maintainer access' do
          expect(team_member.knowledge_base_maintainer?(knowledge_base)).to be false
        end
      end
    end

    describe 'access management' do
      describe '#grant_knowledge_base_access' do
        it 'creates a new access record with correct attributes' do
          expect {
            access = team_member.grant_knowledge_base_access(knowledge_base, :editor)
            expect(access).to be_persisted
            expect(access.resource).to eq(knowledge_base)
            expect(access.role).to eq('editor')
            expect(access.resource_kind).to eq(TeamResourceAccess::RESOURCE_KINDS[:knowledge_base][:enum])
          }.to change(TeamResourceAccess, :count).by(1)
        end

        it 'raises error for invalid role' do
          expect {
            team_member.grant_knowledge_base_access(knowledge_base, :invalid_role)
          }.to raise_error(ArgumentError)
        end
      end

      describe '#revoke_knowledge_base_access' do
        let!(:access) { create(:team_resource_access, :viewer, team_member: team_member, resource: knowledge_base) }

        it 'removes the access record' do
          expect {
            team_member.revoke_knowledge_base_access(knowledge_base)
          }.to change(TeamResourceAccess, :count).by(-1)
        end

        it 'returns nil when no access exists' do
          another_kb = create(:knowledge_base)
          expect(team_member.revoke_knowledge_base_access(another_kb)).to be_nil
        end
      end
    end
  end
end
