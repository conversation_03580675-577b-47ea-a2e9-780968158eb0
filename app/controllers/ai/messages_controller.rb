module Ai
  class MessagesController < ApplicationController
    before_action :set_messageable

    def create
      authorize! @messageable

      # 调用服务处理消息创建和响应
      result = Ai::MessageService.create_and_process(
        messageable: @messageable,
        content: message_params[:content]
      )

      @user_message = result[:user_message]
      @assistant_message = result[:assistant_message]

      respond_to do |format|
        format.turbo_stream
        format.html { redirect_to polymorphic_path([ :ai, @messageable ]) }
      end
    end

    private

    def set_messageable
      messageable_class = message_params[:messageable_type].constantize
      # 验证可接受的 messageable 类型
      # 如果找不到记录或类型无效则重定向到会话列表
      @messageable = messageable_class.find(message_params[:messageable_id])
    rescue NameError, ActiveRecord::RecordNotFound
      redirect_to ai_conversations_path, alert: "Invalid messageable context"
    end

    def message_params
      params.require(:message).permit(:content, :messageable_type, :messageable_id)
    end
  end
end
