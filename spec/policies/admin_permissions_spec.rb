require 'rails_helper'

RSpec.describe "Admin Permissions", type: :policy do
  let(:admin_user) { create(:user, admin: true) }
  let(:regular_user) { create(:user, admin: false) }
  let(:other_user) { create(:user, admin: false) }

  describe "ApplicationPolicy admin methods" do
    let(:policy) { ApplicationPolicy.new(nil, user: admin_user) }

    it "recognizes admin users" do
      expect(policy.send(:admin?)).to be true
    end

    it "check_admin! allows admin users" do
      expect { policy.send(:check_admin!) }.to throw_symbol(:policy_fulfilled)
    end
  end

  describe "UserPolicy admin permissions" do
    context "when user is admin" do
      it "allows admin to view any user" do
        admin = create(:user, admin: true)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: admin)
        expect(policy).to be_allowed_to(:show?)
      end

      it "allows admin to update any user" do
        admin = create(:user, admin: true)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: admin)
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows admin to delete other users" do
        admin = create(:user, admin: true)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: admin)
        expect(policy).to be_allowed_to(:destroy?)
      end

      it "allows admin to toggle admin status of other users" do
        admin = create(:user, admin: true)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: admin)
        expect(policy).to be_allowed_to(:toggle_admin?)
        expect(policy).to be_allowed_to(:make_admin?)
        expect(policy).to be_allowed_to(:remove_admin?)
      end

      it "prevents admin from deleting themselves" do
        admin = create(:user, admin: true)
        self_policy = UserPolicy.new(admin, user: admin)
        expect(self_policy).not_to be_allowed_to(:destroy?)
      end

      it "prevents admin from changing their own admin status" do
        admin = create(:user, admin: true)
        self_policy = UserPolicy.new(admin, user: admin)
        expect(self_policy).not_to be_allowed_to(:toggle_admin?)
      end

      it "allows admin to see all users in scope" do
        admin = create(:user, admin: true)
        create_list(:user, 3)
        scope = UserPolicy.new(User, user: admin).apply_scope(User.all, type: :active_record_relation)
        expect(scope.count).to eq(User.count)
      end
    end

    context "when user is not admin" do
      it "denies regular user from viewing other users" do
        regular = create(:user, admin: false)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: regular)
        expect(policy).not_to be_allowed_to(:show?)
      end

      it "denies regular user from updating other users" do
        regular = create(:user, admin: false)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: regular)
        expect(policy).not_to be_allowed_to(:update?)
      end

      it "denies regular user from deleting other users" do
        regular = create(:user, admin: false)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: regular)
        expect(policy).not_to be_allowed_to(:destroy?)
      end

      it "denies regular user from toggling admin status" do
        regular = create(:user, admin: false)
        target_user = create(:user, admin: false)
        policy = UserPolicy.new(target_user, user: regular)
        expect(policy).not_to be_allowed_to(:toggle_admin?)
      end

      it "limits regular user scope to themselves only" do
        regular = create(:user, admin: false)
        create_list(:user, 3)
        scope = UserPolicy.new(User, user: regular).apply_scope(User.all, type: :active_record_relation)
        expect(scope.count).to eq(1)
        expect(scope.first).to eq(regular)
      end
    end
  end

  describe "EventPolicy admin permissions" do
    let(:event) { create(:event, user: other_user) }

    context "when user is admin" do
      let(:policy) { EventPolicy.new(event, user: admin_user) }

      it "allows admin to view any event" do
        expect(policy).to be_allowed_to(:show?)
      end

      it "allows admin to create events" do
        expect(policy).to be_allowed_to(:create?)
      end

      it "allows admin to update any event" do
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows admin to delete any event" do
        expect(policy).to be_allowed_to(:destroy?)
      end

      it "allows admin to manage registrations" do
        expect(policy).to be_allowed_to(:manage_registrations?)
      end

      it "allows admin to see all events in scope" do
        create_list(:event, 3)
        scope = EventPolicy.new(Event, user: admin_user).apply_scope(Event.all, type: :active_record_relation)
        expect(scope.count).to eq(Event.count)
      end
    end
  end

  describe "PostPolicy admin permissions" do
    let(:post) { create(:post, user: other_user) }

    context "when user is admin" do
      let(:policy) { PostPolicy.new(post, user: admin_user) }

      it "allows admin to create posts" do
        expect(policy).to be_allowed_to(:create?)
      end

      it "allows admin to update any post" do
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows admin to delete any post (even published)" do
        post.update!(status: :published)
        expect(policy).to be_allowed_to(:destroy?)
      end

      it "allows admin to see all posts in scope" do
        create_list(:post, 3, user: other_user)
        scope = PostPolicy.new(Post, user: admin_user).apply_scope(Post.all, type: :active_record_relation)
        expect(scope.count).to eq(Post.count)
      end
    end
  end

  describe "KnowledgeBasePolicy admin permissions" do
    let(:knowledge_base) { create(:knowledge_base, owner: other_user, visibility: :private_visibility) }

    context "when user is admin" do
      let(:policy) { KnowledgeBasePolicy.new(knowledge_base, user: admin_user) }

      it "allows admin to view any knowledge base" do
        expect(policy).to be_allowed_to(:show?)
      end

      it "allows admin to update any knowledge base" do
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows admin to manage any knowledge base" do
        expect(policy).to be_allowed_to(:manage?)
      end

      it "allows admin to configure teams for any knowledge base" do
        expect(policy).to be_allowed_to(:configure_team?)
      end

      it "allows admin to delete any knowledge base" do
        expect(policy).to be_allowed_to(:destroy?)
      end

      it "allows admin to transfer ownership" do
        expect(policy).to be_allowed_to(:transfer_ownership?)
      end

      it "allows admin to see all knowledge bases in scope" do
        create_list(:knowledge_base, 3, owner: other_user, visibility: :private_visibility)
        scope = KnowledgeBasePolicy.new(KnowledgeBase, user: admin_user).apply_scope(KnowledgeBase.all, type: :active_record_relation)
        expect(scope.count).to eq(KnowledgeBase.count)
      end
    end
  end

  describe "TeamPolicy admin permissions" do
    let(:team) { create(:team) }

    context "when user is admin" do
      let(:policy) { TeamPolicy.new(team, user: admin_user) }

      it "allows admin to create teams" do
        expect(policy).to be_allowed_to(:create?)
      end

      it "allows admin to update any team" do
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows admin to delete any team" do
        expect(policy).to be_allowed_to(:destroy?)
      end

      it "allows admin to see all teams in scope" do
        create_list(:team, 3)
        scope = TeamPolicy.new(Team, user: admin_user).apply_scope(Team.all, type: :active_record_relation)
        expect(scope.count).to eq(Team.count)
      end
    end
  end

  describe "AssistantPolicy admin permissions" do
    let(:assistant) { create(:assistant, user: other_user) }

    context "when user is admin" do
      let(:policy) { AssistantPolicy.new(assistant, user: admin_user) }

      it "allows admin to view any assistant" do
        expect(policy.show?).to be true
      end

      it "allows admin to update any assistant" do
        expect(policy.update?).to be true
      end

      it "allows admin to delete any assistant" do
        expect(policy.destroy?).to be true
      end

      it "allows admin to see all assistants in scope" do
        create_list(:assistant, 3, user: other_user)
        scope = AssistantPolicy.new(Assistant, user: admin_user).apply_scope(Assistant.all, type: :active_record_relation)
        expect(scope.count).to eq(Assistant.count)
      end
    end
  end

  describe "ConversationPolicy admin permissions" do
    let(:conversation) { create(:conversation, user: other_user) }

    context "when user is admin" do
      let(:policy) { ConversationPolicy.new(conversation, user: admin_user) }

      it "allows admin to view any conversation" do
        expect(policy).to be_allowed_to(:show?)
      end

      it "allows admin to update any conversation" do
        expect(policy).to be_allowed_to(:update?)
      end

      it "allows admin to delete any conversation" do
        expect(policy).to be_allowed_to(:destroy?)
      end

      it "allows admin to see all conversations in scope" do
        create_list(:conversation, 3, user: other_user)
        scope = ConversationPolicy.new(Conversation, user: admin_user).apply_scope(Conversation.all, type: :active_record_relation)
        expect(scope.count).to eq(Conversation.count)
      end
    end
  end
end
