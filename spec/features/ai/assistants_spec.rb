require "rails_helper"

RSpec.describe "AI Assistants Management", type: :feature do
  let(:user) { create(:user) }
  let(:assistant) { create(:assistant, user: user) }
  let(:tool_infos) { LangchainToolRegistry.new.available_tool_infos }

  before do
    sign_in(user)
  end

  describe "Listing assistants" do
    it "displays the assistants list" do
      assistant
      visit ai_assistants_path
      expect(page).to have_content(assistant.name)
      expect(page).to have_content(assistant.tool_choice)
    end

    it "shows empty state when no assistants" do
      visit ai_assistants_path
      expect(page).to have_content("No assistants found")
    end
  end

  describe "Creating a new assistant" do
    before { visit new_ai_assistant_path }

    it "creates assistant with valid data" do
      fill_in "Name", with: "Math Helper"
      fill_in "Instructions", with: "Help with math problems"
      select "Auto - Smart tool usage based on context", from: "Tool Usage Mode"
      check "Calculator"

      expect {
        click_button "Create Assistant"
      }.to change(Assistant, :count).by(1)

      expect(page).to have_current_path(ai_assistant_path(Assistant.last))
      expect(page).to have_content("Assistant created successfully")
    end

    it "shows errors with invalid data" do
      click_button "Create Assistant"
      expect(page).to have_content("Name can't be blank")
      expect(page).to have_content("Instructions can't be blank")
    end

    it "hides tools section when 'none' is selected" do
      select "None - Pure conversation without tools", from: "Tool Usage Mode"
      expect(page).to have_css("#tools-section", visible: false)
    end
  end

  describe "Editing an assistant" do
    before { visit edit_ai_assistant_path(assistant) }

    it "updates assistant with valid data" do
      fill_in "Name", with: "Updated Name"
      click_button "Update Assistant"

      expect(page).to have_current_path(ai_assistant_path(assistant))
      expect(page).to have_content("Assistant updated successfully")
      expect(assistant.reload.name).to eq("Updated Name")
    end

    it "clears tools when 'none' is selected" do
      assistant.update!(tools: [ "Langchain::Tool::Calculator" ])
      visit edit_ai_assistant_path(assistant)

      select "None - Pure conversation without tools", from: "Tool Usage Mode"
      click_button "Update Assistant"

      expect(assistant.reload.tools).to be_empty
    end
  end

  describe "Viewing an assistant" do
    it "shows assistant details" do
      assistant.update!(tools: [ "Langchain::Tool::Calculator" ])
      visit ai_assistant_path(assistant)

      expect(page).to have_content(assistant.name)
      expect(page).to have_content(assistant.instructions)
      expect(page).to have_content("Calculator")
    end
  end

  describe "Deleting an assistant" do
    it "deletes the assistant" do
      assistant
      visit ai_assistant_path(assistant)

      expect {
        accept_confirm do
          click_button "Delete"
        end
      }.to change(Assistant, :count).by(-1)

      expect(page).to have_current_path(ai_assistants_path)
      expect(page).to have_content("Assistant deleted successfully")
    end
  end

  describe "Tool selection", js: true do
    it "allows selecting all tools" do
      user = create(:user)
      sign_in(user)

      visit new_ai_assistant_path

      # 等待页面加载完成
      expect(page).to have_css("form", wait: 10)

      # 选择工具使用模式
      select "Any - Full access to all available tools", from: "assistant[tool_choice]"

      # 等待工具区域可见
      expect(page).to have_css("#tools-section", visible: true, wait: 10)

      # 等待并点击全选工具复选框
      expect(page).to have_css("#select_all_tools", wait: 10)
      find("#select_all_tools + div").click # 点击复选框的标签部分

      # 验证所有工具都被选中
      all("input[name='assistant[tools][]']").each do |checkbox|
        expect(checkbox).to be_checked
      end
    end
  end
end
