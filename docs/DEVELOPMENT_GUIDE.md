# 📚 AI Pro CGC 开发者指南

本文档提供 AI Pro CGC 项目的完整开发指南和技术文档索引，帮助新开发者快速上手。

## 🎯 新开发者快速路径

如果你是新加入的开发者，建议按以下顺序学习：

1. **📖 项目概述** - 阅读 [项目 README](../README.md) 了解项目背景
2. **🔧 环境搭建** - 跟随本文档的 [开发环境设置](#开发环境设置) 配置环境
3. **🏗️ 系统理解** - 学习 [系统架构](ARCHITECTURE.md) 了解整体设计
4. **🔌 API 使用** - 参考 [API 文档](API_DOCUMENTATION.md) 进行接口开发
5. **📋 功能开发** - 查看 [功能开发管理](features_development/README.md) 了解当前进度

## 📋 完整技术文档索引

### 🏗️ 系统设计
- **[系统架构](ARCHITECTURE.md)** - 完整的系统架构设计文档
- **[API 文档](API_DOCUMENTATION.md)** - REST API 接口规范

### 📚 功能文档
- **[功能开发管理](features_development/README.md)** - 详细的功能清单和开发状态
- **[RAG 系统说明](RAG.md)** - 检索增强生成系统的实现和优化
- **[活动系统开发计划](features_development/events_development_plan.md)** - 即将推出的活动管理功能

### 🔍 按主题快速查找

**🤖 AI 相关**: [RAG 系统](RAG.md) • [API - AI 助手](API_DOCUMENTATION.md#ai-助手-api) • [架构 - AI 系统](ARCHITECTURE.md#ai-系统架构)

**👤 用户管理**: [API - 用户管理](API_DOCUMENTATION.md#用户管理-api) • [认证流程](#代码架构)

**📚 知识库**: [功能 - 知识库](features_development/README.md#-knowledgebase) • [API - 知识库](API_DOCUMENTATION.md#知识库-api)

**📱 微信集成**: [API - 微信集成](API_DOCUMENTATION.md#微信集成-api) • [微信接口格式](../README.md#wechat-接口数据格式)

## 📖 目录

- [开发环境设置](#开发环境设置)
- [代码架构](#代码架构)
- [开发流程](#开发流程)
- [代码规范](#代码规范)
- [测试指南](#测试指南)
- [调试技巧](#调试技巧)
- [常见问题](#常见问题)
- [文档维护](#文档维护)
- [相关资源](#相关资源)

## 开发环境设置

### 系统要求

- macOS/Linux/Windows (推荐 WSL)
- Ruby 3.2+ (推荐使用 rbenv 管理版本)
- Node.js 18+ (推荐使用 nvm 管理版本)
- Git 2.0+

### 详细安装步骤

1. **安装 Ruby 环境**
   ```bash
   # 使用 rbenv 安装 Ruby
   rbenv install 3.2.0
   rbenv global 3.2.0
   
   # 验证安装
   ruby -v
   ```

2. **安装 Node.js**
   ```bash
   # 使用 nvm 安装 Node.js
   nvm install 18
   nvm use 18
   
   # 验证安装
   node -v
   npm -v
   ```

3. **克隆项目并安装依赖**
   ```bash
   git clone https://github.com/your-org/ai_pro_cgc.git
   cd ai_pro_cgc
   
   # 安装 Ruby 依赖
   bundle install
   
   # 安装前端依赖（如需要）
   npm install
   ```

4. **配置数据库**
   ```bash
   # 创建数据库
   rails db:create
   
   # 运行迁移
   rails db:migrate
   
   # 加载种子数据
   rails db:seed
   ```

5. **启动开发服务器**
   ```bash
   # 启动所有服务（推荐）
   bin/dev
   
   # 或分别启动
   rails server  # Rails 服务器
   ./bin/rails tailwindcss:watch  # Tailwind CSS 监听
   ```

### 配置凭据

项目使用 Rails 凭据系统管理敏感信息：

```bash
# 编辑凭据
EDITOR="code --wait" rails credentials:edit

# 或使用 vim
rails credentials:edit
```

必需的凭据配置：
```yaml
secret_key_base: your_secret_key
openai:
  api_key: your_openai_api_key
doubao:
  api_key: your_doubao_api_key
wechat:
  service:
    appid: your_wechat_appid
    secret: your_wechat_secret
  subscription:
    appid: your_subscription_appid
    secret: your_subscription_secret
aws:
  access_key_id: your_aws_access_key
  secret_access_key: your_aws_secret_key
```

## 代码架构

### MVC 架构概览

```
app/
├── controllers/          # 控制器层
│   ├── ai/              # AI 相关控制器
│   ├── wechat/          # 微信相关控制器
│   └── concerns/        # 控制器共享模块
├── models/              # 模型层
│   ├── concerns/        # 模型共享模块
│   └── ...
├── services/            # 服务对象
├── jobs/                # 后台任务
├── policies/            # 权限策略
└── views/               # 视图层
```

### 核心模型关系

```ruby
# 用户系统
User
├── has_many :sessions
├── has_many :omni_auth_identities
├── has_many :team_members
├── has_many :teams, through: :team_members
├── has_many :knowledge_bases
├── has_many :assistants
├── has_many :conversations
└── has_many :posts

# AI 系统
Assistant
├── belongs_to :user
├── has_many :conversations
└── has_many :messages

Conversation
├── belongs_to :user
├── belongs_to :assistant
└── has_many :messages

# 知识库系统
KnowledgeBase
├── belongs_to :owner (User)
├── has_many :team_resource_accesses
├── has_many :knowledge_base_posts
└── has_many :posts, through: :knowledge_base_posts
```

### 服务对象模式

项目使用服务对象来封装复杂的业务逻辑：

```ruby
# app/services/ai/chat_service.rb
class Ai::ChatService
  def initialize(conversation, user_message_content)
    @conversation = conversation
    @user_message_content = user_message_content
  end

  def call
    # 业务逻辑实现
  end
end
```

## 开发流程

### Git 工作流

1. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **开发和提交**
   ```bash
   # 添加文件
   git add .
   
   # 提交（使用有意义的提交信息）
   git commit -m "feat: add new AI assistant feature"
   ```

3. **推送并创建 PR**
   ```bash
   git push origin feature/new-feature
   ```

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 修复 bug
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 重构
- `test:` 测试相关
- `chore:` 构建/工具链相关

示例：
```
feat(ai): add custom tool integration for assistants

- Implement tool registry system
- Add support for custom Python tools
- Update assistant creation form

Closes #123
```

### 分支策略

- `main` - 主分支，对应生产环境
- `develop` - 开发分支，对应预发环境
- `feature/*` - 功能分支
- `hotfix/*` - 热修复分支

## 代码规范

### Ruby/Rails 规范

遵循 [RuboCop Rails Omakase](https://github.com/rails/rubocop-rails-omakase) 规范：

```bash
# 检查代码规范
rubocop

# 自动修复
rubocop -a
```

### 命名约定

- **文件名**: 使用 snake_case
- **类名**: 使用 PascalCase
- **方法名**: 使用 snake_case
- **常量**: 使用 SCREAMING_SNAKE_CASE
- **变量**: 使用 snake_case

### 代码组织

1. **控制器**
   ```ruby
   class PostsController < ApplicationController
     before_action :authenticate_user!
     before_action :set_post, only: [:show, :edit, :update, :destroy]

     def index
       # 实现逻辑
     end

     private

     def set_post
       @post = current_user.posts.find(params[:id])
     end

     def post_params
       params.require(:post).permit(:title, :content)
     end
   end
   ```

2. **模型**
   ```ruby
   class Post < ApplicationRecord
     # 关联
     belongs_to :user
     has_many :comments, dependent: :destroy

     # 验证
     validates :title, presence: true, length: { maximum: 255 }
     validates :content, presence: true

     # 范围
     scope :published, -> { where(published: true) }
     scope :recent, -> { order(created_at: :desc) }

     # 方法
     def excerpt(limit = 100)
       content.truncate(limit)
     end
   end
   ```

### 前端规范

1. **Stimulus 控制器**
   ```javascript
   // app/javascript/controllers/example_controller.js
   import { Controller } from "@hotwired/stimulus"

   export default class extends Controller {
     static targets = ["input", "output"]
     static values = { url: String }

     connect() {
       // 初始化逻辑
     }

     action() {
       // 动作逻辑
     }
   }
   ```

2. **CSS 规范**
   - 使用 Tailwind CSS 工具类
   - 组件级别的自定义样式放在 `app/assets/stylesheets/`
   - 遵循移动优先设计原则

## 测试指南

### 测试框架

- **RSpec** - 单元测试和集成测试
- **Cucumber** - 用户验收测试
- **Capybara** - 系统测试

### 运行测试

```bash
# 运行所有测试
bundle exec rspec

# 运行特定文件
bundle exec rspec spec/models/user_spec.rb

# 运行 Cucumber 测试
bundle exec cucumber

# 运行特定功能
bundle exec cucumber features/authentication.feature
```

### 编写测试

1. **模型测试**
   ```ruby
   RSpec.describe User, type: :model do
     describe "validations" do
       it { should validate_presence_of(:email_address) }
       it { should validate_uniqueness_of(:username) }
     end

     describe "associations" do
       it { should have_many(:posts) }
       it { should have_many(:assistants) }
     end
   end
   ```

2. **控制器测试**
   ```ruby
   RSpec.describe PostsController, type: :controller do
     let(:user) { create(:user) }
     let(:post) { create(:post, user: user) }

     before { sign_in user }

     describe "GET #index" do
       it "returns success response" do
         get :index
         expect(response).to be_successful
       end
     end
   end
   ```

### 测试数据

使用 FactoryBot 创建测试数据：

```ruby
# spec/factories/users.rb
FactoryBot.define do
  factory :user do
    username { Faker::Internet.unique.username }
    email_address { Faker::Internet.unique.email }
    password { "password123" }
  end
end
```

## 调试技巧

### 使用调试器

1. **debug gem**
   ```ruby
   # 在代码中添加断点
   debugger
   ```

2. **日志调试**
   ```ruby
   Rails.logger.info "Debug info: #{variable.inspect}"
   ```

3. **Rails 控制台**
   ```bash
   rails console
   
   # 或在特定环境下
   rails console production
   ```

### 性能调试

1. **查看慢查询**
   ```ruby
   # 在 development.rb 中启用
   config.active_record.verbose_query_logs = true
   ```

2. **内存使用**
   ```bash
   # 使用 memory_profiler gem
   gem 'memory_profiler', group: :development
   ```

### 前端调试

1. **浏览器开发工具**
   - 使用 Chrome DevTools
   - 监控网络请求
   - 检查 JavaScript 错误

2. **Stimulus 调试**
   ```javascript
   // 启用调试模式
   import { Application } from "@hotwired/stimulus"
   import { definitionsFromContext } from "@hotwired/stimulus-webpack-helpers"

   const application = Application.start()
   application.debug = true
   ```

## 常见问题

### Q: 如何添加新的 AI 工具？

A: 
1. 在 `lib/langchain/tools/` 下创建新工具类
2. 继承 `Langchain::Tool::Base`
3. 实现必要的方法
4. 在 `LangchainToolRegistry` 中注册

### Q: 如何修改微信菜单？

A: 
1. 编辑 `config/wechat_menus/menu_service.yml`
2. 运行 `rails wechat:create_service_menu`

### Q: 如何添加新的权限策略？

A: 
1. 在 `app/policies/` 下创建策略文件
2. 继承 `ApplicationPolicy`
3. 定义权限方法

### Q: 数据库迁移失败怎么办？

A: 
```bash
# 回滚最后一次迁移
rails db:rollback

# 回滚到特定版本
rails db:migrate:down VERSION=20240101000000

# 重置数据库（开发环境）
rails db:reset
```

### Q: 如何配置开发环境的 HTTPS？

A: 
使用 `rails credentials:edit` 配置证书，或使用 ngrok 等工具：
```bash
ngrok http 3000
```

### Q: 缓存问题如何解决？

A: 
```bash
# 清除 Rails 缓存
rails cache:clear

# 清除 Tailwind 缓存
./bin/rails tailwindcss:build
```

## 文档维护

### 📝 文档编写指南

**标准格式**:
- 使用 Markdown 格式
- 标题使用 `#` 层级结构
- 代码块使用 ` ```language ` 标记
- 链接使用相对路径

**内容要求**:
- 保持内容准确且最新
- 提供实际可运行的代码示例
- 包含必要的错误处理说明
- 注明重要的版本兼容性信息

### 🔄 更新流程

1. **修改文档**: 直接编辑对应的 Markdown 文件
2. **测试验证**: 确保代码示例可以正常运行
3. **提交变更**: 使用清晰的提交信息，如 `docs: update API authentication guide`
4. **代码审查**: 所有文档变更需要经过代码审查

### 📅 定期维护

- **每月检查**: 确保文档与最新代码一致
- **版本发布**: 更新相关的版本号和变更说明
- **链接验证**: 检查内外部链接的有效性

## 相关资源

### 📚 官方文档
- **[Rails 指南](https://guides.rubyonrails.org/)** - Rails 官方开发指南
- **[Ruby 文档](https://ruby-doc.org/)** - Ruby 语言官方文档
- **[Hotwire 官网](https://hotwired.dev/)** - Turbo 和 Stimulus 框架

### 🛠️ 工具文档
- **[Langchain.rb](https://github.com/patterns-ai-core/langchainrb)** - Ruby AI 开发框架
- **[Tailwind CSS v4](https://tailwindcss.com/docs)** - CSS 框架文档
- **[RSpec](https://rspec.info/)** - Ruby 测试框架
- **[Cucumber](https://cucumber.io/)** - BDD 测试工具

### 🤖 AI 开发资源
- **[OpenAI API](https://platform.openai.com/docs)** - OpenAI 接口文档
- **[豆包 API](https://www.volcengine.com/docs/82379)** - 豆包 AI 接口文档
- **[Qdrant](https://qdrant.tech/documentation/)** - 向量数据库文档

### 🔧 部署运维
- **[Kamal](https://kamal-deploy.org/)** - 容器化部署工具
- **[Docker](https://docs.docker.com/)** - 容器技术文档
- **[PostgreSQL](https://www.postgresql.org/docs/)** - 数据库文档

## 🆘 获取帮助

### 💬 内部支持
- **项目 Issues**: [GitHub Issues](https://github.com/your-org/ai_pro_cgc/issues)
- **讨论区**: [GitHub Discussions](https://github.com/your-org/ai_pro_cgc/discussions)
- **团队沟通**: 内部聊天群组

### 🏠 社区资源
- **Rails 社区**: [Rails Forum](https://discuss.rubyonrails.org/)
- **Ruby 社区**: [Ruby Forum](https://www.ruby-forum.com/)
- **Stack Overflow**: 搜索 `rails` + `具体问题`

### 📧 紧急联系
- **技术负责人**: 通过内部通讯工具联系
- **项目维护者**: 通过 GitHub 或邮件联系

---

💡 **提示**: 在寻求帮助前，请先查阅相关文档和搜索已知问题。描述问题时请提供：
- 错误信息的完整内容
- 重现问题的具体步骤
- 你的开发环境信息
- 你已经尝试过的解决方案 