class WechatMessageJob < ApplicationJob
  queue_as :default

  # 示例：接收消息内容和发送者 OpenID
  def perform(message_content:, from_user:)
    # 在这里执行异步任务
    # 例如：记录日志、调用 API、更新数据库等
    # 模拟耗时操作
    sleep(2)
    Rails.logger.info "[WechatMessageJob] Async task completed for user #{from_user} with message: #{message_content}"

    # 可以在这里通过 Wechat API 主动回复用户，如果需要的话
    # wechat_api = Wechat.api
    # wechat_api.custom_message_send Wechat::Message.to(from_user).text("你的异步任务已处理完成！")
  rescue => e
    Rails.logger.error "[WechatMessageJob] Failed to process message for #{from_user}: #{e.message}"
  end
end
