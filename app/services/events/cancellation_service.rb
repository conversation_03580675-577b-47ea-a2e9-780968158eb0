module Events
  class CancellationService < ApplicationService
    def initialize(registration)
      @registration = registration
    end

    def call
      return failure("Registration is already cancelled") if @registration.cancelled?

      ActiveRecord::Base.transaction do
        event = @registration.event
        user = @registration.user

        # 只更新状态为取消，不设置cancelled_at字段（字段不存在）
        @registration.update!(status: "cancelled")

        begin
          send_cancellation_notification(event, user)
        rescue => e
          Rails.logger.error "Failed to send cancellation notification: #{e.message}"
        end

        track_cancellation_event(event, user)
        success(@registration)
      end
    rescue StandardError => e
      Rails.logger.error "Registration cancellation failed: #{e.message}"
      failure("System error occurred during cancellation")
    end

    private

    def send_cancellation_notification(event, user)
      EventCancellationMailer.notification(user, event).deliver_later
      Rails.logger.info "Cancellation notification email queued for User #{user.id}, Event #{event.id}"
    end

    def track_cancellation_event(event, user)
      Rails.logger.info "Event registration cancelled: User #{user.id} cancelled registration for Event #{event.id}"
    end
  end
end
