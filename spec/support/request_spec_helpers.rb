# spec/support/request_spec_helpers.rb
module RequestSpecHelpers
  # Performs a login POST request for request specs.
  # Assumes user factory creates users with password 'password123'
  def login_as(user)
    post session_path, params: { email_address: user.email_address, password: "password123" }
    # Optional: Check for successful redirect or status if needed, but often
    # the subsequent request's behavior is enough confirmation.
    # expect(response).to redirect_to(root_path)
  end
end
