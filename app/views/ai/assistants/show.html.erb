<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="mb-4 col-span-full xl:mb-2">
        <%= render partial: 'shared/breadcrumb', locals: { items: [[t('ai.assistants.title'), ai_assistants_path], [@assistant.name, nil]] } %>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white"><%= @assistant.name %></h1>
            <span class="bg-blue-100 text-blue-800 text-xs font-medium ms-3 px-2.5 py-0.5 rounded-sm dark:bg-blue-900 dark:text-blue-300">
              <%= @assistant.tool_choice %>
            </span>
          </div>
          <div class="mt-4 md:mt-0 flex space-x-2">
            <%= link_to edit_ai_assistant_path(@assistant), class: "text-white bg-primary-700 hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800" do %>
              <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 18">
                <path d="M12.687 14.408a3.01 3.01 0 0 1-1.533.821l-3.566.713a3 3 0 0 1-3.53-3.53l.713-3.566a3.01 3.01 0 0 1 .821-1.533L10.905 2H2.167A2.169 2.169 0 0 0 0 4.167v11.666A2.169 2.169 0 0 0 2.167 18h11.666A2.169 2.169 0 0 0 16 15.833V11.1l-3.313 3.308Zm5.53-9.065.546-.546a2.518 2.518 0 0 0 0-3.56 2.576 2.576 0 0 0-3.559 0l-.547.547 3.56 3.56Z"/>
                <path d="M13.243 3.2 7.359 9.081a.5.5 0 0 0-.136.256L6.51 12.9a.5.5 0 0 0 .59.59l3.566-.713a.5.5 0 0 0 .255-.136L16.8 6.757 13.243 3.2Z"/>
              </svg>
                  <%= t('common.actions.edit') %>
            <% end %>
            <%= link_to ai_assistants_path, class: "text-gray-900 bg-white border border-gray-300 focus:outline-hidden hover:bg-gray-100 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700" do %>
              <svg class="w-4 h-4 me-2 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5H1m0 0 4 4M1 5l4-4"/>
              </svg>
              <%= t('common.actions.back') %>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 xl:gap-4">
      <div class="col-span-2">
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-xs dark:border-gray-700 sm:p-6 dark:bg-gray-800">
          <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white pb-2"><%= t('ai.assistants.show.info.title') %></h3>
          </div>

          <div class="space-y-6">
            <div>
              <h4 class="mb-2 text-sm font-medium text-gray-900 dark:text-white"><%= t('ai.assistants.fields.instructions') %></h4>
              <div class="p-4 bg-gray-50 rounded-lg border border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                <p class="text-gray-900 dark:text-white whitespace-pre-wrap">
                  <%= @assistant.instructions.presence || t('ai.assistants.show.info.no_instructions') %>
                </p>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 class="mb-2 text-sm font-medium text-gray-900 dark:text-white"><%= t('ai.assistants.show.info.tool_choice') %></h4>
                <div class="flex items-center">
                  <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-sm dark:bg-blue-900 dark:text-blue-300">
                    <%= @assistant.tool_choice %>
                  </span>
                </div>
              </div>

              <div>
                <h4 class="mb-2 text-sm font-medium text-gray-900 dark:text-white"><%= t('ai.assistants.show.info.available_tools') %></h4>
                <% if @assistant.tools.present? %>
                  <ul class="max-w-md space-y-1 text-gray-500 list-inside dark:text-gray-400">
                    <% @assistant.tools.each do |tool_name| %>
                      <li class="flex items-center">
                        <svg class="w-3.5 h-3.5 me-2 text-blue-500 dark:text-blue-400 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                        </svg>
                        <%= tool_name %>
                      </li>
                    <% end %>
                  </ul>
                <% else %>
                  <div class="flex items-center p-4 text-sm text-gray-800 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600" role="alert">
                    <svg class="shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                    </svg>
                    <span class="sr-only">Info</span>
                    <div><%= t('ai.assistants.show.info.no_tools') %></div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-span-1">
        <div class="p-4 mb-4 bg-white border border-gray-200 rounded-lg shadow-xs dark:border-gray-700 sm:p-6 dark:bg-gray-800">
          <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white pb-2"><%= t('ai.assistants.show.time_info.title') %></h3>
          </div>

          <div class="space-y-4">
            <div>
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('common.time_formats.created_at') %></h4>
              <p class="text-gray-900 dark:text-white">
                <%= @assistant.created_at.strftime("%Y-%m-%d %H:%M:%S") %>
              </p>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('common.time_formats.updated_at') %></h4>
              <p class="text-gray-900 dark:text-white">
                <%= @assistant.updated_at.strftime("%Y-%m-%d %H:%M:%S") %>
              </p>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('ai.assistants.show.time_info.conversations_count') %></h4>
              <p class="text-gray-900 dark:text-white">
                <%= @assistant.conversations.count %>
              </p>
            </div>

            <div>
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400"><%= t('ai.assistants.show.time_info.messages_count') %></h4>
              <p class="text-gray-900 dark:text-white">
                <%= @assistant.messages.count %>
              </p>
            </div>

            <!-- 删除按钮 -->
            <div class="pt-4 mt-4 border-t border-gray-200 dark:border-gray-700">
              <%= button_to ai_assistant_path(@assistant), method: :delete,
                  class: "w-full text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800",
                  data: { turbo_confirm: t('ai.assistants.show.delete_confirm') } do %>
                <svg class="w-4 h-4 me-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h16M7 8v8m4-8v8M7 1h4a1 1 0 0 1 1 1v3H6V2a1 1 0 0 1 1-1ZM3 5h12v13a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5Z"/>
                </svg>
                <%= t('common.actions.delete') %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
