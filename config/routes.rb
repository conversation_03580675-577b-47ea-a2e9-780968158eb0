Rails.application.routes.draw do
  mount ActionCable.server => '/cable'
  
  resource :session
  resources :passwords, param: :token

  # OmniAuth routes
  get "/auth/:provider/callback" => "sessions/omni_auths#create", as: :omniauth_callback
  get "/auth/failure" => "sessions/omni_auths#failure", as: :omniauth_failure
  delete "/auth/:provider/unbind" => "sessions/omni_auths#unbind", as: :unbind_oauth

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Defines the root path route ("/")
  root to: "static_pages#home"

  namespace :ai do
    resources :assistants
    resources :prompts, only: [ :index ]
    resources :conversations do
      member do
        post :switch_assistant
        post :clear_conversation # 清除对话 (删除消息+清缓存)
      end
      collection do
        get :latest
      end
    end
    resources :messages, only: [ :create ]
  end

  resources :notifications, only: [:index, :show, :update] do
    patch :mark_all, on: :collection, action: :mark_all_read
  end

  resources :users do
    collection do
      get :settings
      patch :update_preferences
      patch :update_profile # Add route for updating profile info
      patch :update_password # Add route for updating password
    end
  end

  # 注册相关路由
  resource :registration, only: [ :new, :create ] do
    collection do
      post :send_verification_code
      get :registration_user
    end
  end
  # TODO: Kept for potential future use or testing, currently not linked from registration flow.
  # get "registrations/preview_subscribe" => "registrations#preview_subscribe"
  get "sign_in" => "sessions#new", as: :sign_in
  get "sign_up" => "registrations#new", as: :sign_up
  delete "sign_out" => "sessions#destroy", as: :sign_out
  get "/privacy-policy", to: "static_pages#privacy_policy", as: :privacy_policy
  get "/terms", to: "static_pages#terms", as: :terms
  get "/about", to: "static_pages#about", as: :about

  resources :posts do
    member do
      patch :publish
      patch :unpublish
    end
  end

  # 将知识库路由移到 scope 外面
  # Events 路由
  resources :events do
    member do
      patch :publish
      patch :cancel
      get :registrations
      get :analytics
    end

    collection do
      get :upcoming
      get :past
      get :search
      get :search_suggestions
      get :map_data
      get :nearby_events
      get :search_locations
      get :calendar
      get :day_events
    end

    resources :event_registrations, path: "registrations", except: [ :show ] do
      member do
        patch :confirm
        patch :cancel
      end
    end
  end

  resources :event_categories, except: [ :show ]
  resources :event_locations, except: [ :show ]

  namespace :admin do
    # Administrate dashboards
    resources :users
    resources :teams
    resources :knowledge_bases
    resources :assistants
    resources :posts
    resources :events do
      member do
        get :participants
        post :send_notification
      end
    end

    # Set admin root
    root to: "users#index"
  end

  resources :knowledge_bases do
    member do
      patch :archive
      patch :unarchive
      get :teams, action: :manage_teams
      patch :update_post_association
      post :add_team
      delete "remove_team/:team_id", action: :remove_team, as: :remove_team
      post :create_base_unit
      post :create_post
      get :posts
    end

    collection do
      get :archived
      post :bulk_archive
      post :bulk_unarchive

      resources :base_units do
        member do
          get :download
          get :convert_to_md
        end
      end
      # TODO:上传文件
      # resources :base_units, only: [ :create ]
    end
  end

  # 订阅号微信回调路由
  # GET /wechat 用于验证 URL
  # POST /wechat 用于接收消息和事件
  resource :wechat, only: [ :show, :create ], controller: "wechat/subscription"

  # 服务号微信回调路由
  # GET /service_wechat 用于验证 URL
  # POST /service_wechat 用于接收消息和事件
  resource :service_wechat, only: [ :show, :create ], controller: "wechat/service"
end
